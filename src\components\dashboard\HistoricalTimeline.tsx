import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar,
  CheckCircle,
  Target,
  Award,
  Clock,
  DollarSign
} from 'lucide-react';

interface TimelineEvent {
  date: string;
  title: string;
  description: string;
  type: 'improvement' | 'milestone' | 'rate-change' | 'achievement';
  impact?: string;
  rateChange?: number;
  kpiChange?: {
    metric: string;
    from: number;
    to: number;
  };
}

const HistoricalTimeline: React.FC = () => {
  const timelineEvents: TimelineEvent[] = [
    {
      date: '2024-01-15',
      title: 'Rate Reduction Achieved',
      description: 'Interest rate reduced from 6.8% to 6.2% following DSO improvements',
      type: 'rate-change',
      rateChange: -0.6,
      impact: 'Saved 30,000 SEK annually'
    },
    {
      date: '2024-01-01',
      title: 'DSO Optimization Complete',
      description: 'Successfully reduced Days Sales Outstanding through automated invoicing',
      type: 'improvement',
      kpiChange: {
        metric: 'DSO',
        from: 45,
        to: 32
      }
    },
    {
      date: '2023-12-15',
      title: 'Top 30% Performance',
      description: 'Achieved top 30% ranking in Swedish fintech sector for working capital efficiency',
      type: 'achievement',
      impact: 'Industry recognition'
    },
    {
      date: '2023-12-01',
      title: 'EBITDA Margin Improvement',
      description: 'Increased EBITDA margin through operational efficiency initiatives',
      type: 'improvement',
      kpiChange: {
        metric: 'EBITDA Margin',
        from: 16.2,
        to: 18.5
      }
    },
    {
      date: '2023-11-15',
      title: 'FX Hedging Implementation',
      description: 'Implemented comprehensive FX hedging strategy covering 72% of exposure',
      type: 'milestone',
      impact: 'Reduced risk profile'
    },
    {
      date: '2023-11-01',
      title: 'Rate Reduction',
      description: 'Rate reduced from 7.1% to 6.8% due to improved liquidity metrics',
      type: 'rate-change',
      rateChange: -0.3,
      impact: 'Saved 15,000 SEK annually'
    },
    {
      date: '2023-10-15',
      title: 'Cash Flow Optimization',
      description: 'Enhanced cash flow forecasting accuracy to 95%+',
      type: 'improvement',
      kpiChange: {
        metric: 'Cash Visibility',
        from: 87,
        to: 96
      }
    },
    {
      date: '2023-10-01',
      title: 'Fortnox Integration Enhanced',
      description: 'Upgraded ERP integration for real-time financial data synchronization',
      type: 'milestone',
      impact: 'Improved data accuracy'
    }
  ];

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'improvement': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'milestone': return <Target className="h-4 w-4 text-blue-600" />;
      case 'rate-change': return <DollarSign className="h-4 w-4 text-purple-600" />;
      case 'achievement': return <Award className="h-4 w-4 text-yellow-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'improvement': return 'border-green-200 bg-green-50';
      case 'milestone': return 'border-blue-200 bg-blue-50';
      case 'rate-change': return 'border-purple-200 bg-purple-50';
      case 'achievement': return 'border-yellow-200 bg-yellow-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getEventBadgeVariant = (type: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (type) {
      case 'improvement': return 'default';
      case 'milestone': return 'secondary';
      case 'rate-change': return 'outline';
      case 'achievement': return 'default';
      default: return 'outline';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('sv-SE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const totalRateReduction = timelineEvents
    .filter(event => event.rateChange && event.rateChange < 0)
    .reduce((sum, event) => sum + Math.abs(event.rateChange!), 0);

  const totalSavings = timelineEvents
    .filter(event => event.impact && event.impact.includes('SEK'))
    .reduce((sum, event) => {
      const match = event.impact!.match(/(\d+,?\d*)/);
      return sum + (match ? parseInt(match[1].replace(',', '')) : 0);
    }, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calendar className="h-5 w-5 mr-2" />
          Performance Timeline
        </CardTitle>
        <p className="text-sm text-gray-600">
          Track your financial improvements and rate optimizations over time
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">-{totalRateReduction.toFixed(1)}%</div>
            <div className="text-sm text-gray-600">Total Rate Reduction</div>
          </div>
          
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{timelineEvents.length}</div>
            <div className="text-sm text-gray-600">Improvements Made</div>
          </div>
          
          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{totalSavings.toLocaleString('sv-SE')} SEK</div>
            <div className="text-sm text-gray-600">Annual Savings</div>
          </div>
        </div>

        {/* Timeline */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900">Recent Activity</h4>
          
          <div className="relative">
            {/* Timeline line */}
            <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
            
            <div className="space-y-6">
              {timelineEvents.map((event, index) => (
                <div key={index} className="relative flex items-start space-x-4">
                  {/* Timeline dot */}
                  <div className={`relative z-10 w-12 h-12 rounded-full border-2 ${getEventColor(event.type)} flex items-center justify-center flex-shrink-0`}>
                    {getEventIcon(event.type)}
                  </div>
                  
                  {/* Event content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h5 className="font-medium text-gray-900">{event.title}</h5>
                          <Badge variant={getEventBadgeVariant(event.type)}>
                            {event.type.replace('-', ' ')}
                          </Badge>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">{event.description}</p>
                        
                        {/* Event details */}
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>{formatDate(event.date)}</span>
                          
                          {event.impact && (
                            <span className="text-green-600 font-medium">{event.impact}</span>
                          )}
                          
                          {event.rateChange && (
                            <span className={`font-medium ${event.rateChange < 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {event.rateChange > 0 ? '+' : ''}{event.rateChange}% rate change
                            </span>
                          )}
                        </div>
                        
                        {/* KPI Change */}
                        {event.kpiChange && (
                          <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
                            <span className="font-medium">{event.kpiChange.metric}:</span>
                            <span className="ml-1">
                              {event.kpiChange.from} → {event.kpiChange.to}
                              {event.kpiChange.metric.includes('DSO') || event.kpiChange.metric.includes('Days') ? ' days' : 
                               event.kpiChange.metric.includes('Margin') || event.kpiChange.metric.includes('%') ? '%' : ''}
                            </span>
                            <span className="ml-2 text-green-600">
                              ({event.kpiChange.to > event.kpiChange.from ? '+' : ''}
                              {(event.kpiChange.to - event.kpiChange.from).toFixed(1)})
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Success Stories */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-900">Success Highlights</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">DSO Optimization Success</span>
              </div>
              <p className="text-sm text-green-700">
                Reduced DSO from 45 to 32 days through automated invoicing and payment incentives, 
                resulting in a 0.6% rate reduction.
              </p>
            </div>
            
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-2">
                <Award className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-800">Industry Recognition</span>
              </div>
              <p className="text-sm text-blue-700">
                Achieved top 30% performance ranking in Swedish fintech sector for 
                working capital efficiency and risk management.
              </p>
            </div>
          </div>
        </div>

        {/* Next Milestones */}
        <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-center space-x-2 mb-2">
            <Target className="h-5 w-5 text-yellow-600" />
            <span className="font-medium text-yellow-800">Upcoming Milestones</span>
          </div>
          <div className="text-sm text-yellow-700 space-y-1">
            <p>• Target: Achieve top 10% industry performance by Q2 2024</p>
            <p>• Goal: Reduce interest rate below 6.0% through EBITDA margin improvements</p>
            <p>• Objective: Implement advanced FX hedging to achieve 90% coverage</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default HistoricalTimeline;
