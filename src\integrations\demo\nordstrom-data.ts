/**
 * Demo Data for Nordström GreenTech AB
 * Realistic sample data for wind turbine component manufacturer
 */

import { supabase } from '@/integrations/supabase/client';
import { bas2025Accounts } from './bas-2025-accounts';

export interface DemoCompanyProfile {
  organization_number: string;
  company_name: string;
  industry: string;
  base_currency: string;
  annual_revenue: number;
  employee_count: number;
}

export interface DemoVoucherData {
  voucher_series: string;
  voucher_number: number;
  transaction_date: string;
  description: string;
  total_amount: number;
  currency_code: string;
  exchange_rate: number;
  rows: DemoVoucherRow[];
}

export interface DemoVoucherRow {
  account_code: string;
  account_name: string;
  debit_amount: number;
  credit_amount: number;
  currency_code: string;
  exchange_rate: number;
  description: string;
  cost_center?: string;
  project_code?: string;
}

// Nordström GreenTech AB Company Profile
export const nordstromProfile: DemoCompanyProfile = {
  organization_number: '556123-4567',
  company_name: 'Nordström GreenTech AB',
  industry: 'Wind Turbine Components Manufacturing',
  base_currency: 'SEK',
  annual_revenue: *********, // 150M SEK
  employee_count: 50
};

// Chart of Accounts for Manufacturing Company (BAS 2025 Compliant)
export const chartOfAccounts = bas2025Accounts.map(account => ({
  account_code: account.account_code,
  account_name: account.account_name,
  account_type: account.account_type,
  bas_category: account.bas_category,
  is_balance_sheet: account.is_balance_sheet,
  is_income_statement: account.is_income_statement,
  description: account.description
}));

// Exchange rates for demo (approximate rates)
export const exchangeRates = [
  { base_currency: 'SEK', target_currency: 'EUR', rate: 0.087, rate_date: '2024-01-15' },
  { base_currency: 'SEK', target_currency: 'USD', rate: 0.095, rate_date: '2024-01-15' },
  { base_currency: 'SEK', target_currency: 'NOK', rate: 1.05, rate_date: '2024-01-15' },
  { base_currency: 'SEK', target_currency: 'DKK', rate: 0.65, rate_date: '2024-01-15' },
  { base_currency: 'EUR', target_currency: 'SEK', rate: 11.5, rate_date: '2024-01-15' },
  { base_currency: 'USD', target_currency: 'SEK', rate: 10.5, rate_date: '2024-01-15' },
  { base_currency: 'NOK', target_currency: 'SEK', rate: 0.95, rate_date: '2024-01-15' },
  { base_currency: 'DKK', target_currency: 'SEK', rate: 1.54, rate_date: '2024-01-15' }
];

// Sample vouchers for realistic business scenarios
export const generateSampleVouchers = (companyId: string): DemoVoucherData[] => {
  const baseDate = new Date('2024-01-01');
  const vouchers: DemoVoucherData[] = [];

  // 1. Customer Invoice - Norwegian Wind Farm (NOK)
  vouchers.push({
    voucher_series: 'KF',
    voucher_number: 1001,
    transaction_date: '2024-01-15',
    description: 'Faktura till Norsk Vindkraft AS - Turbinkomponenter',
    total_amount: 2100000, // 2.1M NOK = ~2M SEK
    currency_code: 'NOK',
    exchange_rate: 0.95,
    rows: [
      {
        account_code: '1510',
        account_name: 'Kundfordringar',
        debit_amount: 2100000,
        credit_amount: 0,
        currency_code: 'NOK',
        exchange_rate: 0.95,
        description: 'Norsk Vindkraft AS'
      },
      {
        account_code: '3011',
        account_name: 'Försäljning EU',
        debit_amount: 0,
        credit_amount: 1680000,
        currency_code: 'NOK',
        exchange_rate: 0.95,
        description: 'Turbinkomponenter'
      },
      {
        account_code: '2440',
        account_name: 'Moms att betala',
        debit_amount: 0,
        credit_amount: 420000,
        currency_code: 'NOK',
        exchange_rate: 0.95,
        description: 'Utgående moms 25%'
      }
    ]
  });

  // 2. Supplier Invoice - German Steel (EUR)
  vouchers.push({
    voucher_series: 'LF',
    voucher_number: 2001,
    transaction_date: '2024-01-18',
    description: 'Inköp stål från ThyssenKrupp AG',
    total_amount: 87000, // 87k EUR = ~1M SEK
    currency_code: 'EUR',
    exchange_rate: 11.5,
    rows: [
      {
        account_code: '4011',
        account_name: 'Inköp stål (EUR)',
        debit_amount: 69600,
        credit_amount: 0,
        currency_code: 'EUR',
        exchange_rate: 11.5,
        description: 'Högkvalitativt stål för turbinkomponenter'
      },
      {
        account_code: '2440',
        account_name: 'Moms att betala',
        debit_amount: 17400,
        credit_amount: 0,
        currency_code: 'EUR',
        exchange_rate: 11.5,
        description: 'Ingående moms 25%'
      },
      {
        account_code: '2010',
        account_name: 'Leverantörsskulder',
        debit_amount: 0,
        credit_amount: 87000,
        currency_code: 'EUR',
        exchange_rate: 11.5,
        description: 'ThyssenKrupp AG'
      }
    ]
  });

  // 3. Payroll Transaction
  vouchers.push({
    voucher_series: 'LÖ',
    voucher_number: 3001,
    transaction_date: '2024-01-25',
    description: 'Löner januari 2024',
    total_amount: 2800000,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    rows: [
      {
        account_code: '5010',
        account_name: 'Löner',
        debit_amount: 2800000,
        credit_amount: 0,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Bruttolöner januari'
      },
      {
        account_code: '5020',
        account_name: 'Sociala avgifter',
        debit_amount: 868000,
        credit_amount: 0,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Arbetsgivaravgifter 31%'
      },
      {
        account_code: '2710',
        account_name: 'Personalskatt',
        debit_amount: 0,
        credit_amount: 840000,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Preliminärskatt och avgifter'
      },
      {
        account_code: '1930',
        account_name: 'Handelsbanken',
        debit_amount: 0,
        credit_amount: 2828000,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Nettolöner till anställda'
      }
    ]
  });

  // 4. US Customer Payment (USD)
  vouchers.push({
    voucher_series: 'IN',
    voucher_number: 4001,
    transaction_date: '2024-01-30',
    description: 'Betalning från American Wind Solutions Inc',
    total_amount: 190000, // 190k USD = ~2M SEK
    currency_code: 'USD',
    exchange_rate: 10.5,
    rows: [
      {
        account_code: '1930',
        account_name: 'Handelsbanken',
        debit_amount: 190000,
        credit_amount: 0,
        currency_code: 'USD',
        exchange_rate: 10.5,
        description: 'Betalning USD-konto'
      },
      {
        account_code: '1510',
        account_name: 'Kundfordringar',
        debit_amount: 0,
        credit_amount: 190000,
        currency_code: 'USD',
        exchange_rate: 10.5,
        description: 'American Wind Solutions Inc'
      }
    ]
  });

  // 5. Chinese Supplier - Composite Materials (USD)
  vouchers.push({
    voucher_series: 'LF',
    voucher_number: 2002,
    transaction_date: '2024-02-05',
    description: 'Inköp kompositmaterial från Sinocomp Ltd',
    total_amount: 75000, // 75k USD = ~787k SEK
    currency_code: 'USD',
    exchange_rate: 10.5,
    rows: [
      {
        account_code: '4012',
        account_name: 'Inköp kompositmaterial (USD)',
        debit_amount: 75000,
        credit_amount: 0,
        currency_code: 'USD',
        exchange_rate: 10.5,
        description: 'Kolfiberkomposit för turbinblad'
      },
      {
        account_code: '2010',
        account_name: 'Leverantörsskulder',
        debit_amount: 0,
        credit_amount: 75000,
        currency_code: 'USD',
        exchange_rate: 10.5,
        description: 'Sinocomp Ltd'
      }
    ]
  });

  return vouchers;
};

/**
 * Initialize comprehensive demo data for Nordström GreenTech AB
 * Includes BAS 2025 compliant chart of accounts and daily verifikationer
 */
export async function initializeDemoData(): Promise<string> {
  console.log('Starting demo data initialization...');

  try {
    // 1. Create company
    console.log('Creating company...');
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .insert({
        organization_number: nordstromProfile.organization_number,
        company_name: nordstromProfile.company_name,
        industry: nordstromProfile.industry,
        base_currency: nordstromProfile.base_currency,
        annual_revenue: nordstromProfile.annual_revenue,
        employee_count: nordstromProfile.employee_count,
        sync_status: 'completed'
      })
      .select()
      .single();

    if (companyError) {
      console.error('Company creation error:', companyError);
      throw new Error(`Failed to create company: ${companyError.message}`);
    }

    console.log('Company created successfully:', company.id);

    // 2. Insert exchange rates
    console.log('Inserting exchange rates...');
    const { error: ratesError } = await supabase
      .from('exchange_rates')
      .insert(exchangeRates);

    if (ratesError && !ratesError.message.includes('duplicate')) {
      console.error('Exchange rates error:', ratesError);
      throw new Error(`Failed to insert exchange rates: ${ratesError.message}`);
    }

    console.log('Exchange rates inserted successfully');

    // 3. Insert BAS 2025 compliant chart of accounts
    console.log('Inserting chart of accounts...');
    const accountsWithCompanyId = chartOfAccounts.map(account => ({
      ...account,
      company_id: company.id,
      is_active: true
    }));

    const { error: accountsError } = await supabase
      .from('chart_of_accounts')
      .insert(accountsWithCompanyId);

    if (accountsError) {
      console.error('Chart of accounts error:', accountsError);
      throw new Error(`Failed to insert chart of accounts: ${accountsError.message}`);
    }

    console.log(`Chart of accounts inserted successfully: ${accountsWithCompanyId.length} accounts`);

    // 4. Generate and insert comprehensive daily verifikationer
    console.log('Generating daily verifikationer...');
    await generateComprehensiveVerifikationer(company.id);

    console.log('Demo data initialized successfully for Nordström GreenTech AB with daily verifikationer');
    return company.id;

  } catch (error) {
    console.error('Error initializing demo data:', error);
    throw error;
  }
}

/**
 * Generate comprehensive daily verifikationer for the last 3 months
 */
async function generateComprehensiveVerifikationer(companyId: string): Promise<void> {
  try {
    console.log('Importing daily verifikationer generator...');
    const { generateDailyVerifikationer } = await import('./daily-verifikationer');

    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1); // Start with 1 month for testing

    let totalVerifikationer = 0;
    let totalErrors = 0;
    const currentDate = new Date(startDate);

    console.log(`Generating verifikationer from ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);

    while (currentDate <= endDate) {
      try {
        const dailyVerifikationer = generateDailyVerifikationer(currentDate, companyId);
        console.log(`Generated ${dailyVerifikationer.length} verifikationer for ${currentDate.toISOString().split('T')[0]}`);

        for (const voucherData of dailyVerifikationer) {
          try {
            // Insert voucher
            const { data: voucher, error: voucherError } = await supabase
              .from('vouchers')
              .insert({
                company_id: companyId,
                voucher_series: voucherData.voucher_series,
                voucher_number: voucherData.voucher_number,
                transaction_date: voucherData.transaction_date,
                description: voucherData.description,
                total_amount: voucherData.total_amount,
                currency_code: voucherData.currency_code,
                exchange_rate: voucherData.exchange_rate,
                sync_status: 'completed'
              })
              .select()
              .single();

            if (voucherError) {
              console.error(`Error inserting voucher ${voucherData.voucher_series}-${voucherData.voucher_number}:`, voucherError);
              totalErrors++;
              continue;
            }

            // Insert voucher rows
            const rowsWithVoucherId = voucherData.rows.map((row, index) => ({
              ...row,
              voucher_id: voucher.id,
              row_number: index + 1
            }));

            const { error: rowsError } = await supabase
              .from('voucher_rows')
              .insert(rowsWithVoucherId);

            if (rowsError) {
              console.error(`Error inserting voucher rows for ${voucherData.voucher_series}-${voucherData.voucher_number}:`, rowsError);
              totalErrors++;
              continue;
            }

            totalVerifikationer++;
          } catch (error) {
            console.error(`Error processing voucher ${voucherData.voucher_series}-${voucherData.voucher_number}:`, error);
            totalErrors++;
          }
        }
      } catch (error) {
        console.error(`Error generating verifikationer for ${currentDate.toISOString().split('T')[0]}:`, error);
        totalErrors++;
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log(`Verifikationer generation completed: ${totalVerifikationer} successful, ${totalErrors} errors`);

    if (totalVerifikationer === 0) {
      throw new Error('No verifikationer were successfully created');
    }
  } catch (error) {
    console.error('Error in generateComprehensiveVerifikationer:', error);
    throw error;
  }
}
