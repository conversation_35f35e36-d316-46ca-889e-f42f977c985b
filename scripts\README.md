# Database Seeding Script

This directory contains the standalone database seeding script for the Arcim B2B Fintech Platform.

## Overview

The `seed-database.ts` script pre-populates the Supabase database with comprehensive demo data for **Nordström GreenTech AB**, eliminating the need for runtime data generation in the webapp.

## What Gets Seeded

### 🏢 Company Data
- **Company**: Nordström GreenTech AB
- **Organization Number**: 556123-4567
- **Industry**: Wind turbine component manufacturing
- **Base Currency**: SEK
- **Annual Revenue**: 150M SEK
- **Employees**: 50

### 📊 Chart of Accounts
- **BAS 2025 compliant** Swedish chart of accounts
- **70+ accounts** covering all business operations
- Multi-currency support (SEK, EUR, USD, NOK, DKK)
- Manufacturing-specific accounts for wind turbine components

### 📈 Financial Data
- **Period**: January 1, 2023 to May 31, 2025 (2+ years)
- **Daily Verifikationer**: 300-600 entries per month
- **Total Expected**: ~8,700-17,400 verifikationer
- **Transaction Types**:
  - Customer invoices (40%)
  - Supplier invoices (30%)
  - Payroll entries (monthly)
  - Tax payments (monthly)
  - FX adjustments (weekly)
  - Other transactions (30%)

### 💱 Exchange Rates
- **Historical rates** for the entire period
- **Currency pairs**: EUR/SEK, USD/SEK, NOK/SEK, DKK/SEK
- **Weekly updates** with realistic fluctuations
- **Source**: ECB (European Central Bank)

## Usage

### Prerequisites

1. **Environment Variables**: Ensure your `.env` file contains:
   ```env
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. **Dependencies**: Install required packages:
   ```bash
   npm install
   ```

### Running the Script

#### Option 1: Using npm script (Recommended)
```bash
npm run seed-database
```

#### Option 2: Direct execution
```bash
npx tsx scripts/seed-database.ts
```

#### Option 3: Using Node.js with tsx
```bash
npx tsx scripts/seed-database.ts
```

### Expected Output

```
🚀 Starting database seeding for Arcim B2B Fintech Platform
📊 Target: 2+ years of comprehensive financial data
🏢 Company: Nordström GreenTech AB (556123-4567)
📅 Period: January 1, 2023 to May 31, 2025
📈 Expected: ~8,700-17,400 verifikationer total (300-600 per month)

🧹 Cleaning up existing data...
✅ Existing data cleaned up
🏢 Creating Nordström GreenTech AB...
✅ Company created: Nordström GreenTech AB [uuid]
📊 Inserting BAS 2025 chart of accounts...
✅ Chart of accounts inserted: 70 accounts
💱 Generating historical exchange rates...
✅ Historical exchange rates inserted: 2,184 rates
📈 Generating comprehensive verifikationer data...
⏳ This may take several minutes for 2+ years of data...
📅 Processing januari 2023...
✅ januari 2023: 487 verifikationer
📅 Processing februari 2023...
✅ februari 2023: 523 verifikationer
...
📊 Verifikationer generation summary:
✅ Total verifikationer inserted: 12,847
⚠️ Total errors: 23
📈 Average per month: 443 verifikationer

✅ Database seeding completed successfully!
🎯 The webapp will now load instantly with pre-populated data
📊 Navigate to /dashboard to view Treasury Management with live data
```

### Execution Time

- **Small dataset** (testing): ~2-5 minutes
- **Full dataset** (2+ years): ~10-20 minutes
- **Progress updates** every 1,000 verifikationer

## Post-Seeding

After successful seeding:

1. **Webapp loads instantly** - No runtime data generation
2. **Treasury Management** dashboard shows live data immediately
3. **All financial components** have access to comprehensive historical data
4. **Multi-currency operations** are fully supported with historical rates

## Troubleshooting

### Common Issues

1. **Missing environment variables**:
   ```
   ❌ Missing Supabase environment variables
   ```
   **Solution**: Check your `.env` file

2. **Network timeouts**:
   ```
   ❌ Database seeding failed: timeout
   ```
   **Solution**: Check internet connection and Supabase status

3. **Permission errors**:
   ```
   ❌ Company creation failed: insufficient privileges
   ```
   **Solution**: Verify Supabase API key permissions

### Re-running the Script

The script is **idempotent** - it can be run multiple times safely:
- Cleans up existing Nordström GreenTech AB data
- Recreates all data from scratch
- No duplicate entries or conflicts

### Partial Failures

If the script fails partway through:
- **Company and accounts**: Will be recreated on next run
- **Exchange rates**: Uses upsert (no duplicates)
- **Verifikationer**: Will be regenerated from the beginning

## Technical Details

### Database Tables Populated

- `companies` - Company information
- `chart_of_accounts` - BAS 2025 compliant accounts
- `exchange_rates` - Historical currency rates
- `vouchers` - Financial vouchers/verifikationer
- `voucher_rows` - Detailed voucher line items

### Data Generation Logic

- **Realistic amounts** based on wind turbine manufacturing
- **Multi-currency transactions** with proper exchange rates
- **Swedish VAT handling** (25% for domestic transactions)
- **BAS 2025 compliance** for all account codes
- **Proper double-entry bookkeeping** for all transactions

### Performance Optimizations

- **Batch processing** for large datasets
- **Progress reporting** every 1,000 entries
- **Error handling** with continuation on non-critical failures
- **Memory efficient** processing of large date ranges

## Integration with Webapp

After seeding, the webapp:
- **Removes all automatic data generation** from components
- **Assumes data exists** and loads immediately
- **Displays live data indicator** in Treasury Management
- **Provides instant access** to 2+ years of financial history

This approach ensures the webapp loads instantly without any initialization delays or runtime data generation.
