/**
 * Fortnox Data Synchronization Service
 * Handles syncing data from Fortnox API to Supabase database
 */

import { supabase } from '@/integrations/supabase/client';
import { fortnoxClient, FortnoxVoucher, FortnoxAccount } from './client';

export interface SyncProgress {
  stage: 'accounts' | 'vouchers' | 'files' | 'completed';
  current: number;
  total: number;
  message: string;
}

export interface SyncResult {
  success: boolean;
  accountsSynced: number;
  vouchersSynced: number;
  filesSynced: number;
  errors: string[];
  duration: number;
}

export class FortnoxSyncService {
  private companyId: string;
  private onProgress?: (progress: SyncProgress) => void;

  constructor(companyId: string, onProgress?: (progress: SyncProgress) => void) {
    this.companyId = companyId;
    this.onProgress = onProgress;
  }

  /**
   * Full synchronization of Fortnox data
   */
  async syncAll(fromDate?: string): Promise<SyncResult> {
    const startTime = Date.now();
    const result: SyncResult = {
      success: false,
      accountsSynced: 0,
      vouchersSynced: 0,
      filesSynced: 0,
      errors: [],
      duration: 0
    };

    try {
      // Update sync status
      await this.updateSyncStatus('syncing');

      // 1. Sync chart of accounts
      this.reportProgress('accounts', 0, 1, 'Syncing chart of accounts...');
      result.accountsSynced = await this.syncAccounts();
      this.reportProgress('accounts', 1, 1, `Synced ${result.accountsSynced} accounts`);

      // 2. Sync vouchers
      this.reportProgress('vouchers', 0, 1, 'Syncing vouchers...');
      result.vouchersSynced = await this.syncVouchers(fromDate);
      this.reportProgress('vouchers', 1, 1, `Synced ${result.vouchersSynced} vouchers`);

      // 3. Sync files (placeholder for future implementation)
      this.reportProgress('files', 0, 1, 'Syncing files...');
      result.filesSynced = 0; // TODO: Implement file sync
      this.reportProgress('files', 1, 1, `Synced ${result.filesSynced} files`);

      // Update sync completion
      await this.updateSyncStatus('completed');
      await this.updateLastSyncTime();

      result.success = true;
      this.reportProgress('completed', 1, 1, 'Synchronization completed successfully');

    } catch (error) {
      console.error('Sync error:', error);
      result.errors.push(error instanceof Error ? error.message : 'Unknown error');
      await this.updateSyncStatus('error');
    }

    result.duration = Date.now() - startTime;
    return result;
  }

  /**
   * Sync chart of accounts from Fortnox
   */
  private async syncAccounts(): Promise<number> {
    const response = await fortnoxClient.getAccounts();
    const accounts = response.Accounts || [];
    let syncedCount = 0;

    for (const account of accounts) {
      try {
        await supabase
          .from('chart_of_accounts')
          .upsert({
            company_id: this.companyId,
            account_code: account.Number,
            account_name: account.Description,
            account_type: this.mapAccountType(account.Type),
            is_active: account.Active,
            fortnox_account_id: account.Number,
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'company_id,account_code'
          });

        syncedCount++;
      } catch (error) {
        console.error(`Error syncing account ${account.Number}:`, error);
      }
    }

    return syncedCount;
  }

  /**
   * Sync vouchers from Fortnox
   */
  private async syncVouchers(fromDate?: string): Promise<number> {
    let syncedCount = 0;
    let currentPage = 1;
    const limit = 100; // Fortnox API limit

    // If no fromDate provided, sync last 30 days
    if (!fromDate) {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      fromDate = thirtyDaysAgo.toISOString().split('T')[0];
    }

    while (true) {
      try {
        const response = await fortnoxClient.getVouchers({
          page: currentPage,
          limit,
          fromDate
        });

        const vouchers = response.Vouchers || [];
        if (vouchers.length === 0) break;

        for (const voucher of vouchers) {
          try {
            await this.syncSingleVoucher(voucher);
            syncedCount++;
          } catch (error) {
            console.error(`Error syncing voucher ${voucher.VoucherSeries}-${voucher.VoucherNumber}:`, error);
          }
        }

        // Check if we have more pages
        const totalPages = response.MetaInformation?.['@TotalPages'] || 1;
        if (currentPage >= totalPages) break;

        currentPage++;
        
        // Rate limiting - wait 100ms between requests
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        console.error(`Error fetching vouchers page ${currentPage}:`, error);
        break;
      }
    }

    return syncedCount;
  }

  /**
   * Sync a single voucher with its rows
   */
  private async syncSingleVoucher(voucher: FortnoxVoucher): Promise<void> {
    // Calculate total amount
    const totalAmount = voucher.VoucherRows.reduce((sum, row) => {
      return sum + Math.max(row.Debit, row.Credit);
    }, 0);

    // Insert/update voucher
    const { data: voucherData, error: voucherError } = await supabase
      .from('vouchers')
      .upsert({
        company_id: this.companyId,
        voucher_series: voucher.VoucherSeries,
        voucher_number: voucher.VoucherNumber,
        transaction_date: voucher.TransactionDate,
        description: voucher.Description || '',
        total_amount: totalAmount,
        currency_code: 'SEK', // Default to SEK, can be enhanced later
        exchange_rate: 1.0,
        fortnox_voucher_id: `${voucher.VoucherSeries}-${voucher.VoucherNumber}`,
        fortnox_url: voucher['@url'],
        sync_status: 'completed',
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'company_id,voucher_series,voucher_number'
      })
      .select()
      .single();

    if (voucherError) throw voucherError;

    // Delete existing voucher rows
    await supabase
      .from('voucher_rows')
      .delete()
      .eq('voucher_id', voucherData.id);

    // Insert voucher rows
    const voucherRows = voucher.VoucherRows.map((row, index) => ({
      voucher_id: voucherData.id,
      account_code: row.Account,
      account_name: row.AccountName || '',
      debit_amount: row.Debit || 0,
      credit_amount: row.Credit || 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: row.Description || '',
      cost_center: row.CostCenter || null,
      project_code: row.Project || null,
      row_number: index + 1
    }));

    if (voucherRows.length > 0) {
      const { error: rowsError } = await supabase
        .from('voucher_rows')
        .insert(voucherRows);

      if (rowsError) throw rowsError;
    }
  }

  /**
   * Map Fortnox account type to our system
   */
  private mapAccountType(fortnoxType: string): string {
    const typeMap: Record<string, string> = {
      'ASSET': 'asset',
      'LIABILITY': 'liability',
      'EQUITY': 'equity',
      'REVENUE': 'revenue',
      'EXPENSE': 'expense',
      'COST': 'expense'
    };

    return typeMap[fortnoxType.toUpperCase()] || 'expense';
  }

  /**
   * Update company sync status
   */
  private async updateSyncStatus(status: string): Promise<void> {
    await supabase
      .from('companies')
      .update({ sync_status: status })
      .eq('id', this.companyId);
  }

  /**
   * Update last sync time
   */
  private async updateLastSyncTime(): Promise<void> {
    await supabase
      .from('companies')
      .update({ last_sync_at: new Date().toISOString() })
      .eq('id', this.companyId);
  }

  /**
   * Report progress to callback
   */
  private reportProgress(stage: SyncProgress['stage'], current: number, total: number, message: string): void {
    if (this.onProgress) {
      this.onProgress({ stage, current, total, message });
    }
  }
}

/**
 * Initialize sync for a company
 */
export async function initializeFortnoxSync(
  companyId: string,
  accessToken: string,
  refreshToken: string,
  expiresIn: number
): Promise<void> {
  // Store tokens in database
  await supabase
    .from('companies')
    .update({
      fortnox_access_token: accessToken,
      fortnox_refresh_token: refreshToken,
      fortnox_token_expires_at: new Date(Date.now() + expiresIn * 1000).toISOString(),
      sync_status: 'pending'
    })
    .eq('id', companyId);

  // Set tokens in client
  fortnoxClient.setTokens({
    access_token: accessToken,
    refresh_token: refreshToken,
    expires_in: expiresIn,
    token_type: 'Bearer'
  });
}

/**
 * Start incremental sync for a company
 */
export async function startIncrementalSync(companyId: string): Promise<SyncResult> {
  // Get last sync time
  const { data: company } = await supabase
    .from('companies')
    .select('last_sync_at, fortnox_access_token, fortnox_refresh_token')
    .eq('id', companyId)
    .single();

  if (!company?.fortnox_access_token) {
    throw new Error('Company not connected to Fortnox');
  }

  // Set tokens in client
  fortnoxClient.setTokens({
    access_token: company.fortnox_access_token,
    refresh_token: company.fortnox_refresh_token,
    expires_in: 3600, // Will be refreshed if needed
    token_type: 'Bearer'
  });

  // Determine from date
  const fromDate = company.last_sync_at 
    ? new Date(company.last_sync_at).toISOString().split('T')[0]
    : undefined;

  // Start sync
  const syncService = new FortnoxSyncService(companyId);
  return syncService.syncAll(fromDate);
}
