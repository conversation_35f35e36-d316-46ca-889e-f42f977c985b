import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Link } from 'react-router-dom';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import FortnoxSetupInfo from '@/components/fortnox/FortnoxSetupInfo';

const FortnoxSetup: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <section className="pt-36 pb-20 px-6">
        <div className="container-clean mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Fortnox Integration Setup
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Configure your Fortnox developer application to enable seamless integration with our platform.
              </p>
            </div>

            <FortnoxSetupInfo />

            <div className="text-center mt-8">
              <Link to="/register">
                <Button variant="outline" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Registration
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default FortnoxSetup;
