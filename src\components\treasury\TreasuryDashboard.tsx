import React, { useState, useEffect } from 'react';
import { DollarSign, TrendingDown, AlertTriangle, Banknote, MoreHorizontal, RefreshCw, Target, Activity } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ModuleCard from '@/components/dashboard/ModuleCard';
import MetricWidget from '@/components/dashboard/MetricWidget';
import ChartContainer from '@/components/dashboard/ChartContainer';
import CashFlowWaterfall from '@/components/treasury/CashFlowWaterfall';
import FXExposureChart from '@/components/treasury/FXExposureChart';
import { supabase } from '@/integrations/supabase/client';

interface TreasuryMetrics {
  cashPosition: number;
  monthlyBurn: number;
  runway: number;
  fxExposure: number;
  liquidityRatio: number;
  // Enhanced cash flow metrics
  operatingCashFlow: number;
  forecastAccuracy: number;
  cashFlowVariance: number;
}

const TreasuryDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<TreasuryMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTreasuryData();
  }, []);

  const loadTreasuryData = async () => {
    try {
      setLoading(true);

      // Get demo company
      const { data: companies } = await supabase
        .from('companies')
        .select('*')
        .eq('company_name', 'Nordström GreenTech AB')
        .limit(1);

      if (!companies || companies.length === 0) {
        // Use mock data if no company found
        setMetrics({
          cashPosition: 15750000,
          monthlyBurn: 850000,
          runway: 18.5,
          fxExposure: 2340000,
          liquidityRatio: 2.8,
          operatingCashFlow: 2100000,
          forecastAccuracy: 87.5,
          cashFlowVariance: 12.3
        });
        return;
      }

      const companyId = companies[0].id;

      // Import cash flow calculations
      const { calculateCashFlowMetrics, calculateLiquidityMetrics } = await import('@/utils/kpiCalculations');
      const [cashFlowMetrics, liquidityMetrics] = await Promise.all([
        calculateCashFlowMetrics(companyId),
        calculateLiquidityMetrics(companyId)
      ]);

      setMetrics({
        cashPosition: liquidityMetrics.cashPosition || 15750000,
        monthlyBurn: liquidityMetrics.cashBurnRate || 850000,
        runway: liquidityMetrics.daysLiquidityCoverage > 0 ? liquidityMetrics.daysLiquidityCoverage / 30 : 18.5,
        fxExposure: 2340000, // Would be calculated from FX positions
        liquidityRatio: 2.8, // Would be calculated from current assets/liabilities
        operatingCashFlow: cashFlowMetrics.operatingCashFlow || 2100000,
        forecastAccuracy: cashFlowMetrics.forecastAccuracy || 87.5,
        cashFlowVariance: cashFlowMetrics.cashFlowVariance || 12.3
      });

    } catch (error) {
      console.error('Error loading treasury data:', error);
      // Fallback to mock data
      setMetrics({
        cashPosition: 15750000,
        monthlyBurn: 850000,
        runway: 18.5,
        fxExposure: 2340000,
        liquidityRatio: 2.8,
        operatingCashFlow: 2100000,
        forecastAccuracy: 87.5,
        cashFlowVariance: 12.3
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Treasury Data</h3>
        <p className="text-gray-500 mb-4">Initialize demo data to see treasury insights</p>
        <Button onClick={loadTreasuryData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-display-sm">Treasury Management</h1>
          <p className="text-body-lg text-gray-600 mt-2">
            Real-time treasury insights and ERP data analytics for Nordström GreenTech AB
          </p>
          <div className="flex items-center mt-2">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            <span className="text-sm text-gray-500">
              Live data • 2+ years of financial history available
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="btn-secondary">
            Export Report
          </Button>
          <Button className="btn-primary">
            Cash Forecast
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Cash Position"
          value={metrics.cashPosition}
          format="currency"
          change={{
            value: -5.2,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={DollarSign}
        />

        <MetricWidget
          title="Monthly Burn Rate"
          value={metrics.monthlyBurn}
          format="currency"
          change={{
            value: 8.1,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={TrendingDown}
        />

        <MetricWidget
          title="Runway (Months)"
          value={metrics.runway}
          format="number"
          change={{
            value: -1.2,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={AlertTriangle}
        />

        <MetricWidget
          title="Operating Cash Flow"
          value={metrics.operatingCashFlow}
          format="currency"
          change={{
            value: 12.3,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={Activity}
        />
      </div>

      {/* Secondary Metrics - Cash Flow Forecasting */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Forecast Accuracy"
          value={metrics.forecastAccuracy}
          format="percentage"
          change={{
            value: 2.1,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={Target}
        />

        <MetricWidget
          title="Cash Flow Variance"
          value={metrics.cashFlowVariance}
          format="percentage"
          change={{
            value: -3.5,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={TrendingDown}
        />

        <MetricWidget
          title="FX Exposure"
          value={metrics.fxExposure}
          format="currency"
          change={{
            value: -8.2,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={Banknote}
        />

        <MetricWidget
          title="Liquidity Ratio"
          value={metrics.liquidityRatio}
          format="number"
          change={{
            value: 0.2,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={Activity}
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cash Flow Waterfall */}
        <div className="w-full">
          <ChartContainer
            title="Cash Flow Waterfall"
            description="Monthly cash flow breakdown"
            icon={DollarSign}
            headerActions={
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            }
          >
            <div className="w-full h-full">
              <CashFlowWaterfall />
            </div>
          </ChartContainer>
        </div>

        {/* FX Exposure */}
        <div className="w-full">
          <ChartContainer
            title="FX Exposure by Currency"
            description="Foreign exchange risk breakdown"
            icon={Banknote}
            headerActions={
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            }
          >
            <div className="w-full h-full">
              <FXExposureChart />
            </div>
          </ChartContainer>
        </div>
      </div>

      {/* Liquidity Analysis */}
      <ModuleCard
        title="Liquidity Analysis"
        description="Real-time liquidity monitoring and scenario planning"
        icon={DollarSign}
        headerActions={
          <Button variant="outline" size="sm" className="btn-secondary">
            Run Scenarios
          </Button>
        }
      >
        <div className="space-y-6">
          {/* Current Liquidity Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-success-50 border border-success-200 rounded-lg">
              <div className="text-heading-lg font-semibold text-success-700">Strong</div>
              <div className="text-body-sm text-success-600">Current Position</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="text-heading-lg font-semibold text-yellow-700">Monitor</div>
              <div className="text-body-sm text-yellow-600">3-Month Outlook</div>
            </div>
            <div className="text-center p-4 bg-error-50 border border-error-200 rounded-lg">
              <div className="text-heading-lg font-semibold text-error-700">Risk</div>
              <div className="text-body-sm text-error-600">6-Month Outlook</div>
            </div>
          </div>

          {/* Scenario Planning */}
          <div className="space-y-4">
            <h4 className="text-heading-sm">Scenario Analysis</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-body-md font-medium text-gray-900 mb-2">Base Case</div>
                <div className="text-body-sm text-gray-600 mb-3">Current burn rate continues</div>
                <div className="text-heading-md font-semibold text-gray-900">18.5 months</div>
                <div className="text-body-xs text-gray-500">runway remaining</div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-body-md font-medium text-gray-900 mb-2">Optimistic</div>
                <div className="text-body-sm text-gray-600 mb-3">20% revenue growth</div>
                <div className="text-heading-md font-semibold text-success-600">24.2 months</div>
                <div className="text-body-xs text-gray-500">runway remaining</div>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="text-body-md font-medium text-gray-900 mb-2">Pessimistic</div>
                <div className="text-body-sm text-gray-600 mb-3">Economic downturn</div>
                <div className="text-heading-md font-semibold text-error-600">12.8 months</div>
                <div className="text-body-xs text-gray-500">runway remaining</div>
              </div>
            </div>
          </div>

          {/* Recommendations */}
          <div className="space-y-3">
            <h4 className="text-heading-sm">Treasury Recommendations</h4>
            <ul className="space-y-2">
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Consider hedging EUR exposure (€1.2M) due to volatility
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Optimize cash allocation: move 30% to higher-yield instruments
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Set up automated cash sweeping to maximize interest income
                </span>
              </li>
            </ul>
          </div>
        </div>
      </ModuleCard>
    </div>
  );
};

export default TreasuryDashboard;
