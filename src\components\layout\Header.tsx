
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Menu, X, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [currentPath, setCurrentPath] = useState('/');

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    // Set current path
    setCurrentPath(window.location.pathname);

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const isActive = (path: string) => {
    return currentPath === path;
  };

  return (
    <header
      className={cn(
        "fixed top-0 w-full z-50 transition-all duration-300",
        scrolled
          ? "bg-white/95 backdrop-blur-sm border-b border-gray-200"
          : "bg-white/95 backdrop-blur-sm border-b border-gray-200"
      )}
    >
      <div className="container-clean">
        <div className="flex items-center justify-between h-16">
          <div className="text-heading-lg font-semibold text-gray-900">Arcim</div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <a href="#value-proposition" className="nav-link">Why Choose Us</a>
            <a href="#how-it-works" className="nav-link">How It Works</a>
            <a href="#benefits" className="nav-link">Benefits</a>
            <a href="/dashboard" className="nav-link">Demo</a>
            <a href="/contact" className="nav-link">Contact</a>
          </nav>

          <div className="flex items-center space-x-4">
            <Button variant="ghost" className="hidden md:inline-flex">
              Log in
            </Button>
            <Link to="/register">
              <Button className="btn-primary px-6 py-2">
                Get Started Now
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden text-gray-700 focus:outline-none"
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? (
              <X size={24} />
            ) : (
              <Menu size={24} />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden pt-5 pb-3 mt-4 border-t border-gray-100 animate-fade-in">
            <nav className="flex flex-col space-y-3">
              <a href="#value-proposition" className="text-gray-700 hover:text-primary transition-colors py-2">
                Why Choose Us
              </a>
              <a href="#how-it-works" className="text-gray-700 hover:text-primary transition-colors py-2">
                How It Works
              </a>
              <a href="#benefits" className="text-gray-700 hover:text-primary transition-colors py-2">
                Benefits
              </a>
              <a href="/dashboard" className="text-gray-700 hover:text-primary transition-colors py-2">
                Demo
              </a>
              <a href="/contact" className="text-gray-700 hover:text-primary transition-colors py-2">
                Contact
              </a>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
