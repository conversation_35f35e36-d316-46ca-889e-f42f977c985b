import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Building2,
  CreditCard,
  CheckCircle,
  ExternalLink,
  TrendingUp,
  BarChart3,
  Shield,
  Clock,
  ArrowRight,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface IntegrationOption {
  id: string;
  name: string;
  description: string;
  benefits: string[];
  icon: React.ComponentType<{ className?: string }>;
  status: 'available' | 'connected' | 'pending';
  isOptional: boolean;
}

interface IntegrationSetupProps {
  onComplete: (integrations: Record<string, boolean>) => void;
  className?: string;
}

const IntegrationSetup: React.FC<IntegrationSetupProps> = ({
  onComplete,
  className,
}) => {
  // Add error boundary state
  const [hasError, setHasError] = useState(false);

  // Error boundary effect
  React.useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('IntegrationSetup error:', error);
      setHasError(true);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  // If there's an error, show a fallback UI
  if (hasError) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Connect Your Financial Systems
          </h3>
          <p className="text-gray-600">
            Integrate with your existing tools to unlock powerful financial insights.
          </p>
          <div className="p-6 bg-yellow-50 rounded-lg border border-yellow-200">
            <p className="text-yellow-800">
              Integration setup is temporarily unavailable. You can continue and set up integrations later from your dashboard.
            </p>
          </div>
          <Button
            onClick={() => onComplete({})}
            className="w-full max-w-md"
          >
            Continue to Platform
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    );
  }
  const [integrations, setIntegrations] = useState<IntegrationOption[]>([
    {
      id: 'fortnox',
      name: 'Fortnox ERP',
      description: 'Connect your Fortnox accounting system for automated financial data sync and real-time insights.',
      benefits: [
        'Automated financial health monitoring',
        'Real-time KPI tracking and analytics',
        'Simplified regulatory reporting',
        'Enhanced lending decision accuracy',
        'Streamlined cash flow forecasting'
      ],
      icon: Building2,
      status: 'available',
      isOptional: true,
    },
    {
      id: 'open_banking',
      name: 'Open Banking',
      description: 'Connect your Swedish bank accounts for comprehensive financial analysis and cash flow insights.',
      benefits: [
        'Real-time cash flow analytics',
        'Automated transaction categorization',
        'Enhanced lending decisions',
        'Multi-bank account aggregation',
        'Advanced financial forecasting'
      ],
      icon: CreditCard,
      status: 'available',
      isOptional: true,
    },
  ]);

  const [isConnecting, setIsConnecting] = useState<string | null>(null);

  const handleFortnoxConnect = async () => {
    setIsConnecting('fortnox');

    try {
      // For demo purposes, simulate Fortnox OAuth flow
      // In production, this would use the actual Fortnox client
      const mockAuthUrl = `https://api.fortnox.se/oauth-v1/auth?client_id=1Y9FA35cHyB3&redirect_uri=https://arcim-fintech-nexus.lovableproject.com/auth/fortnox/callback&response_type=code&scope=salary bookkeeping archive connectfile article companyinformation settings invoice costcenter currency customer inbox payment noxfinansinvoice offer order price print project profile supplierinvoice supplier timereporting&state=onboarding-${Date.now()}`;

      // Open Fortnox OAuth in new window
      window.open(mockAuthUrl, 'fortnox-auth', 'width=600,height=700,scrollbars=yes,resizable=yes');

      // Listen for OAuth completion (in real implementation, use postMessage)
      // For now, simulate successful connection
      setTimeout(() => {
        setIntegrations(prev => prev.map(integration =>
          integration.id === 'fortnox'
            ? { ...integration, status: 'connected' as const }
            : integration
        ));
        setIsConnecting(null);
      }, 3000);

    } catch (error) {
      console.error('Fortnox connection failed:', error);
      setIsConnecting(null);
    }
  };

  const handleOpenBankingConnect = async () => {
    setIsConnecting('open_banking');

    try {
      // Simulate Open Banking connection (Tink/Nordigen integration)
      await new Promise(resolve => setTimeout(resolve, 2000));

      setIntegrations(prev => prev.map(integration =>
        integration.id === 'open_banking'
          ? { ...integration, status: 'connected' as const }
          : integration
      ));

    } catch (error) {
      console.error('Open Banking connection failed:', error);
    } finally {
      setIsConnecting(null);
    }
  };

  const handleConnect = (integrationId: string) => {
    switch (integrationId) {
      case 'fortnox':
        handleFortnoxConnect();
        break;
      case 'open_banking':
        handleOpenBankingConnect();
        break;
    }
  };

  const handleSkip = (integrationId: string) => {
    // Mark as skipped but available for later setup
    console.log(`Skipped ${integrationId} integration`);
  };

  const handleComplete = () => {
    const integrationStatus = integrations.reduce((acc, integration) => {
      acc[integration.id] = integration.status === 'connected';
      return acc;
    }, {} as Record<string, boolean>);

    onComplete(integrationStatus);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const connectedCount = integrations.filter(i => i.status === 'connected').length;

  try {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold text-gray-900">
            Connect Your Financial Systems
          </h3>
          <p className="text-gray-600">
            Integrate with your existing tools to unlock powerful financial insights and streamlined operations.
          </p>
          <Badge variant="outline" className="text-blue-600 border-blue-200">
            Optional - You can set these up later
          </Badge>
        </div>

      <div className="grid gap-6">
        {integrations.map((integration) => {
          const Icon = integration.icon;
          const isConnecting = isConnecting === integration.id;

          return (
            <Card key={integration.id} className="relative overflow-hidden">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{integration.name}</h4>
                      <p className="text-sm text-gray-600 font-normal">
                        {integration.description}
                      </p>
                    </div>
                  </div>
                  <Badge className={getStatusColor(integration.status)}>
                    {integration.status === 'connected' ? 'Connected' : 'Available'}
                  </Badge>
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-4">
                <div>
                  <h5 className="font-medium text-gray-900 mb-2 flex items-center">
                    <Zap className="h-4 w-4 mr-1 text-yellow-500" />
                    Key Benefits
                  </h5>
                  <ul className="space-y-1">
                    {integration.benefits.map((benefit, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  {integration.status === 'connected' ? (
                    <div className="flex items-center text-green-600">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      <span className="text-sm font-medium">Successfully connected</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={() => handleConnect(integration.id)}
                        disabled={isConnecting !== null}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {isConnecting ? (
                          <>
                            <Clock className="h-4 w-4 mr-2 animate-spin" />
                            Connecting...
                          </>
                        ) : (
                          <>
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Connect {integration.name}
                          </>
                        )}
                      </Button>

                      <Button
                        variant="ghost"
                        onClick={() => handleSkip(integration.id)}
                        disabled={isConnecting !== null}
                        className="text-gray-600"
                      >
                        Skip for now
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="text-center space-y-4 pt-6 border-t">
        <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center">
            <TrendingUp className="h-4 w-4 mr-1 text-blue-500" />
            <span>Enhanced Analytics</span>
          </div>
          <div className="flex items-center">
            <BarChart3 className="h-4 w-4 mr-1 text-green-500" />
            <span>Real-time Insights</span>
          </div>
          <div className="flex items-center">
            <Shield className="h-4 w-4 mr-1 text-purple-500" />
            <span>Secure Integration</span>
          </div>
        </div>

        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            {connectedCount > 0
              ? `Great! You've connected ${connectedCount} integration${connectedCount > 1 ? 's' : ''}. You can always add more later.`
              : 'No worries! You can set up these integrations anytime from your dashboard.'
            }
          </p>

          <Button onClick={handleComplete} className="w-full max-w-md">
            Continue to Platform
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
    );
  } catch (error) {
    console.error('IntegrationSetup render error:', error);
    return (
      <div className={cn("space-y-6", className)}>
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Connect Your Financial Systems
          </h3>
          <div className="p-6 bg-yellow-50 rounded-lg border border-yellow-200">
            <p className="text-yellow-800">
              Integration setup encountered an error. You can continue and set up integrations later from your dashboard.
            </p>
          </div>
          <Button
            onClick={() => onComplete({})}
            className="w-full max-w-md"
          >
            Continue to Platform
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    );
  }
};

export default IntegrationSetup;
