/**
 * Financial Overview Dashboard Component
 * Real-time financial insights with comprehensive liquidity and cash position KPIs
 */

import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Globe,
  AlertTriangle,
  RefreshCw,
  Calendar,
  Building2,
  Droplets,
  Clock,
  Target,
  Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import MetricWidget from '@/components/dashboard/MetricWidget';

interface FinancialMetrics {
  // Existing metrics
  cashPosition: number;
  monthlyRevenue: number;
  monthlyExpenses: number;
  fxExposure: {
    EUR: number;
    USD: number;
    NOK: number;
    DKK: number;
  };
  recentTransactions: Transaction[];
  currencyBreakdown: CurrencyData[];
  cashFlowTrend: CashFlowData[];

  // New Liquidity & Cash Position KPIs
  cashVisibilityPercentage: number;
  daysLiquidityCoverage: number;
  cashBurnRate: number;
  fundingBuffer: number;
  totalLiquidity: number;
  cashEquivalents: number;
}

interface Transaction {
  id: string;
  date: string;
  description: string;
  amount: number;
  currency: string;
  type: 'income' | 'expense';
}

interface CurrencyData {
  currency: string;
  amount: number;
  percentage: number;
  color: string;
}

interface CashFlowData {
  date: string;
  income: number;
  expenses: number;
  net: number;
}

const CURRENCY_COLORS = {
  SEK: '#3b82f6',
  EUR: '#10b981',
  USD: '#f59e0b',
  NOK: '#8b5cf6',
  DKK: '#ef4444'
};

const FinancialOverview: React.FC = () => {
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [selectedCompany, setSelectedCompany] = useState<string>('');

  useEffect(() => {
    loadFinancialData();

    // Set up real-time subscription
    const subscription = supabase
      .channel('financial-updates')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'vouchers' },
        () => loadFinancialData()
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [selectedCompany]);

  const loadFinancialData = async () => {
    try {
      setLoading(true);

      // Get demo company (Nordström GreenTech AB)
      const { data: companies } = await supabase
        .from('companies')
        .select('*')
        .eq('company_name', 'Nordström GreenTech AB')
        .limit(1);

      if (!companies || companies.length === 0) {
        console.log('No demo company found');
        return;
      }

      const companyId = companies[0].id;
      setSelectedCompany(companyId);

      // Get recent vouchers with rows
      const { data: vouchers } = await supabase
        .from('vouchers')
        .select(`
          *,
          voucher_rows (*)
        `)
        .eq('company_id', companyId)
        .order('transaction_date', { ascending: false })
        .limit(50);

      if (!vouchers) return;

      // Calculate metrics
      const calculatedMetrics = await calculateMetrics(vouchers);
      setMetrics(calculatedMetrics);
      setLastUpdated(new Date());

    } catch (error) {
      console.error('Error loading financial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateMetrics = async (vouchers: any[]): Promise<FinancialMetrics> => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    // Import KPI calculation functions
    const { calculateLiquidityMetrics } = await import('@/utils/kpiCalculations');

    // Get company ID for KPI calculations
    const { data: companies } = await supabase
      .from('companies')
      .select('id')
      .eq('company_name', 'Nordström GreenTech AB')
      .limit(1);

    const companyId = companies?.[0]?.id;

    // Calculate new liquidity metrics
    const liquidityMetrics = companyId ? await calculateLiquidityMetrics(companyId) : {
      cashPosition: 15750000,
      cashEquivalents: 2500000,
      totalLiquidity: 18250000,
      cashVisibilityPercentage: 95,
      daysLiquidityCoverage: 185,
      cashBurnRate: 850000,
      fundingBuffer: 16550000
    };

    // Calculate monthly revenue and expenses
    let monthlyRevenue = 0;
    let monthlyExpenses = 0;
    const fxExposure = { EUR: 0, USD: 0, NOK: 0, DKK: 0 };
    const recentTransactions: Transaction[] = [];
    const currencyTotals: Record<string, number> = {};

    vouchers.forEach(voucher => {
      const voucherDate = new Date(voucher.transaction_date);
      const isCurrentMonth = voucherDate.getMonth() === currentMonth &&
                            voucherDate.getFullYear() === currentYear;

      // Process voucher rows
      voucher.voucher_rows?.forEach((row: any) => {
        const amount = Math.max(row.debit_amount, row.credit_amount);
        const currency = row.currency_code || 'SEK';

        // Track currency exposure
        if (currency !== 'SEK') {
          fxExposure[currency as keyof typeof fxExposure] += amount;
        }

        // Track currency totals
        currencyTotals[currency] = (currencyTotals[currency] || 0) + amount;

        // Calculate monthly revenue/expenses
        if (isCurrentMonth) {
          if (row.account_code.startsWith('3')) { // Revenue accounts
            monthlyRevenue += amount * (row.exchange_rate || 1);
          } else if (row.account_code.startsWith('4') || row.account_code.startsWith('5')) { // Expense accounts
            monthlyExpenses += amount * (row.exchange_rate || 1);
          }
        }
      });

      // Add to recent transactions
      if (recentTransactions.length < 10) {
        const type = voucher.voucher_rows?.some((row: any) => row.account_code.startsWith('3')) ? 'income' : 'expense';
        recentTransactions.push({
          id: voucher.id,
          date: voucher.transaction_date,
          description: voucher.description,
          amount: voucher.total_amount,
          currency: voucher.currency_code,
          type
        });
      }
    });

    // Calculate currency breakdown
    const totalAmount = Object.values(currencyTotals).reduce((sum, amount) => sum + amount, 0);
    const currencyBreakdown: CurrencyData[] = Object.entries(currencyTotals).map(([currency, amount]) => ({
      currency,
      amount,
      percentage: (amount / totalAmount) * 100,
      color: CURRENCY_COLORS[currency as keyof typeof CURRENCY_COLORS] || '#6b7280'
    }));

    // Generate cash flow trend (mock data for demo)
    const cashFlowTrend: CashFlowData[] = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      cashFlowTrend.push({
        date: date.toISOString().slice(0, 7),
        income: 8000000 + Math.random() * 4000000,
        expenses: 6000000 + Math.random() * 3000000,
        net: 2000000 + Math.random() * 1000000
      });
    }

    return {
      // Existing metrics
      cashPosition: liquidityMetrics.cashPosition,
      monthlyRevenue,
      monthlyExpenses,
      fxExposure,
      recentTransactions,
      currencyBreakdown,
      cashFlowTrend,

      // New liquidity metrics
      cashVisibilityPercentage: liquidityMetrics.cashVisibilityPercentage,
      daysLiquidityCoverage: liquidityMetrics.daysLiquidityCoverage,
      cashBurnRate: liquidityMetrics.cashBurnRate,
      fundingBuffer: liquidityMetrics.fundingBuffer,
      totalLiquidity: liquidityMetrics.totalLiquidity,
      cashEquivalents: liquidityMetrics.cashEquivalents
    };
  };

  const formatCurrency = (amount: number, currency: string = 'SEK') => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Financial Data</h3>
        <p className="text-gray-500 mb-4">Initialize demo data to see financial insights</p>
        <Button onClick={loadFinancialData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Financial Overview</h2>
          <p className="text-gray-500">Nordström GreenTech AB - Real-time insights</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-500">
            <Calendar className="h-4 w-4 inline mr-1" />
            Last updated: {lastUpdated.toLocaleTimeString('sv-SE')}
          </div>
          <Button variant="outline" size="sm" onClick={loadFinancialData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Liquidity & Cash Position KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Total Liquidity"
          value={metrics.totalLiquidity}
          format="currency"
          change={{
            value: 2.1,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={Droplets}
        />

        <MetricWidget
          title="Cash Visibility"
          value={metrics.cashVisibilityPercentage}
          format="percentage"
          change={{
            value: 0.5,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={Activity}
        />

        <MetricWidget
          title="Days Liquidity Coverage"
          value={metrics.daysLiquidityCoverage}
          format="days"
          change={{
            value: -5.2,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={Clock}
        />

        <MetricWidget
          title="Funding Buffer"
          value={metrics.fundingBuffer}
          format="currency"
          change={{
            value: 8.3,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={Target}
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Cash Position"
          value={metrics.cashPosition}
          format="currency"
          change={{
            value: 1.8,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={DollarSign}
        />

        <MetricWidget
          title="Cash Burn Rate"
          value={metrics.cashBurnRate}
          format="currency"
          change={{
            value: -3.2,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={TrendingDown}
        />

        <MetricWidget
          title="Cash Equivalents"
          value={metrics.cashEquivalents}
          format="currency"
          change={{
            value: 12.5,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={Building2}
        />

        <MetricWidget
          title="FX Exposure"
          value={Object.values(metrics.fxExposure).reduce((sum, val) => sum + val, 0)}
          format="currency"
          change={{
            value: -2.1,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={Globe}
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Cash Flow Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Cash Flow Trend</CardTitle>
            <CardDescription>Monthly income vs expenses (SEK)</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={metrics.cashFlowTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis tickFormatter={(value) => `${value / 1000000}M`} />
                <Tooltip
                  formatter={(value: number) => formatCurrency(value)}
                  labelFormatter={(label) => `Month: ${label}`}
                />
                <Line type="monotone" dataKey="income" stroke="#10b981" strokeWidth={2} name="Income" />
                <Line type="monotone" dataKey="expenses" stroke="#ef4444" strokeWidth={2} name="Expenses" />
                <Line type="monotone" dataKey="net" stroke="#3b82f6" strokeWidth={2} name="Net" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Currency Exposure */}
        <Card>
          <CardHeader>
            <CardTitle>Currency Exposure</CardTitle>
            <CardDescription>Transaction volume by currency</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={metrics.currencyBreakdown}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="amount"
                  label={({ currency, percentage }) => `${currency} ${percentage.toFixed(1)}%`}
                >
                  {metrics.currencyBreakdown.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => formatCurrency(value)} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>Latest vouchers from Fortnox</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics.recentTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`w-2 h-2 rounded-full ${
                    transaction.type === 'income' ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <div>
                    <p className="font-medium">{transaction.description}</p>
                    <p className="text-sm text-gray-500">{transaction.date}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-medium ${
                    transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.type === 'income' ? '+' : '-'}
                    {formatCurrency(transaction.amount, transaction.currency)}
                  </p>
                  <Badge variant="outline">{transaction.currency}</Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FinancialOverview;
