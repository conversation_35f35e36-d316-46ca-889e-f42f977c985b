import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calculator, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  Target,
  Activity,
  Clock,
  Zap,
  ArrowRight,
  CheckCircle
} from 'lucide-react';
import { RateFactors, calculateScenarios, ScenarioResult } from '@/utils/rateCalculations';

interface ScenarioEngineProps {
  currentFactors: RateFactors;
  loanAmount: number;
}

const ScenarioEngine: React.FC<ScenarioEngineProps> = ({ currentFactors, loanAmount }) => {
  const [selectedScenario, setSelectedScenario] = useState<ScenarioResult | null>(null);
  
  const scenarios = calculateScenarios(currentFactors, loanAmount);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getScenarioIcon = (scenarioName: string) => {
    if (scenarioName.includes('DSO')) return <Clock className="h-5 w-5" />;
    if (scenarioName.includes('EBITDA')) return <TrendingUp className="h-5 w-5" />;
    if (scenarioName.includes('FX')) return <Target className="h-5 w-5" />;
    return <Activity className="h-5 w-5" />;
  };

  const getScenarioColor = (scenarioName: string) => {
    if (scenarioName.includes('DSO')) return 'text-blue-600';
    if (scenarioName.includes('EBITDA')) return 'text-green-600';
    if (scenarioName.includes('FX')) return 'text-purple-600';
    return 'text-gray-600';
  };

  const getScenarioBgColor = (scenarioName: string) => {
    if (scenarioName.includes('DSO')) return 'bg-blue-50 border-blue-200';
    if (scenarioName.includes('EBITDA')) return 'bg-green-50 border-green-200';
    if (scenarioName.includes('FX')) return 'bg-purple-50 border-purple-200';
    return 'bg-gray-50 border-gray-200';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calculator className="h-5 w-5 mr-2" />
            What-If Scenario Analysis
          </CardTitle>
          <p className="text-sm text-gray-600">
            Explore how specific business improvements could impact your interest rate and annual costs
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Current Rate</div>
              <div className="text-2xl font-bold text-blue-600">
                {currentFactors.currentRate.toFixed(2)}%
              </div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Loan Amount</div>
              <div className="text-2xl font-bold text-gray-900">
                {formatCurrency(loanAmount)}
              </div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Annual Interest</div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(loanAmount * currentFactors.currentRate / 100)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Scenario Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {scenarios.map((scenario, index) => (
          <Card 
            key={index} 
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedScenario?.scenarioName === scenario.scenarioName 
                ? 'ring-2 ring-blue-500 shadow-lg' 
                : ''
            } ${getScenarioBgColor(scenario.scenarioName)}`}
            onClick={() => setSelectedScenario(scenario)}
          >
            <CardHeader className="pb-3">
              <CardTitle className={`text-sm flex items-center ${getScenarioColor(scenario.scenarioName)}`}>
                {getScenarioIcon(scenario.scenarioName)}
                <span className="ml-2">{scenario.scenarioName}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Rate Impact */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">New Rate</span>
                <div className="flex items-center space-x-2">
                  <span className="font-semibold">{scenario.newRate.toFixed(2)}%</span>
                  <Badge variant={scenario.rateDifference < 0 ? 'default' : 'destructive'}>
                    {scenario.rateDifference >= 0 ? '+' : ''}{scenario.rateDifference.toFixed(2)}%
                  </Badge>
                </div>
              </div>

              {/* Annual Savings */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Annual Impact</span>
                <div className={`font-semibold ${scenario.annualSavings > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {scenario.annualSavings > 0 ? '+' : ''}{formatCurrency(scenario.annualSavings)}
                </div>
              </div>

              {/* KPI Changes */}
              <div className="space-y-2">
                <span className="text-sm text-gray-600">Required Changes:</span>
                {Object.entries(scenario.kpiChanges).map(([kpi, change]) => (
                  <div key={kpi} className="flex items-center justify-between text-xs">
                    <span className="text-gray-500 capitalize">{kpi.replace(/([A-Z])/g, ' $1').trim()}</span>
                    <span className={`font-medium ${change > 0 ? 'text-green-600' : 'text-blue-600'}`}>
                      {change > 0 ? '+' : ''}{change}
                      {kpi.includes('Score') ? ' pts' : kpi.includes('Ratio') ? '%' : ' days'}
                    </span>
                  </div>
                ))}
              </div>

              {/* Action Button */}
              <Button 
                variant={selectedScenario?.scenarioName === scenario.scenarioName ? 'default' : 'outline'}
                size="sm" 
                className="w-full"
                onClick={(e) => {
                  e.stopPropagation();
                  setSelectedScenario(scenario);
                }}
              >
                {selectedScenario?.scenarioName === scenario.scenarioName ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Selected
                  </>
                ) : (
                  <>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Analyze
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Analysis */}
      {selectedScenario && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="h-5 w-5 mr-2" />
              Detailed Analysis: {selectedScenario.scenarioName}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Impact Summary */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Current Rate</div>
                <div className="text-xl font-bold text-blue-600">
                  {currentFactors.currentRate.toFixed(2)}%
                </div>
              </div>
              
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">New Rate</div>
                <div className="text-xl font-bold text-gray-900">
                  {selectedScenario.newRate.toFixed(2)}%
                </div>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Rate Reduction</div>
                <div className="text-xl font-bold text-green-600">
                  {Math.abs(selectedScenario.rateDifference).toFixed(2)}%
                </div>
              </div>
              
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-sm text-gray-600 mb-1">Annual Savings</div>
                <div className="text-xl font-bold text-yellow-600">
                  {formatCurrency(selectedScenario.annualSavings)}
                </div>
              </div>
            </div>

            {/* Implementation Steps */}
            <div className="space-y-4">
              <h4 className="font-semibold text-gray-900">Implementation Steps</h4>
              
              {selectedScenario.scenarioName.includes('DSO') && (
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-blue-600">1</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Review Customer Payment Terms</p>
                      <p className="text-sm text-gray-600">Analyze current payment terms and identify customers with extended payment periods</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-blue-600">2</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Implement Early Payment Incentives</p>
                      <p className="text-sm text-gray-600">Offer 2-3% discounts for payments within 10 days</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-blue-600">3</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Automate Invoice Follow-ups</p>
                      <p className="text-sm text-gray-600">Set up automated reminders at 15, 30, and 45 days</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedScenario.scenarioName.includes('EBITDA') && (
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-green-600">1</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Cost Structure Analysis</p>
                      <p className="text-sm text-gray-600">Identify and eliminate non-essential expenses</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-green-600">2</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Revenue Optimization</p>
                      <p className="text-sm text-gray-600">Focus on high-margin products and services</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-green-600">3</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Operational Efficiency</p>
                      <p className="text-sm text-gray-600">Automate processes to reduce operational costs</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedScenario.scenarioName.includes('FX') && (
                <div className="space-y-3">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-purple-600">1</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">FX Exposure Assessment</p>
                      <p className="text-sm text-gray-600">Quantify current foreign exchange exposure by currency</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-purple-600">2</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Implement Hedging Strategy</p>
                      <p className="text-sm text-gray-600">Use forward contracts or options to hedge 80% of FX exposure</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-xs font-medium text-purple-600">3</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Monitor and Adjust</p>
                      <p className="text-sm text-gray-600">Regular review and adjustment of hedging positions</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Timeline */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-2">Expected Timeline</h4>
              <p className="text-sm text-gray-600">
                Implementation: 2-4 weeks • Rate impact visible: Next monthly review • 
                Full benefits realized: 3-6 months
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ScenarioEngine;
