import React from 'react';

const CohortHeatmap: React.FC = () => {
  // Mock cohort retention data
  const cohortData = [
    { cohort: 'Jan 2024', m0: 100, m1: 95, m2: 89, m3: 85, m4: 82, m5: 79, m6: 76 },
    { cohort: 'Feb 2024', m0: 100, m1: 93, m2: 87, m3: 83, m4: 80, m5: 77, m6: null },
    { cohort: 'Mar 2024', m0: 100, m1: 96, m2: 91, m3: 87, m4: 84, m5: null, m6: null },
    { cohort: 'Apr 2024', m0: 100, m1: 94, m2: 88, m3: 84, m4: null, m5: null, m6: null },
    { cohort: 'May 2024', m0: 100, m1: 97, m2: 92, m3: null, m4: null, m5: null, m6: null },
    { cohort: 'Jun 2024', m0: 100, m1: 95, m2: null, m3: null, m4: null, m5: null, m6: null },
    { cohort: 'Jul 2024', m0: 100, m1: null, m2: null, m3: null, m4: null, m5: null, m6: null },
  ];

  const getColorIntensity = (value: number | null) => {
    if (value === null) return 'bg-gray-100';
    if (value >= 90) return 'bg-green-500';
    if (value >= 80) return 'bg-green-400';
    if (value >= 70) return 'bg-yellow-400';
    if (value >= 60) return 'bg-orange-400';
    return 'bg-red-400';
  };

  const getTextColor = (value: number | null) => {
    if (value === null) return 'text-gray-400';
    if (value >= 80) return 'text-white';
    return 'text-gray-900';
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Heatmap */}
      <div className="flex-1 overflow-auto">
        <div className="min-w-full">
          {/* Header */}
          <div className="grid grid-cols-8 gap-1 mb-2">
            <div className="text-body-xs text-gray-600 font-medium p-2">Cohort</div>
            <div className="text-body-xs text-gray-600 font-medium p-2 text-center">M0</div>
            <div className="text-body-xs text-gray-600 font-medium p-2 text-center">M1</div>
            <div className="text-body-xs text-gray-600 font-medium p-2 text-center">M2</div>
            <div className="text-body-xs text-gray-600 font-medium p-2 text-center">M3</div>
            <div className="text-body-xs text-gray-600 font-medium p-2 text-center">M4</div>
            <div className="text-body-xs text-gray-600 font-medium p-2 text-center">M5</div>
            <div className="text-body-xs text-gray-600 font-medium p-2 text-center">M6</div>
          </div>

          {/* Data Rows */}
          {cohortData.map((row, rowIndex) => (
            <div key={rowIndex} className="grid grid-cols-8 gap-1 mb-1">
              <div className="text-body-xs text-gray-700 font-medium p-2 bg-gray-50 rounded">
                {row.cohort}
              </div>
              <div className={`text-body-xs font-medium p-2 rounded text-center ${getColorIntensity(row.m0)} ${getTextColor(row.m0)}`}>
                {row.m0}%
              </div>
              <div className={`text-body-xs font-medium p-2 rounded text-center ${getColorIntensity(row.m1)} ${getTextColor(row.m1)}`}>
                {row.m1 ? `${row.m1}%` : '-'}
              </div>
              <div className={`text-body-xs font-medium p-2 rounded text-center ${getColorIntensity(row.m2)} ${getTextColor(row.m2)}`}>
                {row.m2 ? `${row.m2}%` : '-'}
              </div>
              <div className={`text-body-xs font-medium p-2 rounded text-center ${getColorIntensity(row.m3)} ${getTextColor(row.m3)}`}>
                {row.m3 ? `${row.m3}%` : '-'}
              </div>
              <div className={`text-body-xs font-medium p-2 rounded text-center ${getColorIntensity(row.m4)} ${getTextColor(row.m4)}`}>
                {row.m4 ? `${row.m4}%` : '-'}
              </div>
              <div className={`text-body-xs font-medium p-2 rounded text-center ${getColorIntensity(row.m5)} ${getTextColor(row.m5)}`}>
                {row.m5 ? `${row.m5}%` : '-'}
              </div>
              <div className={`text-body-xs font-medium p-2 rounded text-center ${getColorIntensity(row.m6)} ${getTextColor(row.m6)}`}>
                {row.m6 ? `${row.m6}%` : '-'}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center space-x-2">
          <span className="text-body-xs text-gray-600">Retention Rate:</span>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-red-400 rounded"></div>
            <span className="text-body-xs text-gray-500">Low</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-yellow-400 rounded"></div>
            <span className="text-body-xs text-gray-500">Medium</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span className="text-body-xs text-gray-500">High</span>
          </div>
        </div>
        <div className="text-body-xs text-gray-500">
          Average 6M retention: 78.5%
        </div>
      </div>
    </div>
  );
};

export default CohortHeatmap;
