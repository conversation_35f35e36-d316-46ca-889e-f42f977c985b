
import { supabase } from './client';
import { Database } from './types';

/**
 * Finance application type from the database
 */
export type FinanceApplication = Database['public']['Tables']['finance_applications']['Row'];

/**
 * Finance application insert type
 */
export type FinanceApplicationInsert = Database['public']['Tables']['finance_applications']['Insert'];

/**
 * Creates a new finance application
 * 
 * @param application Finance application data
 * @returns Created finance application or error
 */
export const createFinanceApplication = async (
  application: FinanceApplicationInsert
): Promise<{ data: FinanceApplication | null; error: Error | null }> => {
  try {
    // Set default values
    const now = new Date().toISOString();
    const applicationWithDefaults: FinanceApplicationInsert = {
      ...application,
      created_at: now,
      updated_at: now,
      status: 'draft',
    };

    const { data, error } = await supabase
      .from('finance_applications')
      .insert(applicationWithDefaults)
      .select()
      .single();

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error creating finance application:', error);
    return { data: null, error: error as Error };
  }
};

/**
 * Updates an existing finance application
 * 
 * @param id Finance application ID
 * @param updates Finance application updates
 * @returns Updated finance application or error
 */
export const updateFinanceApplication = async (
  id: string,
  updates: Partial<FinanceApplicationInsert>
): Promise<{ data: FinanceApplication | null; error: Error | null }> => {
  try {
    // Set updated_at timestamp
    const updatesWithTimestamp = {
      ...updates,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from('finance_applications')
      .update(updatesWithTimestamp)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error updating finance application:', error);
    return { data: null, error: error as Error };
  }
};

/**
 * Gets a finance application by ID
 * 
 * @param id Finance application ID
 * @returns Finance application or error
 */
export const getFinanceApplication = async (
  id: string
): Promise<{ data: FinanceApplication | null; error: Error | null }> => {
  try {
    const { data, error } = await supabase
      .from('finance_applications')
      .select()
      .eq('id', id)
      .single();

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error getting finance application:', error);
    return { data: null, error: error as Error };
  }
};

/**
 * Gets all finance applications for the current user
 * 
 * @returns Finance applications or error
 */
export const getFinanceApplications = async (): Promise<{
  data: FinanceApplication[] | null;
  error: Error | null;
}> => {
  try {
    const { data: user } = await supabase.auth.getUser();
    
    if (!user.user) {
      throw new Error('User not authenticated');
    }

    const { data, error } = await supabase
      .from('finance_applications')
      .select()
      .eq('user_id', user.user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error getting finance applications:', error);
    return { data: null, error: error as Error };
  }
};

/**
 * Submits a finance application for review
 * 
 * @param id Finance application ID
 * @returns Updated finance application or error
 */
export const submitFinanceApplication = async (
  id: string
): Promise<{ data: FinanceApplication | null; error: Error | null }> => {
  try {
    const { data, error } = await supabase
      .from('finance_applications')
      .update({
        status: 'submitted',
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error submitting finance application:', error);
    return { data: null, error: error as Error };
  }
};

/**
 * Uploads a financial document for a finance application
 * 
 * @param applicationId Finance application ID
 * @param file File to upload
 * @returns Uploaded file path or error
 */
export const uploadFinancialDocument = async (
  applicationId: string,
  file: File
): Promise<{ data: string | null; error: Error | null }> => {
  try {
    const fileExt = file.name.split('.').pop();
    const fileName = `${applicationId}/${Date.now()}.${fileExt}`;
    const filePath = `financial-documents/${fileName}`;

    const { error } = await supabase.storage
      .from('finance-applications')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (error) throw error;

    // Get the public URL
    const { data } = supabase.storage
      .from('finance-applications')
      .getPublicUrl(filePath);

    return { data: data.publicUrl, error: null };
  } catch (error) {
    console.error('Error uploading financial document:', error);
    return { data: null, error: error as Error };
  }
};
