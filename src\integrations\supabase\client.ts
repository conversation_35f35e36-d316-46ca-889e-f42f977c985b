// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://oalvyhgprwxydmeossvg.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9hbHZ5aGdwcnd4eWRtZW9zc3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5OTc5ODEsImV4cCI6MjA2MzU3Mzk4MX0.Ec3lMwbuo4i3Ncc-UpIheBUHm0ErObV13APXsT6aK30";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);