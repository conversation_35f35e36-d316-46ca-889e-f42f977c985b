-- Platform Modules Database Schema Extension
-- This migration adds tables for the comprehensive B2B fintech platform modules

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Companies table (enhanced for Fortnox integration)
CREATE TABLE IF NOT EXISTS companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_number VARCHAR(20) UNIQUE NOT NULL,
  company_name VARCHAR(255) NOT NULL,
  industry VARCHAR(100),
  base_currency VARCHAR(3) DEFAULT 'SEK',
  annual_revenue DECIMAL(15,2),
  employee_count INTEGER,
  fortnox_access_token TEXT,
  fortnox_refresh_token TEXT,
  fortnox_token_expires_at TIMESTAMP WITH TIME ZONE,
  fortnox_company_id VARCHAR(50),
  last_sync_at TIMESTAMP WITH TIME ZONE,
  sync_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'syncing', 'completed', 'error'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Exchange rates table for multi-currency support
CREATE TABLE IF NOT EXISTS exchange_rates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  base_currency VARCHAR(3) NOT NULL,
  target_currency VARCHAR(3) NOT NULL,
  rate DECIMAL(10,6) NOT NULL,
  rate_date DATE NOT NULL,
  source VARCHAR(50) DEFAULT 'ECB', -- European Central Bank, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(base_currency, target_currency, rate_date)
);

-- Chart of accounts table
CREATE TABLE IF NOT EXISTS chart_of_accounts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  account_code VARCHAR(10) NOT NULL,
  account_name VARCHAR(255) NOT NULL,
  account_type VARCHAR(50) NOT NULL, -- 'asset', 'liability', 'equity', 'revenue', 'expense'
  parent_account_code VARCHAR(10),
  is_active BOOLEAN DEFAULT true,
  fortnox_account_id VARCHAR(50),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id, account_code)
);

-- Vouchers table for Fortnox verifikationer
CREATE TABLE IF NOT EXISTS vouchers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,
  voucher_series VARCHAR(10) NOT NULL,
  voucher_number INTEGER NOT NULL,
  transaction_date DATE NOT NULL,
  description TEXT,
  total_amount DECIMAL(15,2) NOT NULL,
  currency_code VARCHAR(3) DEFAULT 'SEK',
  exchange_rate DECIMAL(10,6) DEFAULT 1.0,
  fortnox_voucher_id VARCHAR(50),
  fortnox_url TEXT,
  sync_status VARCHAR(20) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(company_id, voucher_series, voucher_number)
);

-- Voucher rows table for individual transaction lines
CREATE TABLE IF NOT EXISTS voucher_rows (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  voucher_id UUID REFERENCES vouchers(id) ON DELETE CASCADE,
  account_code VARCHAR(10) NOT NULL,
  account_name VARCHAR(255),
  debit_amount DECIMAL(15,2) DEFAULT 0,
  credit_amount DECIMAL(15,2) DEFAULT 0,
  currency_code VARCHAR(3) DEFAULT 'SEK',
  exchange_rate DECIMAL(10,6) DEFAULT 1.0,
  description TEXT,
  cost_center VARCHAR(50),
  project_code VARCHAR(50),
  row_number INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT valid_amounts CHECK (
    (debit_amount > 0 AND credit_amount = 0) OR
    (credit_amount > 0 AND debit_amount = 0)
  )
);

-- Voucher files table for attachments
CREATE TABLE IF NOT EXISTS voucher_files (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  voucher_id UUID REFERENCES vouchers(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_url TEXT NOT NULL,
  file_type VARCHAR(50),
  file_size INTEGER,
  fortnox_file_id VARCHAR(50),
  upload_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Funding metrics table for Funding Readiness Dashboard
CREATE TABLE funding_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,

  -- Core ARR metrics
  arr_current DECIMAL(15,2),
  arr_previous_year DECIMAL(15,2),
  arr_growth_rate DECIMAL(5,2),

  -- Retention metrics
  nrr_percentage DECIMAL(5,2), -- Net Revenue Retention
  gross_retention_rate DECIMAL(5,2),
  cohort_retention JSONB, -- Store cohort data as JSON

  -- Unit economics
  ltv DECIMAL(15,2), -- Lifetime Value
  cac DECIMAL(15,2), -- Customer Acquisition Cost
  ltv_cac_ratio DECIMAL(5,2),

  -- Growth metrics
  monthly_growth_rate DECIMAL(5,2),
  quarterly_growth_rate DECIMAL(5,2),

  -- Funding readiness score
  funding_score DECIMAL(3,1), -- 0.0 to 10.0

  -- Metadata
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Treasury data table for Treasury Management
CREATE TABLE treasury_data (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,

  -- Cash position
  cash_position DECIMAL(15,2) NOT NULL,
  cash_equivalents DECIMAL(15,2),
  total_liquidity DECIMAL(15,2),

  -- Cash flow metrics
  monthly_burn_rate DECIMAL(15,2),
  runway_months DECIMAL(4,1),

  -- FX exposure
  fx_exposure JSONB, -- Store currency exposure data
  fx_hedging_ratio DECIMAL(5,2),

  -- Liquidity ratios
  current_ratio DECIMAL(5,2),
  quick_ratio DECIMAL(5,2),

  -- Forecasting data
  cash_forecast JSONB, -- 12-month cash flow forecast
  scenario_analysis JSONB, -- Best/worst/base case scenarios

  -- Metadata
  reporting_date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Growth KPIs table for Analytics Dashboard
CREATE TABLE growth_kpis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,

  -- Working capital metrics
  dso_days INTEGER, -- Days Sales Outstanding
  dpo_days INTEGER, -- Days Payable Outstanding
  dio_days INTEGER, -- Days Inventory Outstanding
  cash_conversion_cycle INTEGER, -- DSO + DIO - DPO

  -- Inventory metrics
  inventory_turnover DECIMAL(5,2),
  inventory_value DECIMAL(15,2),

  -- Operational efficiency
  asset_turnover DECIMAL(5,2),
  working_capital DECIMAL(15,2),
  working_capital_ratio DECIMAL(5,2),

  -- Growth indicators
  revenue_growth_rate DECIMAL(5,2),
  gross_margin DECIMAL(5,2),
  operating_margin DECIMAL(5,2),

  -- Industry benchmarks
  industry_dso_benchmark INTEGER,
  industry_dpo_benchmark INTEGER,
  industry_turnover_benchmark DECIMAL(5,2),

  -- Performance scores
  efficiency_score DECIMAL(3,1), -- 0.0 to 10.0

  -- Metadata
  reporting_period DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Compliance and audit trail table
CREATE TABLE audit_trail (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,

  -- Audit details
  action_type VARCHAR(100) NOT NULL, -- e.g., 'loan_application', 'document_upload'
  action_description TEXT NOT NULL,

  -- User information
  user_id UUID, -- Reference to user performing action
  user_email VARCHAR(255),
  user_role VARCHAR(50),

  -- System information
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(255),

  -- Data changes (for GDPR compliance)
  data_before JSONB,
  data_after JSONB,

  -- GDPR compliance
  data_category VARCHAR(100), -- e.g., 'personal_data', 'financial_data'
  retention_period INTERVAL,

  -- Metadata
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- GDPR data requests table
CREATE TABLE gdpr_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,

  -- Request details
  request_type VARCHAR(50) NOT NULL, -- 'access', 'rectification', 'erasure', 'portability'
  request_status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'in_progress', 'completed', 'rejected'

  -- Requester information
  requester_email VARCHAR(255) NOT NULL,
  requester_name VARCHAR(255),
  identity_verified BOOLEAN DEFAULT FALSE,

  -- Request specifics
  data_categories TEXT[], -- Array of data categories requested
  reason TEXT,

  -- Processing information
  assigned_to UUID, -- User handling the request
  response_data JSONB, -- Data provided in response
  completion_notes TEXT,

  -- Compliance tracking
  request_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  due_date TIMESTAMP WITH TIME ZONE, -- 30 days from request
  completed_date TIMESTAMP WITH TIME ZONE,

  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Compliance reports table
CREATE TABLE compliance_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id) ON DELETE CASCADE,

  -- Report details
  report_type VARCHAR(100) NOT NULL, -- 'monthly_risk', 'gdpr_compliance', 'audit_summary'
  report_name VARCHAR(255) NOT NULL,
  report_status VARCHAR(50) DEFAULT 'not_started', -- 'not_started', 'draft', 'in_progress', 'completed'

  -- Scheduling
  due_date DATE NOT NULL,
  priority VARCHAR(20) DEFAULT 'medium', -- 'low', 'medium', 'high'

  -- Content
  report_data JSONB, -- Generated report content
  report_file_url TEXT, -- Link to generated file

  -- Metadata
  created_by UUID,
  assigned_to UUID,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_funding_metrics_company_period ON funding_metrics(company_id, period_start, period_end);
CREATE INDEX idx_treasury_data_company_date ON treasury_data(company_id, reporting_date);
CREATE INDEX idx_growth_kpis_company_period ON growth_kpis(company_id, reporting_period);
CREATE INDEX idx_audit_trail_company_timestamp ON audit_trail(company_id, timestamp);
CREATE INDEX idx_audit_trail_action_type ON audit_trail(action_type);
CREATE INDEX idx_gdpr_requests_status ON gdpr_requests(request_status);
CREATE INDEX idx_gdpr_requests_due_date ON gdpr_requests(due_date);
CREATE INDEX idx_compliance_reports_due_date ON compliance_reports(due_date, report_status);

-- Row Level Security (RLS) policies
ALTER TABLE funding_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE treasury_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE growth_kpis ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_trail ENABLE ROW LEVEL SECURITY;
ALTER TABLE gdpr_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_reports ENABLE ROW LEVEL SECURITY;

-- RLS policies (companies can only see their own data)
CREATE POLICY "Companies can view their own funding metrics" ON funding_metrics
  FOR ALL USING (company_id IN (
    SELECT id FROM companies WHERE organization_number = current_setting('app.current_org_number', true)
  ));

CREATE POLICY "Companies can view their own treasury data" ON treasury_data
  FOR ALL USING (company_id IN (
    SELECT id FROM companies WHERE organization_number = current_setting('app.current_org_number', true)
  ));

CREATE POLICY "Companies can view their own growth KPIs" ON growth_kpis
  FOR ALL USING (company_id IN (
    SELECT id FROM companies WHERE organization_number = current_setting('app.current_org_number', true)
  ));

CREATE POLICY "Companies can view their own audit trail" ON audit_trail
  FOR ALL USING (company_id IN (
    SELECT id FROM companies WHERE organization_number = current_setting('app.current_org_number', true)
  ));

CREATE POLICY "Companies can view their own GDPR requests" ON gdpr_requests
  FOR ALL USING (company_id IN (
    SELECT id FROM companies WHERE organization_number = current_setting('app.current_org_number', true)
  ));

CREATE POLICY "Companies can view their own compliance reports" ON compliance_reports
  FOR ALL USING (company_id IN (
    SELECT id FROM companies WHERE organization_number = current_setting('app.current_org_number', true)
  ));

-- Insert sample data for demonstration - Nordström GreenTech AB
INSERT INTO companies (organization_number, company_name, industry, base_currency, annual_revenue, employee_count)
VALUES ('556123-4567', 'Nordström GreenTech AB', 'Wind Turbine Components Manufacturing', 'SEK', *********, 50)
ON CONFLICT (organization_number) DO NOTHING;

-- Sample funding metrics for Nordström GreenTech AB
INSERT INTO funding_metrics (
  company_id, arr_current, arr_previous_year, arr_growth_rate, nrr_percentage,
  gross_retention_rate, ltv, cac, ltv_cac_ratio, funding_score,
  period_start, period_end
) VALUES (
  (SELECT id FROM companies WHERE organization_number = '556123-4567'),
  *********, *********, 25.0, 115.0, 92.5, 45000, 3500, 12.9, 8.7,
  '2024-01-01', '2024-12-31'
);

-- Sample treasury data for Nordström GreenTech AB
INSERT INTO treasury_data (
  company_id, cash_position, total_liquidity, monthly_burn_rate, runway_months,
  current_ratio, quick_ratio, reporting_date
) VALUES (
  (SELECT id FROM companies WHERE organization_number = '556123-4567'),
  15750000, 16200000, 1200000, 13.1, 2.8, 2.4, '2024-01-15'
);

-- Sample growth KPIs for Nordström GreenTech AB
INSERT INTO growth_kpis (
  company_id, dso_days, dpo_days, dio_days, cash_conversion_cycle,
  inventory_turnover, working_capital, efficiency_score, reporting_period
) VALUES (
  (SELECT id FROM companies WHERE organization_number = '556123-4567'),
  35, 42, 38, 31, 9.5, 5200000, 8.8, '2024-01-01'
);
