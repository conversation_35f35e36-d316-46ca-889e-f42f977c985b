/**
 * Financing Application Dashboard Component
 * Comprehensive financing application with pre-populated financial data
 */

import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  FileText,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertTriangle,
  Upload,
  Calculator,
  Building2,
  RefreshCw,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import MetricWidget from '@/components/dashboard/MetricWidget';

interface FinancialHealthData {
  cashPosition: number;
  monthlyRevenue: number;
  dso: number;
  dpo: number;
  creditScore: number;
  debtToEquity: number;
}

interface ApplicationData {
  loanAmount: number;
  loanPurpose: string;
  loanTerm: number;
  interestRatePreference: 'fixed' | 'variable';
  documents: File[];
  companyId: string;
}

const LOAN_PURPOSES = [
  { value: 'working_capital', label: 'Working Capital' },
  { value: 'equipment_purchase', label: 'Equipment Purchase' },
  { value: 'business_expansion', label: 'Business Expansion' },
  { value: 'inventory_financing', label: 'Inventory Financing' },
  { value: 'real_estate', label: 'Real Estate' },
  { value: 'refinancing', label: 'Refinancing' }
];

const FinancingApplicationDashboard: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(true);
  const [financialHealth, setFinancialHealth] = useState<FinancialHealthData | null>(null);
  const [applicationData, setApplicationData] = useState<ApplicationData>({
    loanAmount: 1000000,
    loanPurpose: '',
    loanTerm: 24,
    interestRatePreference: 'fixed',
    documents: [],
    companyId: ''
  });

  useEffect(() => {
    loadFinancialHealth();
  }, []);

  const loadFinancialHealth = async () => {
    try {
      setLoading(true);

      // Get demo company
      const { data: companies } = await supabase
        .from('companies')
        .select('*')
        .eq('company_name', 'Nordström GreenTech AB')
        .limit(1);

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].id;
      setApplicationData(prev => ({ ...prev, companyId }));

      // Import KPI calculations
      const { calculateLiquidityMetrics, calculateWorkingCapitalMetrics } = await import('@/utils/kpiCalculations');

      const [liquidityMetrics, workingCapitalMetrics] = await Promise.all([
        calculateLiquidityMetrics(companyId),
        calculateWorkingCapitalMetrics(companyId)
      ]);

      // Calculate financial health indicators
      const healthData: FinancialHealthData = {
        cashPosition: liquidityMetrics.cashPosition || 15750000,
        monthlyRevenue: 8500000, // Would be calculated from revenue data
        dso: workingCapitalMetrics.dso || 32,
        dpo: workingCapitalMetrics.dpo || 45,
        creditScore: 750, // Would be fetched from credit bureau
        debtToEquity: 0.35 // Would be calculated from balance sheet
      };

      setFinancialHealth(healthData);

    } catch (error) {
      console.error('Error loading financial health:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const calculateLoanEligibility = () => {
    if (!financialHealth) return { eligible: false, maxAmount: 0, score: 0 };

    const cashRatio = financialHealth.cashPosition / applicationData.loanAmount;
    const revenueRatio = (financialHealth.monthlyRevenue * 12) / applicationData.loanAmount;
    const creditFactor = financialHealth.creditScore / 850;
    const debtFactor = Math.max(0, 1 - financialHealth.debtToEquity);

    const score = Math.round((cashRatio * 0.3 + revenueRatio * 0.4 + creditFactor * 0.2 + debtFactor * 0.1) * 100);
    const maxAmount = Math.min(50000000, financialHealth.monthlyRevenue * 12 * 0.5);
    const eligible = score >= 60 && applicationData.loanAmount <= maxAmount;

    return { eligible, maxAmount, score };
  };

  const handleSubmitApplication = async () => {
    try {
      const eligibility = calculateLoanEligibility();

      const applicationRecord = {
        organization_number: '***********', // From company data
        company_name: 'Nordström GreenTech AB',
        company_type: 'Aktiebolag',
        has_vat_registration: true,
        contact_person_name: 'Erik Nordström',
        contact_person_email: '<EMAIL>',
        contact_person_phone: '+46 70 123 4567',
        contact_person_role: 'CEO',
        contact_person_personnummer: '19801201-1234',
        loan_amount: applicationData.loanAmount,
        loan_purpose: applicationData.loanPurpose,
        loan_term_months: applicationData.loanTerm,
        financial_documents: [], // Would include uploaded documents
        consent_marketing: false,
        consent_data_processing: true,
        consent_credit_check: true,
        consent_terms: true,
        status: eligibility.eligible ? 'submitted' : 'under_review'
      };

      const { error } = await supabase
        .from('finance_applications')
        .insert([applicationRecord]);

      if (error) throw error;

      setCurrentStep(4); // Move to success step
    } catch (error) {
      console.error('Error submitting application:', error);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-heading-lg mb-4">Financial Health Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <MetricWidget
            title="Cash Position"
            value={financialHealth?.cashPosition || 0}
            format="currency"
            change={{ value: 2.1, period: 'vs last month', type: 'increase' }}
            icon={CreditCard}
            size="sm"
          />
          <MetricWidget
            title="Monthly Revenue"
            value={financialHealth?.monthlyRevenue || 0}
            format="currency"
            change={{ value: 8.5, period: 'vs last month', type: 'increase' }}
            icon={TrendingUp}
            size="sm"
          />
          <MetricWidget
            title="Credit Score"
            value={financialHealth?.creditScore || 0}
            format="number"
            change={{ value: 15, period: 'vs last quarter', type: 'increase' }}
            icon={CheckCircle}
            size="sm"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <MetricWidget
          title="Days Sales Outstanding"
          value={financialHealth?.dso || 0}
          format="days"
          change={{ value: -3.2, period: 'vs last quarter', type: 'decrease' }}
          icon={Clock}
          size="sm"
        />
        <MetricWidget
          title="Debt-to-Equity Ratio"
          value={financialHealth?.debtToEquity || 0}
          format="number"
          change={{ value: -0.05, period: 'vs last quarter', type: 'decrease' }}
          icon={AlertTriangle}
          size="sm"
        />
      </div>
    </div>
  );

  const renderStep2 = () => {
    const eligibility = calculateLoanEligibility();

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-heading-lg mb-4">Loan Details</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="loanAmount">Loan Amount (SEK)</Label>
                <Input
                  id="loanAmount"
                  type="number"
                  min="100000"
                  max="50000000"
                  value={applicationData.loanAmount}
                  onChange={(e) => setApplicationData(prev => ({
                    ...prev,
                    loanAmount: parseInt(e.target.value) || 0
                  }))}
                  className="mt-1"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Min: 100,000 SEK • Max: {formatCurrency(eligibility.maxAmount)}
                </p>
              </div>

              <div>
                <Label htmlFor="loanPurpose">Loan Purpose</Label>
                <Select
                  value={applicationData.loanPurpose}
                  onValueChange={(value) => setApplicationData(prev => ({ ...prev, loanPurpose: value }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="Select loan purpose" />
                  </SelectTrigger>
                  <SelectContent>
                    {LOAN_PURPOSES.map(purpose => (
                      <SelectItem key={purpose.value} value={purpose.value}>
                        {purpose.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Loan Term: {applicationData.loanTerm} months</Label>
                <Slider
                  value={[applicationData.loanTerm]}
                  onValueChange={(value) => setApplicationData(prev => ({ ...prev, loanTerm: value[0] }))}
                  min={6}
                  max={120}
                  step={6}
                  className="mt-2"
                />
                <div className="flex justify-between text-sm text-gray-500 mt-1">
                  <span>6 months</span>
                  <span>120 months</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <Label>Interest Rate Preference</Label>
                <RadioGroup
                  value={applicationData.interestRatePreference}
                  onValueChange={(value: 'fixed' | 'variable') =>
                    setApplicationData(prev => ({ ...prev, interestRatePreference: value }))
                  }
                  className="mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="fixed" id="fixed" />
                    <Label htmlFor="fixed">Fixed Rate (3.5% - 5.5%)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="variable" id="variable" />
                    <Label htmlFor="variable">Variable Rate (2.8% - 4.8%)</Label>
                  </div>
                </RadioGroup>
              </div>

              <Card className={`p-4 ${eligibility.eligible ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}`}>
                <div className="flex items-center space-x-2 mb-2">
                  {eligibility.eligible ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-5 w-5 text-yellow-600" />
                  )}
                  <span className="font-medium">
                    {eligibility.eligible ? 'Pre-Approved' : 'Under Review'}
                  </span>
                </div>
                <p className="text-sm text-gray-600">
                  Eligibility Score: {eligibility.score}/100
                </p>
                <p className="text-sm text-gray-600">
                  Max Loan Amount: {formatCurrency(eligibility.maxAmount)}
                </p>
              </Card>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-display-sm">Apply for Financing</h1>
          <p className="text-body-lg text-gray-600 mt-2">
            Get financing based on your real-time financial performance
          </p>
        </div>
        <Badge variant="outline" className="text-green-600 border-green-200">
          <Building2 className="h-4 w-4 mr-1" />
          Nordström GreenTech AB
        </Badge>
      </div>

      {/* Progress Indicator */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium">Application Progress</span>
            <span className="text-sm text-gray-500">{currentStep}/3</span>
          </div>
          <Progress value={(currentStep / 3) * 100} className="mb-4" />
          <div className="flex justify-between text-sm">
            <span className={currentStep >= 1 ? 'text-blue-600 font-medium' : 'text-gray-500'}>
              Financial Review
            </span>
            <span className={currentStep >= 2 ? 'text-blue-600 font-medium' : 'text-gray-500'}>
              Loan Details
            </span>
            <span className={currentStep >= 3 ? 'text-blue-600 font-medium' : 'text-gray-500'}>
              Documents & Submit
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Step Content */}
      <Card>
        <CardContent className="p-6">
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && (
            <div className="space-y-6">
              <h3 className="text-heading-lg">Upload Documents</h3>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 mb-2">Drag and drop your financial documents here</p>
                  <p className="text-sm text-gray-500">PDF, Excel, or Word documents (max 10MB each)</p>
                  <Button variant="outline" className="mt-4">
                    Choose Files
                  </Button>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Required Documents:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Latest annual financial statements</li>
                    <li>• Bank statements (last 3 months)</li>
                    <li>• Tax returns (last 2 years)</li>
                    <li>• Business registration certificate</li>
                    <li>• Management accounts (if available)</li>
                  </ul>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-blue-900">Auto-populated from ERP</p>
                      <p className="text-sm text-blue-700">
                        We've automatically included your Fortnox financial data. Additional documents will strengthen your application.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {currentStep === 4 && (
            <div className="text-center space-y-6">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h3 className="text-heading-lg text-green-900 mb-2">Application Submitted Successfully!</h3>
                <p className="text-gray-600">
                  Your financing application has been submitted and is being reviewed by our team.
                </p>
              </div>

              <div className="bg-gray-50 rounded-lg p-6 text-left max-w-md mx-auto">
                <h4 className="font-medium mb-3">Application Summary:</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Loan Amount:</span>
                    <span className="font-medium">{formatCurrency(applicationData.loanAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Purpose:</span>
                    <span className="font-medium">
                      {LOAN_PURPOSES.find(p => p.value === applicationData.loanPurpose)?.label}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Term:</span>
                    <span className="font-medium">{applicationData.loanTerm} months</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Rate Type:</span>
                    <span className="font-medium capitalize">{applicationData.interestRatePreference}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  <strong>Next Steps:</strong>
                </p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• You'll receive an email confirmation within 5 minutes</li>
                  <li>• Our team will review your application within 24 hours</li>
                  <li>• We may contact you for additional information</li>
                  <li>• Final decision will be communicated within 3-5 business days</li>
                </ul>
              </div>

              <Button onClick={() => window.location.href = '/dashboard'} className="mt-6">
                Return to Dashboard
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      {currentStep < 4 && (
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          {currentStep < 3 ? (
            <Button
              onClick={() => setCurrentStep(currentStep + 1)}
              disabled={currentStep === 2 && !applicationData.loanPurpose}
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmitApplication}
              className="bg-green-600 hover:bg-green-700"
              disabled={!applicationData.loanPurpose}
            >
              Submit Application
              <CheckCircle className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default FinancingApplicationDashboard;
