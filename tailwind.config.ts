import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '1.5rem',
			screens: {
				'sm': '640px',
				'md': '768px',
				'lg': '1024px',
				'xl': '1280px',
				'2xl': '1400px'
			}
		},
		fontFamily: {
			sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
			display: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',

				// Treyd-inspired color palette - minimal and clean
				primary: {
					DEFAULT: '#000000',     // Pure black for primary actions
					50: '#f9fafb',
					100: '#f3f4f6',
					200: '#e5e7eb',
					300: '#d1d5db',
					400: '#9ca3af',
					500: '#6b7280',
					600: '#4b5563',
					700: '#374151',
					800: '#1f2937',
					900: '#111827',
					950: '#030712',
					foreground: '#ffffff'
				},

				// Clean gray scale matching treyd.io
				gray: {
					50: '#f9fafb',
					100: '#f3f4f6',
					200: '#e5e7eb',
					300: '#d1d5db',
					400: '#9ca3af',
					500: '#6b7280',
					600: '#4b5563',
					700: '#374151',
					800: '#1f2937',
					900: '#111827',
					950: '#030712',
				},

				// Minimal accent colors
				accent: {
					DEFAULT: '#000000',     // Black accent
					light: '#f9fafb',
					foreground: '#ffffff'
				},

				// Status colors - subtle and professional
				success: {
					DEFAULT: '#10b981',
					50: '#ecfdf5',
					100: '#d1fae5',
					500: '#10b981',
					600: '#059669',
					700: '#047857',
				},
				warning: {
					DEFAULT: '#f59e0b',
					50: '#fffbeb',
					100: '#fef3c7',
					500: '#f59e0b',
					600: '#d97706',
				},
				error: {
					DEFAULT: '#ef4444',
					50: '#fef2f2',
					100: '#fee2e2',
					500: '#ef4444',
					600: '#dc2626',
				},

				secondary: {
					DEFAULT: '#f3f4f6',
					foreground: '#111827'
				},
				destructive: {
					DEFAULT: '#ef4444',
					foreground: '#ffffff'
				},
				muted: {
					DEFAULT: '#f9fafb',
					foreground: '#6b7280'
				},
				popover: {
					DEFAULT: '#ffffff',
					foreground: '#111827'
				},
				card: {
					DEFAULT: '#ffffff',
					foreground: '#111827'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				}
			},
			borderRadius: {
				none: '0',
				sm: '0.125rem',
				DEFAULT: '0.375rem',
				md: '0.5rem',
				lg: '0.75rem',
				xl: '1rem',
				'2xl': '1.5rem',
				full: '9999px'
			},
			fontSize: {
				// Treyd.io-inspired typography - clean and readable
				'display-xl': ['4.5rem', { lineHeight: '1.1', fontWeight: '600', letterSpacing: '-0.02em' }],
				'display-lg': ['3.75rem', { lineHeight: '1.1', fontWeight: '600', letterSpacing: '-0.02em' }],
				'display-md': ['3rem', { lineHeight: '1.2', fontWeight: '600', letterSpacing: '-0.015em' }],
				'display-sm': ['2.25rem', { lineHeight: '1.3', fontWeight: '600', letterSpacing: '-0.01em' }],
				'heading-xl': ['2rem', { lineHeight: '1.3', fontWeight: '600', letterSpacing: '-0.01em' }],
				'heading-lg': ['1.5rem', { lineHeight: '1.4', fontWeight: '600', letterSpacing: '-0.005em' }],
				'heading-md': ['1.25rem', { lineHeight: '1.4', fontWeight: '600' }],
				'heading-sm': ['1.125rem', { lineHeight: '1.5', fontWeight: '600' }],
				'body-xl': ['1.25rem', { lineHeight: '1.6', fontWeight: '400' }],
				'body-lg': ['1.125rem', { lineHeight: '1.6', fontWeight: '400' }],
				'body-md': ['1rem', { lineHeight: '1.6', fontWeight: '400' }],
				'body-sm': ['0.875rem', { lineHeight: '1.5', fontWeight: '400' }],
				'body-xs': ['0.75rem', { lineHeight: '1.5', fontWeight: '400' }],
				'label-lg': ['0.875rem', { lineHeight: '1.25', fontWeight: '500' }],
				'label-md': ['0.75rem', { lineHeight: '1.25', fontWeight: '500' }],
				'label-sm': ['0.6875rem', { lineHeight: '1.25', fontWeight: '500' }],
			},
			spacing: {
				'0.5': '0.125rem',
				'1.5': '0.375rem',
				'2.5': '0.625rem',
				'3.5': '0.875rem',
				'4.5': '1.125rem',
				'5.5': '1.375rem',
				'6.5': '1.625rem',
				'7.5': '1.875rem',
				'8.5': '2.125rem',
				'9.5': '2.375rem',
				'10.5': '2.625rem',
				'11.5': '2.875rem',
				'12.5': '3.125rem',
				'13': '3.25rem',
				'15': '3.75rem',
				'17': '4.25rem',
				'18': '4.5rem',
				'19': '4.75rem',
				'21': '5.25rem',
				'22': '5.5rem',
				'23': '5.75rem',
				'25': '6.25rem',
				'26': '6.5rem',
				'27': '6.75rem',
				'28': '7rem',
				'29': '7.25rem',
				'30': '7.5rem',
				'section': '5rem',
				'section-sm': '3rem',
				'section-xs': '2rem',
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					from: { opacity: '0' },
					to: { opacity: '1' }
				},
				'fade-out': {
					from: { opacity: '1' },
					to: { opacity: '0' }
				},
				'slide-in': {
					from: { transform: 'translateY(10px)', opacity: '0' },
					to: { transform: 'translateY(0)', opacity: '1' }
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.3s ease-out',
				'fade-out': 'fade-out 0.3s ease-out',
				'slide-in': 'slide-in 0.4s ease-out'
			},
			boxShadow: {
				'subtle': '0 1px 2px 0 rgba(0, 0, 0, 0.03)',
				'card': '0 1px 3px 0 rgba(0, 0, 0, 0.04), 0 1px 2px 0 rgba(0, 0, 0, 0.03)',
				'card-hover': '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)',
				'button': '0 1px 2px 0 rgba(0, 0, 0, 0.03)',
				'button-hover': '0 2px 4px -1px rgba(0, 0, 0, 0.04), 0 1px 2px -1px rgba(0, 0, 0, 0.03)',
				'elevated': '0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -2px rgba(0, 0, 0, 0.02)',
			},
			backgroundImage: {
				'primary-gradient': 'linear-gradient(135deg, #1E3A8A 0%, #0F172A 100%)',
				'light-gradient': 'linear-gradient(135deg, #F1F5F9 0%, #FFFFFF 100%)',
				'accent-gradient': 'linear-gradient(135deg, #0F766E 0%, #0D9488 100%)',
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
