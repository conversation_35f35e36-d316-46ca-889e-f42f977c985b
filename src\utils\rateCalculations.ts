/**
 * Interest Rate Calculation Engine
 * Calculates dynamic interest rates based on KPI performance
 */

import {
  LiquidityMetrics,
  WorkingCapitalMetrics,
  ProfitabilityMetrics,
  ComplianceMetrics,
  RateFactors
} from './kpiCalculations';

export interface RateComponent {
  name: string;
  value: number;
  weight: number;
  impact: number; // basis points
  description: string;
}

export interface RateBreakdown {
  baseRate: number;
  riskPremium: number;
  components: RateComponent[];
  totalRate: number;
  industryBenchmark: number;
  potentialSavings: number;
}

export interface ScenarioResult {
  scenarioName: string;
  newRate: number;
  rateDifference: number;
  annualSavings: number;
  kpiChanges: Record<string, number>;
}

// Base rate configuration
const BASE_RATE = 2.5; // Current Swedish base rate
const INDUSTRY_BENCHMARK = 6.2; // Average SME lending rate

// KPI scoring weights (total should equal 100)
const WEIGHTS = {
  liquidity: 25,
  workingCapital: 20,
  profitability: 25,
  risk: 15,
  compliance: 15
};

// Rate impact ranges (in basis points)
const RATE_IMPACTS = {
  excellent: -150, // -1.5%
  good: -75,       // -0.75%
  average: 0,      // 0%
  poor: 100,       // ****%
  critical: 250    // ****%
};

/**
 * Calculate liquidity score based on cash position and coverage
 */
export function calculateLiquidityScore(metrics: LiquidityMetrics): number {
  const cashCoverageScore = Math.min(100, (metrics.daysLiquidityCoverage / 180) * 100);
  const liquidityRatioScore = Math.min(100, (metrics.totalLiquidity / metrics.cashBurnRate / 6) * 100);
  const visibilityScore = metrics.cashVisibilityPercentage;
  
  return (cashCoverageScore * 0.4 + liquidityRatioScore * 0.4 + visibilityScore * 0.2);
}

/**
 * Calculate working capital efficiency score
 */
export function calculateWorkingCapitalScore(metrics: WorkingCapitalMetrics): number {
  // Optimal ranges for Swedish SMEs
  const dsoScore = Math.max(0, Math.min(100, 100 - ((metrics.dso - 30) / 60) * 100));
  const dpoScore = Math.min(100, (metrics.dpo / 45) * 100);
  const cccScore = Math.max(0, Math.min(100, 100 - (metrics.ccc / 90) * 100));
  
  return (dsoScore * 0.4 + dpoScore * 0.3 + cccScore * 0.3);
}

/**
 * Calculate profitability score
 */
export function calculateProfitabilityScore(metrics: ProfitabilityMetrics): number {
  const ebitdaScore = Math.min(100, (metrics.ebitdaMargin / 25) * 100);
  const growthScore = Math.max(0, Math.min(100, 50 + (metrics.revenueGrowth / 30) * 50));
  const retentionScore = metrics.customerRetention;
  const marginScore = Math.min(100, (metrics.grossMargin / 70) * 100);
  
  return (ebitdaScore * 0.3 + growthScore * 0.3 + retentionScore * 0.2 + marginScore * 0.2);
}

/**
 * Calculate risk score (lower is better)
 */
export function calculateRiskScore(fxExposure: number, totalAssets: number): number {
  const fxRiskRatio = fxExposure / totalAssets;
  const fxRiskScore = Math.max(0, 100 - (fxRiskRatio * 500)); // Penalize high FX exposure
  
  // Additional risk factors would be calculated here
  const concentrationRisk = 85; // Mock value
  const industryRisk = 90; // Mock value
  
  return (fxRiskScore * 0.4 + concentrationRisk * 0.3 + industryRisk * 0.3);
}

/**
 * Calculate compliance score
 */
export function calculateComplianceScore(metrics: ComplianceMetrics): number {
  return (
    metrics.covenantCompliance * 0.3 +
    metrics.onTimeTaxPayments * 0.3 +
    metrics.auditScore * 0.2 +
    metrics.regulatoryCompliance * 0.2
  );
}

/**
 * Convert score to rate impact
 */
export function scoreToRateImpact(score: number): number {
  if (score >= 90) return RATE_IMPACTS.excellent;
  if (score >= 75) return RATE_IMPACTS.good;
  if (score >= 60) return RATE_IMPACTS.average;
  if (score >= 40) return RATE_IMPACTS.poor;
  return RATE_IMPACTS.critical;
}

/**
 * Calculate comprehensive rate factors
 */
export function calculateRateFactors(
  liquidityMetrics: LiquidityMetrics,
  workingCapitalMetrics: WorkingCapitalMetrics,
  profitabilityMetrics: ProfitabilityMetrics,
  complianceMetrics: ComplianceMetrics,
  fxExposure: number = 2340000,
  totalAssets: number = 50000000
): RateFactors {
  // Calculate individual scores
  const liquidityScore = calculateLiquidityScore(liquidityMetrics);
  const workingCapitalScore = calculateWorkingCapitalScore(workingCapitalMetrics);
  const profitabilityScore = calculateProfitabilityScore(profitabilityMetrics);
  const riskScore = calculateRiskScore(fxExposure, totalAssets);
  const complianceScore = calculateComplianceScore(complianceMetrics);

  // Calculate weighted overall score
  const overallScore = (
    liquidityScore * (WEIGHTS.liquidity / 100) +
    workingCapitalScore * (WEIGHTS.workingCapital / 100) +
    profitabilityScore * (WEIGHTS.profitability / 100) +
    riskScore * (WEIGHTS.risk / 100) +
    complianceScore * (WEIGHTS.compliance / 100)
  );

  // Calculate rate components
  const liquidityImpact = scoreToRateImpact(liquidityScore);
  const workingCapitalImpact = scoreToRateImpact(workingCapitalScore);
  const profitabilityImpact = scoreToRateImpact(profitabilityScore);
  const riskImpact = scoreToRateImpact(riskScore);
  const complianceImpact = scoreToRateImpact(complianceScore);

  // Calculate total risk premium
  const totalRiskPremium = (
    liquidityImpact * (WEIGHTS.liquidity / 100) +
    workingCapitalImpact * (WEIGHTS.workingCapital / 100) +
    profitabilityImpact * (WEIGHTS.profitability / 100) +
    riskImpact * (WEIGHTS.risk / 100) +
    complianceImpact * (WEIGHTS.compliance / 100)
  ) / 100; // Convert basis points to percentage

  const currentRate = BASE_RATE + totalRiskPremium;

  return {
    liquidityScore,
    workingCapitalScore,
    profitabilityScore,
    riskScore,
    complianceScore,
    overallScore,
    currentRate,
    baseRate: BASE_RATE,
    riskPremium: totalRiskPremium
  };
}

/**
 * Generate detailed rate breakdown
 */
export function generateRateBreakdown(rateFactors: RateFactors): RateBreakdown {
  const components: RateComponent[] = [
    {
      name: 'Liquidity Coverage',
      value: rateFactors.liquidityScore,
      weight: WEIGHTS.liquidity,
      impact: scoreToRateImpact(rateFactors.liquidityScore),
      description: 'Cash position and liquidity management'
    },
    {
      name: 'Working Capital Efficiency',
      value: rateFactors.workingCapitalScore,
      weight: WEIGHTS.workingCapital,
      impact: scoreToRateImpact(rateFactors.workingCapitalScore),
      description: 'DSO, DPO, and cash conversion cycle'
    },
    {
      name: 'Profitability & Growth',
      value: rateFactors.profitabilityScore,
      weight: WEIGHTS.profitability,
      impact: scoreToRateImpact(rateFactors.profitabilityScore),
      description: 'EBITDA margin and revenue growth'
    },
    {
      name: 'Risk Management',
      value: rateFactors.riskScore,
      weight: WEIGHTS.risk,
      impact: scoreToRateImpact(rateFactors.riskScore),
      description: 'FX exposure and operational risks'
    },
    {
      name: 'Compliance & Governance',
      value: rateFactors.complianceScore,
      weight: WEIGHTS.compliance,
      impact: scoreToRateImpact(rateFactors.complianceScore),
      description: 'Regulatory compliance and audit scores'
    }
  ];

  const potentialSavings = Math.max(0, (rateFactors.currentRate - INDUSTRY_BENCHMARK) * -1);

  return {
    baseRate: rateFactors.baseRate,
    riskPremium: rateFactors.riskPremium,
    components,
    totalRate: rateFactors.currentRate,
    industryBenchmark: INDUSTRY_BENCHMARK,
    potentialSavings
  };
}

/**
 * Calculate what-if scenarios
 */
export function calculateScenarios(
  currentFactors: RateFactors,
  loanAmount: number = 5000000
): ScenarioResult[] {
  const scenarios: ScenarioResult[] = [];

  // Scenario 1: Improve DSO by 10 days
  const dsoImprovement = { ...currentFactors };
  dsoImprovement.workingCapitalScore = Math.min(100, dsoImprovement.workingCapitalScore + 15);
  dsoImprovement.overallScore = (dsoImprovement.liquidityScore * 0.25 + dsoImprovement.workingCapitalScore * 0.20 + 
                                dsoImprovement.profitabilityScore * 0.25 + dsoImprovement.riskScore * 0.15 + 
                                dsoImprovement.complianceScore * 0.15);
  const newRateDSO = BASE_RATE + (scoreToRateImpact(dsoImprovement.overallScore) / 100);
  
  scenarios.push({
    scenarioName: 'Reduce DSO by 10 days',
    newRate: newRateDSO,
    rateDifference: newRateDSO - currentFactors.currentRate,
    annualSavings: (currentFactors.currentRate - newRateDSO) * loanAmount,
    kpiChanges: { dso: -10, workingCapitalScore: 15 }
  });

  // Scenario 2: Improve EBITDA margin by 5%
  const ebitdaImprovement = { ...currentFactors };
  ebitdaImprovement.profitabilityScore = Math.min(100, ebitdaImprovement.profitabilityScore + 20);
  ebitdaImprovement.overallScore = (ebitdaImprovement.liquidityScore * 0.25 + ebitdaImprovement.workingCapitalScore * 0.20 + 
                                   ebitdaImprovement.profitabilityScore * 0.25 + ebitdaImprovement.riskScore * 0.15 + 
                                   ebitdaImprovement.complianceScore * 0.15);
  const newRateEBITDA = BASE_RATE + (scoreToRateImpact(ebitdaImprovement.overallScore) / 100);
  
  scenarios.push({
    scenarioName: 'Increase EBITDA margin by 5%',
    newRate: newRateEBITDA,
    rateDifference: newRateEBITDA - currentFactors.currentRate,
    annualSavings: (currentFactors.currentRate - newRateEBITDA) * loanAmount,
    kpiChanges: { ebitdaMargin: 5, profitabilityScore: 20 }
  });

  // Scenario 3: Hedge FX exposure
  const fxHedging = { ...currentFactors };
  fxHedging.riskScore = Math.min(100, fxHedging.riskScore + 25);
  fxHedging.overallScore = (fxHedging.liquidityScore * 0.25 + fxHedging.workingCapitalScore * 0.20 + 
                           fxHedging.profitabilityScore * 0.25 + fxHedging.riskScore * 0.15 + 
                           fxHedging.complianceScore * 0.15);
  const newRateFX = BASE_RATE + (scoreToRateImpact(fxHedging.overallScore) / 100);
  
  scenarios.push({
    scenarioName: 'Hedge 80% of FX exposure',
    newRate: newRateFX,
    rateDifference: newRateFX - currentFactors.currentRate,
    annualSavings: (currentFactors.currentRate - newRateFX) * loanAmount,
    kpiChanges: { fxHedgeRatio: 80, riskScore: 25 }
  });

  return scenarios;
}
