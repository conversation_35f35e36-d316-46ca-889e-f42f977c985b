import React from 'react';
import { Shield, FileText, AlertTriangle, CheckCircle, Clock, Download } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import ModuleCard from '@/components/dashboard/ModuleCard';
import MetricWidget from '@/components/dashboard/MetricWidget';

const ComplianceDashboard: React.FC = () => {
  // Mock compliance data
  const complianceMetrics = {
    auditTrails: 1247,
    gdprRequests: 3,
    reportsDue: 2,
    complianceScore: 94.5
  };

  const recentAudits = [
    { id: 1, action: 'Loan Application Submitted', user: 'Nordström GreenTech AB', timestamp: '2024-01-15 14:32', status: 'completed' },
    { id: 2, action: 'Financial Document Uploaded', user: 'Nordström GreenTech AB', timestamp: '2024-01-15 14:28', status: 'completed' },
    { id: 3, action: 'BankID Verification', user: '<PERSON>', timestamp: '2024-01-15 14:25', status: 'completed' },
    { id: 4, action: 'Fortnox Data Sync', user: 'System', timestamp: '2024-01-15 13:45', status: 'completed' },
    { id: 5, action: 'GDPR Data Request', user: 'External Request', timestamp: '2024-01-15 12:15', status: 'in_progress' }
  ];

  const upcomingReports = [
    { name: 'Monthly Risk Assessment', dueDate: '2024-01-31', status: 'draft', priority: 'medium' },
    { name: 'GDPR Compliance Report', dueDate: '2024-02-15', status: 'not_started', priority: 'high' },
    { name: 'Financial Audit Trail', dueDate: '2024-02-28', status: 'in_progress', priority: 'low' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-success-600 bg-success-50';
      case 'in_progress': return 'text-yellow-600 bg-yellow-50';
      case 'pending': return 'text-gray-600 bg-gray-50';
      case 'draft': return 'text-blue-600 bg-blue-50';
      case 'not_started': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-error-600 bg-error-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-success-600 bg-success-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-display-sm">Compliance & Reporting</h1>
          <p className="text-body-lg text-gray-600 mt-2">
            Monitor regulatory compliance, audit trails, and automated reporting
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="btn-secondary">
            <Download className="h-4 w-4 mr-2" />
            Export Audit Log
          </Button>
          <Button className="btn-primary">
            Generate Report
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Audit Trail Entries"
          value={complianceMetrics.auditTrails}
          format="number"
          change={{
            value: 15.2,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={FileText}
        />

        <MetricWidget
          title="GDPR Requests"
          value={complianceMetrics.gdprRequests}
          format="number"
          change={{
            value: -1,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={Shield}
        />

        <MetricWidget
          title="Reports Due"
          value={complianceMetrics.reportsDue}
          format="number"
          icon={Clock}
        />

        <MetricWidget
          title="Compliance Score"
          value={complianceMetrics.complianceScore}
          format="percentage"
          change={{
            value: 2.1,
            period: 'vs last quarter',
            type: 'increase'
          }}
          icon={CheckCircle}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Audit Trail */}
        <ModuleCard
          title="Recent Audit Trail"
          description="Latest system activities and user actions"
          icon={FileText}
          headerActions={
            <Button variant="outline" size="sm" className="btn-secondary">
              View All
            </Button>
          }
        >
          <div className="space-y-3">
            {recentAudits.map((audit) => (
              <div key={audit.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="text-body-sm font-medium text-gray-900">
                    {audit.action}
                  </div>
                  <div className="text-body-xs text-gray-600 mt-1">
                    {audit.user} • {audit.timestamp}
                  </div>
                </div>
                <div className={`px-2 py-1 rounded text-body-xs font-medium ${getStatusColor(audit.status)}`}>
                  {audit.status.replace('_', ' ')}
                </div>
              </div>
            ))}
          </div>
        </ModuleCard>

        {/* Upcoming Reports */}
        <ModuleCard
          title="Upcoming Reports"
          description="Scheduled compliance and regulatory reports"
          icon={Clock}
          headerActions={
            <Button variant="outline" size="sm" className="btn-secondary">
              Schedule New
            </Button>
          }
        >
          <div className="space-y-3">
            {upcomingReports.map((report, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="text-body-sm font-medium text-gray-900">
                    {report.name}
                  </div>
                  <div className="text-body-xs text-gray-600 mt-1">
                    Due: {report.dueDate}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className={`px-2 py-1 rounded text-body-xs font-medium ${getPriorityColor(report.priority)}`}>
                    {report.priority}
                  </div>
                  <div className={`px-2 py-1 rounded text-body-xs font-medium ${getStatusColor(report.status)}`}>
                    {report.status.replace('_', ' ')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ModuleCard>
      </div>

      {/* GDPR Compliance */}
      <ModuleCard
        title="GDPR Compliance Status"
        description="Data protection and privacy compliance monitoring"
        icon={Shield}
        headerActions={
          <Button variant="outline" size="sm" className="btn-secondary">
            Privacy Settings
          </Button>
        }
      >
        <div className="space-y-6">
          {/* Compliance Checklist */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="text-heading-sm">Data Protection</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-success-600" />
                  <span className="text-body-sm text-gray-700">Data encryption at rest</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-success-600" />
                  <span className="text-body-sm text-gray-700">Data encryption in transit</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-success-600" />
                  <span className="text-body-sm text-gray-700">Access controls implemented</span>
                </div>
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="text-body-sm text-gray-700">Data retention policy review due</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="text-heading-sm">User Rights</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-success-600" />
                  <span className="text-body-sm text-gray-700">Right to access</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-success-600" />
                  <span className="text-body-sm text-gray-700">Right to rectification</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-success-600" />
                  <span className="text-body-sm text-gray-700">Right to erasure</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-success-600" />
                  <span className="text-body-sm text-gray-700">Data portability</span>
                </div>
              </div>
            </div>
          </div>

          {/* Recent GDPR Activities */}
          <div className="space-y-3">
            <h4 className="text-heading-sm">Recent GDPR Activities</h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-body-sm text-gray-700">Data export request processed</span>
                <span className="text-body-xs text-gray-500">2 days ago</span>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-body-sm text-gray-700">Privacy policy updated</span>
                <span className="text-body-xs text-gray-500">1 week ago</span>
              </div>
              <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                <span className="text-body-sm text-gray-700">Data retention review completed</span>
                <span className="text-body-xs text-gray-500">2 weeks ago</span>
              </div>
            </div>
          </div>
        </div>
      </ModuleCard>
    </div>
  );
};

export default ComplianceDashboard;
