-- Cleanup Demo Data Migration
-- Removes old "Demo Company AB" and ensures only Nordström GreenTech AB exists

-- Delete old Demo Company AB and all related data
DO $$
DECLARE
    old_company_id UUID;
BEGIN
    -- Find the old demo company
    SELECT id INTO old_company_id 
    FROM companies 
    WHERE organization_number = '559223-3208' OR company_name = 'Demo Company AB';
    
    IF old_company_id IS NOT NULL THEN
        -- Delete related data first (due to foreign key constraints)
        DELETE FROM voucher_rows WHERE voucher_id IN (
            SELECT id FROM vouchers WHERE company_id = old_company_id
        );
        DELETE FROM vouchers WHERE company_id = old_company_id;
        DELETE FROM chart_of_accounts WHERE company_id = old_company_id;
        DELETE FROM funding_metrics WHERE company_id = old_company_id;
        DELETE FROM treasury_data WHERE company_id = old_company_id;
        DELETE FROM growth_kpis WHERE company_id = old_company_id;
        DELETE FROM compliance_reports WHERE company_id = old_company_id;
        DELETE FROM audit_trail WHERE company_id = old_company_id;
        
        -- Finally delete the company
        DELETE FROM companies WHERE id = old_company_id;
        
        RAISE NOTICE 'Deleted old Demo Company AB and related data';
    END IF;
END $$;

-- Ensure Nordström GreenTech AB exists with correct data
INSERT INTO companies (
    organization_number, 
    company_name, 
    industry, 
    base_currency, 
    annual_revenue, 
    employee_count,
    sync_status,
    created_at,
    updated_at
) VALUES (
    '556123-4567', 
    'Nordström GreenTech AB', 
    'Wind Turbine Components Manufacturing', 
    'SEK', 
    *********, 
    50,
    'pending',
    NOW(),
    NOW()
) ON CONFLICT (organization_number) DO UPDATE SET
    company_name = EXCLUDED.company_name,
    industry = EXCLUDED.industry,
    base_currency = EXCLUDED.base_currency,
    annual_revenue = EXCLUDED.annual_revenue,
    employee_count = EXCLUDED.employee_count,
    updated_at = NOW();

-- Update funding metrics for Nordström GreenTech AB
INSERT INTO funding_metrics (
    company_id, 
    arr_current, 
    arr_previous_year, 
    arr_growth_rate, 
    nrr_percentage,
    gross_retention_rate, 
    ltv, 
    cac, 
    ltv_cac_ratio, 
    funding_score,
    period_start, 
    period_end,
    created_at,
    updated_at
) VALUES (
    (SELECT id FROM companies WHERE organization_number = '556123-4567'),
    *********, 
    *********, 
    25.0, 
    115.0, 
    92.5, 
    45000, 
    3500, 
    12.9, 
    8.7,
    '2024-01-01', 
    '2024-12-31',
    NOW(),
    NOW()
) ON CONFLICT (company_id, period_start, period_end) DO UPDATE SET
    arr_current = EXCLUDED.arr_current,
    arr_previous_year = EXCLUDED.arr_previous_year,
    arr_growth_rate = EXCLUDED.arr_growth_rate,
    nrr_percentage = EXCLUDED.nrr_percentage,
    gross_retention_rate = EXCLUDED.gross_retention_rate,
    ltv = EXCLUDED.ltv,
    cac = EXCLUDED.cac,
    ltv_cac_ratio = EXCLUDED.ltv_cac_ratio,
    funding_score = EXCLUDED.funding_score,
    updated_at = NOW();

-- Update treasury data for Nordström GreenTech AB
INSERT INTO treasury_data (
    company_id, 
    cash_position, 
    total_liquidity, 
    monthly_burn_rate, 
    runway_months,
    current_ratio, 
    quick_ratio, 
    reporting_date,
    created_at,
    updated_at
) VALUES (
    (SELECT id FROM companies WHERE organization_number = '556123-4567'),
    15750000, 
    16200000, 
    1200000, 
    13.1, 
    2.8, 
    2.4, 
    '2024-01-15',
    NOW(),
    NOW()
) ON CONFLICT (company_id, reporting_date) DO UPDATE SET
    cash_position = EXCLUDED.cash_position,
    total_liquidity = EXCLUDED.total_liquidity,
    monthly_burn_rate = EXCLUDED.monthly_burn_rate,
    runway_months = EXCLUDED.runway_months,
    current_ratio = EXCLUDED.current_ratio,
    quick_ratio = EXCLUDED.quick_ratio,
    updated_at = NOW();

-- Update growth KPIs for Nordström GreenTech AB
INSERT INTO growth_kpis (
    company_id, 
    dso_days, 
    dpo_days, 
    dio_days, 
    cash_conversion_cycle,
    inventory_turnover, 
    working_capital, 
    efficiency_score, 
    reporting_period,
    created_at,
    updated_at
) VALUES (
    (SELECT id FROM companies WHERE organization_number = '556123-4567'),
    35, 
    42, 
    38, 
    31, 
    9.5, 
    5200000, 
    8.8, 
    '2024-01-01',
    NOW(),
    NOW()
) ON CONFLICT (company_id, reporting_period) DO UPDATE SET
    dso_days = EXCLUDED.dso_days,
    dpo_days = EXCLUDED.dpo_days,
    dio_days = EXCLUDED.dio_days,
    cash_conversion_cycle = EXCLUDED.cash_conversion_cycle,
    inventory_turnover = EXCLUDED.inventory_turnover,
    working_capital = EXCLUDED.working_capital,
    efficiency_score = EXCLUDED.efficiency_score,
    updated_at = NOW();

-- Clean up duplicate exchange rates
DELETE FROM exchange_rates 
WHERE id NOT IN (
    SELECT DISTINCT ON (base_currency, target_currency, rate_date) id
    FROM exchange_rates
    ORDER BY base_currency, target_currency, rate_date, created_at DESC
);

-- Add audit log entry
INSERT INTO audit_trail (
    company_id,
    action_type,
    action_description,
    user_email,
    user_role,
    data_category,
    created_at
) VALUES (
    (SELECT id FROM companies WHERE organization_number = '556123-4567'),
    'data_cleanup',
    'Cleaned up demo data and ensured only Nordström GreenTech AB exists',
    '<EMAIL>',
    'system',
    'company_data',
    NOW()
);
