import React from 'react';

const FXExposureChart: React.FC = () => {
  // Mock FX exposure data
  const data = [
    { currency: 'EUR', amount: 1200000, percentage: 51.3, risk: 'Medium' },
    { currency: 'USD', amount: 800000, percentage: 34.2, risk: 'Low' },
    { currency: 'NOK', amount: 240000, percentage: 10.3, risk: 'High' },
    { currency: 'DKK', amount: 100000, percentage: 4.2, risk: 'Low' }
  ];

  const total = data.reduce((sum, item) => sum + item.amount, 0);
  
  const colors = ['#111827', '#374151', '#6b7280', '#9ca3af'];
  
  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'High': return 'text-error-600 bg-error-50';
      case 'Medium': return 'text-yellow-600 bg-yellow-50';
      case 'Low': return 'text-success-600 bg-success-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  // Calculate pie chart segments
  let cumulativePercentage = 0;
  const segments = data.map((item, index) => {
    const startAngle = (cumulativePercentage / 100) * 360;
    const endAngle = ((cumulativePercentage + item.percentage) / 100) * 360;
    cumulativePercentage += item.percentage;
    
    const startAngleRad = (startAngle - 90) * (Math.PI / 180);
    const endAngleRad = (endAngle - 90) * (Math.PI / 180);
    
    const largeArcFlag = item.percentage > 50 ? 1 : 0;
    
    const x1 = 100 + 80 * Math.cos(startAngleRad);
    const y1 = 100 + 80 * Math.sin(startAngleRad);
    const x2 = 100 + 80 * Math.cos(endAngleRad);
    const y2 = 100 + 80 * Math.sin(endAngleRad);
    
    const pathData = [
      `M 100 100`,
      `L ${x1} ${y1}`,
      `A 80 80 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ');
    
    return {
      ...item,
      pathData,
      color: colors[index % colors.length]
    };
  });

  return (
    <div className="w-full h-full flex flex-col">
      {/* Chart Area */}
      <div className="flex-1 flex items-center justify-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center w-full">
          {/* Pie Chart */}
          <div className="flex justify-center">
            <svg width="200" height="200" viewBox="0 0 200 200">
              {segments.map((segment, index) => (
                <path
                  key={index}
                  d={segment.pathData}
                  fill={segment.color}
                  className="hover:opacity-80 transition-opacity cursor-pointer"
                />
              ))}
              {/* Center circle */}
              <circle cx="100" cy="100" r="40" fill="white" />
              <text x="100" y="95" textAnchor="middle" className="text-sm font-semibold fill-gray-900">
                Total
              </text>
              <text x="100" y="110" textAnchor="middle" className="text-xs fill-gray-600">
                {formatCurrency(total)}
              </text>
            </svg>
          </div>

          {/* Legend and Details */}
          <div className="space-y-3">
            {data.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: colors[index % colors.length] }}
                  ></div>
                  <div>
                    <div className="text-body-md font-medium text-gray-900">
                      {item.currency}
                    </div>
                    <div className="text-body-sm text-gray-600">
                      {formatCurrency(item.amount)} ({item.percentage}%)
                    </div>
                  </div>
                </div>
                <div className={`px-2 py-1 rounded text-body-xs font-medium ${getRiskColor(item.risk)}`}>
                  {item.risk} Risk
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Summary */}
      <div className="pt-4 border-t border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-heading-md font-semibold text-gray-900">
              {data.length}
            </div>
            <div className="text-body-sm text-gray-600">Currencies</div>
          </div>
          <div className="text-center">
            <div className="text-heading-md font-semibold text-error-600">
              {data.filter(d => d.risk === 'High').length}
            </div>
            <div className="text-body-sm text-gray-600">High Risk</div>
          </div>
          <div className="text-center">
            <div className="text-heading-md font-semibold text-gray-900">
              {((data.find(d => d.currency === 'EUR')?.percentage || 0) + 
                (data.find(d => d.currency === 'USD')?.percentage || 0)).toFixed(1)}%
            </div>
            <div className="text-body-sm text-gray-600">Major Currencies</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FXExposureChart;
