/**
 * BAS 2025 Compliant Chart of Accounts for Nordström GreenTech AB
 * Based on official Swedish BAS 2025 structure from bas.se
 * Tailored for wind turbine component manufacturing business
 */

export interface BASAccount {
  account_code: string;
  account_name: string;
  account_type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  bas_category: string;
  is_balance_sheet: boolean;
  is_income_statement: boolean;
  parent_account?: string;
  description?: string;
}

// BAS 2025 Chart of Accounts for Manufacturing Company
export const bas2025Accounts: BASAccount[] = [
  // ASSETS (1000-1999) - Balance Sheet
  
  // Current Assets - Cash and Bank
  {
    account_code: '1910',
    account_name: '<PERSON>ssa',
    account_type: 'asset',
    bas_category: 'Kassa och bank',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Kontanter i kassan'
  },
  {
    account_code: '1930',
    account_name: 'Handelsbanken Företagskonto',
    account_type: 'asset',
    bas_category: 'Kassa och bank',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Huvudsakligt företagskonto i SEK'
  },
  {
    account_code: '1931',
    account_name: 'SEB Valutakonto EUR',
    account_type: 'asset',
    bas_category: 'Kassa och bank',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Valutakonto för EUR-transaktioner'
  },
  {
    account_code: '1932',
    account_name: 'SEB Valutakonto USD',
    account_type: 'asset',
    bas_category: 'Kassa och bank',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Valutakonto för USD-transaktioner'
  },
  {
    account_code: '1933',
    account_name: 'Nordea Valutakonto NOK',
    account_type: 'asset',
    bas_category: 'Kassa och bank',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Valutakonto för NOK-transaktioner'
  },

  // Current Assets - Accounts Receivable
  {
    account_code: '1510',
    account_name: 'Kundfordringar',
    account_type: 'asset',
    bas_category: 'Kortfristiga fordringar',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Utestående kundfordringar'
  },
  {
    account_code: '1513',
    account_name: 'Kundfordringar EUR',
    account_type: 'asset',
    bas_category: 'Kortfristiga fordringar',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Kundfordringar i EUR'
  },
  {
    account_code: '1514',
    account_name: 'Kundfordringar USD',
    account_type: 'asset',
    bas_category: 'Kortfristiga fordringar',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Kundfordringar i USD'
  },
  {
    account_code: '1515',
    account_name: 'Kundfordringar NOK',
    account_type: 'asset',
    bas_category: 'Kortfristiga fordringar',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Kundfordringar i NOK'
  },

  // Current Assets - Inventory
  {
    account_code: '1410',
    account_name: 'Råmaterial och förnödenheter',
    account_type: 'asset',
    bas_category: 'Varulager',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Stål, kompositmaterial och andra råvaror'
  },
  {
    account_code: '1420',
    account_name: 'Produkter i arbete',
    account_type: 'asset',
    bas_category: 'Varulager',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Halvfabrikat och pågående tillverkning'
  },
  {
    account_code: '1430',
    account_name: 'Färdiga varor',
    account_type: 'asset',
    bas_category: 'Varulager',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Färdiga turbinkomponenter'
  },

  // Fixed Assets
  {
    account_code: '1220',
    account_name: 'Maskiner och andra tekniska anläggningar',
    account_type: 'asset',
    bas_category: 'Materiella anläggningstillgångar',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Produktionsmaskiner och utrustning'
  },
  {
    account_code: '1250',
    account_name: 'Inventarier, verktyg och installationer',
    account_type: 'asset',
    bas_category: 'Materiella anläggningstillgångar',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Kontorsinventarier och verktyg'
  },

  // LIABILITIES (2000-2999) - Balance Sheet
  
  // Current Liabilities
  {
    account_code: '2010',
    account_name: 'Leverantörsskulder',
    account_type: 'liability',
    bas_category: 'Kortfristiga skulder',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Skulder till leverantörer'
  },
  {
    account_code: '2013',
    account_name: 'Leverantörsskulder EUR',
    account_type: 'liability',
    bas_category: 'Kortfristiga skulder',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Skulder till leverantörer i EUR'
  },
  {
    account_code: '2014',
    account_name: 'Leverantörsskulder USD',
    account_type: 'liability',
    bas_category: 'Kortfristiga skulder',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Skulder till leverantörer i USD'
  },

  // Tax Liabilities
  {
    account_code: '2440',
    account_name: 'Utgående moms',
    account_type: 'liability',
    bas_category: 'Kortfristiga skulder',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Moms att betala till Skatteverket'
  },
  {
    account_code: '2450',
    account_name: 'Ingående moms',
    account_type: 'asset',
    bas_category: 'Kortfristiga fordringar',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Moms att få tillbaka från Skatteverket'
  },

  // Payroll Liabilities
  {
    account_code: '2710',
    account_name: 'Personalskatt',
    account_type: 'liability',
    bas_category: 'Kortfristiga skulder',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Preliminärskatt och sociala avgifter'
  },
  {
    account_code: '2730',
    account_name: 'Avräkning för sociala avgifter',
    account_type: 'liability',
    bas_category: 'Kortfristiga skulder',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Arbetsgivaravgifter att betala'
  },

  // EQUITY (2000-2999) - Balance Sheet
  {
    account_code: '2010',
    account_name: 'Aktiekapital',
    account_type: 'equity',
    bas_category: 'Eget kapital',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Registrerat aktiekapital'
  },
  {
    account_code: '2091',
    account_name: 'Balanserad vinst eller förlust',
    account_type: 'equity',
    bas_category: 'Eget kapital',
    is_balance_sheet: true,
    is_income_statement: false,
    description: 'Ackumulerat resultat från tidigare år'
  },

  // REVENUE (3000-3999) - Income Statement
  {
    account_code: '3010',
    account_name: 'Försäljning inom Sverige',
    account_type: 'revenue',
    bas_category: 'Nettoomsättning',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Försäljning till svenska kunder'
  },
  {
    account_code: '3011',
    account_name: 'Försäljning inom EU',
    account_type: 'revenue',
    bas_category: 'Nettoomsättning',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Försäljning till EU-länder'
  },
  {
    account_code: '3012',
    account_name: 'Försäljning utanför EU',
    account_type: 'revenue',
    bas_category: 'Nettoomsättning',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Export till länder utanför EU'
  },

  // Other Operating Revenue
  {
    account_code: '3740',
    account_name: 'Valutakursvinster',
    account_type: 'revenue',
    bas_category: 'Övriga rörelseintäkter',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Realiserade och orealiserade valutakursvinster'
  },

  // EXPENSES (4000-8999) - Income Statement
  
  // Cost of Goods Sold
  {
    account_code: '4010',
    account_name: 'Inköp av råmaterial och förnödenheter',
    account_type: 'expense',
    bas_category: 'Rörelsens kostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Inköp av stål och andra råmaterial'
  },
  {
    account_code: '4011',
    account_name: 'Inköp stål från Tyskland (EUR)',
    account_type: 'expense',
    bas_category: 'Rörelsens kostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Specialstål från tyska leverantörer'
  },
  {
    account_code: '4012',
    account_name: 'Inköp kompositmaterial från Kina (USD)',
    account_type: 'expense',
    bas_category: 'Rörelsens kostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Kolfiberkomposit från kinesiska leverantörer'
  },

  // Personnel Costs
  {
    account_code: '5010',
    account_name: 'Löner till arbetare',
    account_type: 'expense',
    bas_category: 'Personalkostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Löner till produktionspersonal'
  },
  {
    account_code: '5020',
    account_name: 'Löner till tjänstemän',
    account_type: 'expense',
    bas_category: 'Personalkostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Löner till kontorspersonal och chefer'
  },
  {
    account_code: '5410',
    account_name: 'Sociala avgifter',
    account_type: 'expense',
    bas_category: 'Personalkostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Arbetsgivaravgifter enligt lag'
  },

  // Other Operating Expenses
  {
    account_code: '6110',
    account_name: 'Kontorsmaterial',
    account_type: 'expense',
    bas_category: 'Övriga externa kostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Kontorsmaterial och förbrukningsmaterial'
  },
  {
    account_code: '6250',
    account_name: 'Transportkostnader',
    account_type: 'expense',
    bas_category: 'Övriga externa kostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Frakt och transportkostnader'
  },
  {
    account_code: '6540',
    account_name: 'IT-kostnader',
    account_type: 'expense',
    bas_category: 'Övriga externa kostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Programvaror och IT-tjänster'
  },

  // Financial Costs
  {
    account_code: '7740',
    account_name: 'Valutakursförluster',
    account_type: 'expense',
    bas_category: 'Finansiella kostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Realiserade och orealiserade valutakursförluster'
  },
  {
    account_code: '8310',
    account_name: 'Ränteintäkter',
    account_type: 'revenue',
    bas_category: 'Finansiella intäkter',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Räntor på banktillgodohavanden'
  },
  {
    account_code: '8410',
    account_name: 'Räntekostnader',
    account_type: 'expense',
    bas_category: 'Finansiella kostnader',
    is_balance_sheet: false,
    is_income_statement: true,
    description: 'Räntor på lån och krediter'
  }
];

/**
 * Get account type based on BAS 2025 account code
 */
export function getAccountTypeFromCode(accountCode: string): string {
  const code = parseInt(accountCode);
  
  if (code >= 1000 && code <= 1999) return 'asset';
  if (code >= 2000 && code <= 2999) {
    // Special handling for equity accounts in 2000 range
    if (code >= 2010 && code <= 2099) return 'equity';
    return 'liability';
  }
  if (code >= 3000 && code <= 3999) return 'revenue';
  if (code >= 4000 && code <= 8999) return 'expense';
  
  return 'expense'; // Default
}

/**
 * Check if account is balance sheet account
 */
export function isBalanceSheetAccount(accountCode: string): boolean {
  const code = parseInt(accountCode);
  return code >= 1000 && code <= 2999;
}

/**
 * Check if account is income statement account
 */
export function isIncomeStatementAccount(accountCode: string): boolean {
  const code = parseInt(accountCode);
  return code >= 3000 && code <= 8999;
}

/**
 * Get BAS category for account
 */
export function getBASCategory(accountCode: string): string {
  const account = bas2025Accounts.find(acc => acc.account_code === accountCode);
  return account?.bas_category || 'Övriga kostnader';
}
