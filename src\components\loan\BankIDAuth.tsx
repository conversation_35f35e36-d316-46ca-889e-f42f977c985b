
import React, { useState, useEffect } from 'react';
import { Loader2, CheckCircle, XCircle, AlertTriangle, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import {
  initiateBankIDAuth,
  pollBankIDStatus,
  cancelBankIDAuth,
  BankIDStatus,
  BankIDResponse
} from '@/lib/bankid';

interface BankIDAuthProps {
  onSuccess: (data: BankIDResponse) => void;
  onCancel: () => void;
  personalNumber?: string;
  className?: string;
}

const BankIDAuth: React.FC<BankIDAuthProps> = ({
  onSuccess,
  onCancel,
  personalNumber: initialPersonalNumber,
  className,
}) => {
  const [personalNumber, setPersonalNumber] = useState(initialPersonalNumber || '');
  const [status, setStatus] = useState<BankIDStatus | null>(null);
  const [orderRef, setOrderRef] = useState<string | null>(null);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [pollingInterval, setPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // Start BankID authentication
  const startAuthentication = async () => {
    try {
      setStatus(BankIDStatus.PENDING);
      setError(null);

      const response = await initiateBankIDAuth(personalNumber);

      if (response.status === BankIDStatus.PENDING) {
        setOrderRef(response.orderRef!);
        setQrCode(response.qrCode || null);
        startPolling(response.orderRef!);
      } else {
        setError('Failed to initiate BankID authentication');
        setStatus(BankIDStatus.FAILED);
      }
    } catch (error) {
      console.error('Error starting BankID authentication:', error);
      setError('An unexpected error occurred');
      setStatus(BankIDStatus.FAILED);
    }
  };

  // Start polling for BankID status
  const startPolling = (ref: string) => {
    // Clear any existing polling interval
    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    // Poll every 2 seconds
    const interval = setInterval(async () => {
      try {
        const response = await pollBankIDStatus(ref);

        if (response.status === BankIDStatus.COMPLETE) {
          clearInterval(interval);
          setStatus(BankIDStatus.COMPLETE);
          onSuccess(response);
        } else if (
          response.status === BankIDStatus.FAILED ||
          response.status === BankIDStatus.CANCELLED ||
          response.status === BankIDStatus.TIMEOUT
        ) {
          clearInterval(interval);
          setStatus(response.status);
          setError(response.error || 'Authentication failed');
        }
      } catch (error) {
        console.error('Error polling BankID status:', error);
        clearInterval(interval);
        setStatus(BankIDStatus.FAILED);
        setError('Failed to check authentication status');
      }
    }, 2000);

    setPollingInterval(interval);
  };

  // Cancel BankID authentication
  const cancelAuthentication = async () => {
    if (orderRef) {
      await cancelBankIDAuth(orderRef);
    }

    if (pollingInterval) {
      clearInterval(pollingInterval);
    }

    setStatus(null);
    setOrderRef(null);
    setQrCode(null);
    setError(null);
    onCancel();
  };

  // Clean up polling interval when component unmounts
  useEffect(() => {
    return () => {
      if (pollingInterval) {
        clearInterval(pollingInterval);
      }
    };
  }, [pollingInterval]);

  return (
    <Card className={cn("overflow-hidden border-gray-300", className)}>
      <div className="bg-primary-gradient text-white p-6">
        <div className="text-center space-y-2">
          <h3 className="text-heading-3 font-semibold">BankID Authentication</h3>
          <p className="text-body-small opacity-90">
            Verify your identity using BankID
          </p>
        </div>
      </div>
      <CardContent className="p-6 space-y-6">

        {status === null && (
          <div className="space-y-6">
            <div className="space-y-3">
              <Label htmlFor="personalNumber" className="text-body font-medium">Personal Number (optional)</Label>
              <Input
                id="personalNumber"
                placeholder="YYYYMMDD-XXXX"
                value={personalNumber}
                onChange={(e) => setPersonalNumber(e.target.value)}
                className="h-12"
              />
              <p className="text-body-small text-gray-700">
                Enter your personal number to pre-fill it in BankID
              </p>
            </div>

            <div className="pt-2">
              <Button
                onClick={startAuthentication}
                className="w-full h-12"
                size="lg"
              >
                <Shield className="mr-2 h-5 w-5" />
                Start BankID Authentication
              </Button>
            </div>
          </div>
        )}

        {status === BankIDStatus.PENDING && (
          <div className="space-y-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="w-20 h-20 rounded-full bg-primary-light flex items-center justify-center">
                <Loader2 className="h-10 w-10 animate-spin text-primary" />
              </div>
              <div className="text-center">
                <p className="text-heading-4 font-semibold text-gray-900">Waiting for BankID...</p>
                <p className="text-body text-gray-700 mt-2">
                  Open your BankID app to authenticate
                </p>
              </div>
            </div>

            {qrCode && (
              <div className="flex flex-col items-center space-y-3 bg-gray-50 p-6 rounded-lg border border-gray-300">
                <p className="text-body font-medium text-gray-900">Or scan this QR code:</p>
                <img
                  src={qrCode}
                  alt="BankID QR Code"
                  className="w-48 h-48 border-2 border-primary-light rounded-lg shadow-sm"
                />
              </div>
            )}

            <Button
              variant="outline"
              onClick={cancelAuthentication}
              className="w-full h-12"
              size="lg"
            >
              Cancel
            </Button>
          </div>
        )}

        {status === BankIDStatus.COMPLETE && (
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="w-20 h-20 rounded-full bg-success/10 flex items-center justify-center">
              <CheckCircle className="h-10 w-10 text-success" />
            </div>
            <div className="text-center">
              <p className="text-heading-4 font-semibold text-gray-900">Authentication Successful</p>
              <p className="text-body text-gray-700 mt-2">
                Your identity has been verified
              </p>
            </div>
          </div>
        )}

        {(status === BankIDStatus.FAILED ||
          status === BankIDStatus.CANCELLED ||
          status === BankIDStatus.TIMEOUT) && (
          <div className="space-y-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className={cn(
                "w-20 h-20 rounded-full flex items-center justify-center",
                status === BankIDStatus.CANCELLED ? "bg-gray-100" : "bg-error/10"
              )}>
                {status === BankIDStatus.CANCELLED ? (
                  <XCircle className="h-10 w-10 text-gray-700" />
                ) : (
                  <AlertTriangle className="h-10 w-10 text-error" />
                )}
              </div>
              <div className="text-center">
                <p className="text-heading-4 font-semibold text-gray-900">
                  {status === BankIDStatus.CANCELLED
                    ? 'Authentication Cancelled'
                    : status === BankIDStatus.TIMEOUT
                    ? 'Authentication Timed Out'
                    : 'Authentication Failed'}
                </p>
                {error && (
                  <p className="text-body text-error mt-2">{error}</p>
                )}
              </div>
            </div>

            <div className="space-y-3">
              <Button onClick={startAuthentication} className="w-full h-12" size="lg">
                Try Again
              </Button>
              <Button
                variant="outline"
                onClick={cancelAuthentication}
                className="w-full h-12"
                size="lg"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BankIDAuth;
