import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Lightbulb, 
  TrendingUp, 
  Clock, 
  DollarSign,
  Target,
  Activity,
  Shield,
  ArrowRight,
  CheckCircle,
  AlertTriangle,
  Star
} from 'lucide-react';
import {
  RateFactors,
  LiquidityMetrics,
  WorkingCapitalMetrics,
  ProfitabilityMetrics
} from '@/utils/kpiCalculations';

interface Recommendation {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
  timeline: string;
  potentialSavings: number;
  category: 'liquidity' | 'working-capital' | 'profitability' | 'risk';
  icon: React.ComponentType<{ className?: string }>;
  steps: string[];
}

interface RecommendationsPanelProps {
  rateFactors: RateFactors;
  liquidityMetrics: LiquidityMetrics;
  workingCapitalMetrics: WorkingCapitalMetrics;
  profitabilityMetrics: ProfitabilityMetrics;
}

const RecommendationsPanel: React.FC<RecommendationsPanelProps> = ({
  rateFactors,
  liquidityMetrics,
  workingCapitalMetrics,
  profitabilityMetrics
}) => {
  const generateRecommendations = (): Recommendation[] => {
    const recommendations: Recommendation[] = [];

    // Working Capital Recommendations
    if (rateFactors.workingCapitalScore < 75) {
      recommendations.push({
        id: 'dso-optimization',
        title: 'Optimize Days Sales Outstanding (DSO)',
        description: `Your current DSO of ${workingCapitalMetrics.dso} days is above optimal. Reducing it to 25-30 days could improve your rate.`,
        impact: 'high',
        effort: 'medium',
        timeline: '2-3 months',
        potentialSavings: 0.5,
        category: 'working-capital',
        icon: Clock,
        steps: [
          'Implement early payment discounts (2-3% for 10-day payments)',
          'Automate invoice follow-ups at 15, 30, and 45 days',
          'Review and tighten credit terms for new customers',
          'Consider factoring for large receivables'
        ]
      });
    }

    if (workingCapitalMetrics.dpo < 35) {
      recommendations.push({
        id: 'dpo-extension',
        title: 'Extend Days Payable Outstanding (DPO)',
        description: `Your DPO of ${workingCapitalMetrics.dpo} days could be optimized. Negotiating better payment terms could improve cash flow.`,
        impact: 'medium',
        effort: 'low',
        timeline: '1-2 months',
        potentialSavings: 0.3,
        category: 'working-capital',
        icon: Activity,
        steps: [
          'Negotiate extended payment terms with key suppliers',
          'Consolidate suppliers to increase bargaining power',
          'Implement supplier financing programs',
          'Review all vendor contracts for payment optimization'
        ]
      });
    }

    // Liquidity Recommendations
    if (rateFactors.liquidityScore < 80) {
      recommendations.push({
        id: 'cash-optimization',
        title: 'Optimize Cash Management',
        description: 'Improve cash visibility and forecasting accuracy to strengthen your liquidity position.',
        impact: 'high',
        effort: 'medium',
        timeline: '1-2 months',
        potentialSavings: 0.4,
        category: 'liquidity',
        icon: DollarSign,
        steps: [
          'Implement 13-week rolling cash flow forecasts',
          'Set up automated cash sweeping between accounts',
          'Establish committed credit facilities for backup liquidity',
          'Optimize cash allocation across currencies'
        ]
      });
    }

    // Profitability Recommendations
    if (rateFactors.profitabilityScore < 80) {
      recommendations.push({
        id: 'margin-improvement',
        title: 'Improve EBITDA Margin',
        description: `Your EBITDA margin of ${profitabilityMetrics.ebitdaMargin.toFixed(1)}% has room for improvement. Focus on high-margin activities.`,
        impact: 'high',
        effort: 'high',
        timeline: '3-6 months',
        potentialSavings: 0.8,
        category: 'profitability',
        icon: TrendingUp,
        steps: [
          'Analyze product/service profitability by segment',
          'Eliminate or improve low-margin offerings',
          'Automate processes to reduce operational costs',
          'Implement value-based pricing strategies'
        ]
      });
    }

    // Risk Management Recommendations
    if (rateFactors.riskScore < 75) {
      recommendations.push({
        id: 'fx-hedging',
        title: 'Implement FX Risk Management',
        description: 'Your foreign exchange exposure could be better managed through hedging strategies.',
        impact: 'medium',
        effort: 'medium',
        timeline: '1 month',
        potentialSavings: 0.6,
        category: 'risk',
        icon: Shield,
        steps: [
          'Assess total FX exposure by currency',
          'Implement forward contracts for 60-80% of exposure',
          'Set up natural hedging where possible',
          'Monitor and adjust hedging positions monthly'
        ]
      });
    }

    // Always include a general recommendation
    recommendations.push({
      id: 'integration-optimization',
      title: 'Enhance ERP Integration',
      description: 'Deeper integration with your Fortnox system could provide more accurate real-time data for better rates.',
      impact: 'medium',
      effort: 'low',
      timeline: '2-4 weeks',
      potentialSavings: 0.2,
      category: 'liquidity',
      icon: Target,
      steps: [
        'Enable additional Fortnox API endpoints',
        'Set up real-time data synchronization',
        'Implement automated KPI calculations',
        'Configure alerts for key metric changes'
      ]
    });

    return recommendations.sort((a, b) => {
      const impactWeight = { high: 3, medium: 2, low: 1 };
      const effortWeight = { low: 3, medium: 2, high: 1 };
      
      const scoreA = impactWeight[a.impact] + effortWeight[a.effort];
      const scoreB = impactWeight[b.impact] + effortWeight[b.effort];
      
      return scoreB - scoreA;
    });
  };

  const recommendations = generateRecommendations();

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getEffortColor = (effort: string) => {
    switch (effort) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'liquidity': return DollarSign;
      case 'working-capital': return Activity;
      case 'profitability': return TrendingUp;
      case 'risk': return Shield;
      default: return Target;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Lightbulb className="h-5 w-5 mr-2" />
            Personalized Recommendations
          </CardTitle>
          <p className="text-sm text-gray-600">
            AI-powered suggestions to optimize your KPIs and reduce your interest rate
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{recommendations.length}</div>
              <div className="text-sm text-gray-600">Active Recommendations</div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {recommendations.reduce((sum, rec) => sum + rec.potentialSavings, 0).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Total Potential Rate Reduction</div>
            </div>
            
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {(recommendations.reduce((sum, rec) => sum + rec.potentialSavings, 0) * 5000000 / 100).toLocaleString('sv-SE')} SEK
              </div>
              <div className="text-sm text-gray-600">Annual Savings Potential</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations List */}
      <div className="space-y-4">
        {recommendations.map((recommendation, index) => {
          const CategoryIcon = getCategoryIcon(recommendation.category);
          
          return (
            <Card key={recommendation.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <CategoryIcon className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{recommendation.title}</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">{recommendation.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className={getImpactColor(recommendation.impact)}>
                      {recommendation.impact} impact
                    </Badge>
                    <Badge variant="outline" className={getEffortColor(recommendation.effort)}>
                      {recommendation.effort} effort
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Metrics */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">
                      -{recommendation.potentialSavings.toFixed(1)}%
                    </div>
                    <div className="text-xs text-gray-600">Rate Reduction</div>
                  </div>
                  
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">
                      {recommendation.timeline}
                    </div>
                    <div className="text-xs text-gray-600">Timeline</div>
                  </div>
                  
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-semibold text-gray-900">
                      {(recommendation.potentialSavings * 50000).toLocaleString('sv-SE')} SEK
                    </div>
                    <div className="text-xs text-gray-600">Annual Savings</div>
                  </div>
                </div>

                {/* Implementation Steps */}
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Implementation Steps:</h4>
                  <div className="space-y-2">
                    {recommendation.steps.map((step, stepIndex) => (
                      <div key={stepIndex} className="flex items-start space-x-3">
                        <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-medium text-blue-600">{stepIndex + 1}</span>
                        </div>
                        <span className="text-sm text-gray-600">{step}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm text-gray-600">Priority #{index + 1}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      Learn More
                    </Button>
                    <Button size="sm">
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Start Implementation
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Summary Card */}
      <Card className="bg-gradient-to-r from-blue-50 to-green-50">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Ready to Optimize Your Rate?
              </h3>
              <p className="text-sm text-gray-600">
                Implementing these recommendations could reduce your rate by up to{' '}
                <span className="font-semibold text-green-600">
                  {recommendations.reduce((sum, rec) => sum + rec.potentialSavings, 0).toFixed(1)}%
                </span>
                {' '}annually, saving you{' '}
                <span className="font-semibold text-green-600">
                  {(recommendations.reduce((sum, rec) => sum + rec.potentialSavings, 0) * 50000).toLocaleString('sv-SE')} SEK
                </span>
                {' '}per year on a 5M loan.
              </p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <ArrowRight className="h-4 w-4 mr-2" />
              Get Started
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RecommendationsPanel;
