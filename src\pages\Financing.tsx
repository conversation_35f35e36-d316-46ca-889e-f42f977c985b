import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  TrendingUp,
  Building,
  DollarSign,
  ArrowUpRight,
  ArrowRight,
  CheckCircle
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Financing = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="pt-36 pb-20 px-6 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-refined mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div className="text-left space-y-6">
              <Badge variant="subtle" className="mb-2 text-xs px-3 py-1 font-medium">
                Financing Solutions
              </Badge>
              <h1 className="text-heading-1 text-gray-900 leading-tight">
                Strategic Capital for Sweden's Growth Leaders
              </h1>
              <p className="text-body-large text-gray-700 max-w-xl leading-relaxed">
                Tailored financing solutions that adapt to your business trajectory, backed by advanced financial intelligence.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <a href="/loan-application">
                  <Button size="lg" className="h-12 px-6 text-sm font-medium bg-primary hover:bg-primary/90 transition-colors">
                    Apply for Financing
                  </Button>
                </a>
                <a href="/contact">
                  <Button size="lg" variant="outline" className="h-12 px-6 text-sm font-medium border-gray-300 text-gray-700 hover:bg-gray-50">
                    Talk to Sales
                  </Button>
                </a>
              </div>
            </div>
            <div className="relative hidden md:block">
              <div className="relative rounded-2xl overflow-hidden shadow-lg">
                <img
                  src="https://images.unsplash.com/photo-1560520653-9e0e4c89eb11?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1073&q=80"
                  alt="Business financing"
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Financing Options Section */}
      <section className="py-section px-6 bg-white">
        <div className="container-refined mx-auto">
          <div className="max-w-3xl mb-16">
            <Badge variant="subtle" className="mb-4 text-xs px-3 py-1 font-medium">Our Primary Offering</Badge>
            <h2 className="text-heading-2 text-gray-900 mb-4">
              Financing Tailored to Your Business Trajectory
            </h2>
            <p className="text-body-large text-gray-700">
              Our data-driven approach means faster decisions and terms that reflect your true business potential.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardContent className="p-8">
                <div className="bg-primary-light w-16 h-16 rounded-full flex items-center justify-center mb-6">
                  <TrendingUp className="text-primary" size={24} />
                </div>
                <h3 className="text-heading-3 text-gray-900 mb-3">Growth Capital</h3>
                <p className="text-body text-gray-700 mb-6">
                  Secure €500K-€5M in growth financing with flexible terms aligned to your business cycle. Our data-driven underwriting means faster decisions and terms that reflect your true business potential.
                </p>
                <div className="p-4 bg-gray-50 rounded-lg border border-gray-100 mb-6">
                  <div className="flex items-center">
                    <ArrowUpRight className="text-primary mr-2 flex-shrink-0" size={16} />
                    <span className="text-sm font-medium text-gray-800">Flexible repayment schedules based on revenue performance</span>
                  </div>
                </div>
                <a href="/loan-application">
                  <Button className="w-full">Apply Now</Button>
                </a>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-8">
                <div className="bg-primary-light w-16 h-16 rounded-full flex items-center justify-center mb-6">
                  <DollarSign className="text-primary" size={24} />
                </div>
                <h3 className="text-heading-3 text-gray-900 mb-3">Working Capital</h3>
                <p className="text-body text-gray-700 mb-6">
                  Optimize cash flow with our working capital facilities designed for medium-sized enterprises. Access €250K-€2M with streamlined approval processes and competitive rates.
                </p>
                <div className="p-4 bg-gray-50 rounded-lg border border-gray-100 mb-6">
                  <div className="flex items-center">
                    <ArrowUpRight className="text-primary mr-2 flex-shrink-0" size={16} />
                    <span className="text-sm font-medium text-gray-800">No hidden fees or complex covenants</span>
                  </div>
                </div>
                <a href="/loan-application">
                  <Button className="w-full">Apply Now</Button>
                </a>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-8">
                <div className="bg-primary-light w-16 h-16 rounded-full flex items-center justify-center mb-6">
                  <Building className="text-primary" size={24} />
                </div>
                <h3 className="text-heading-3 text-gray-900 mb-3">Expansion Financing</h3>
                <p className="text-body text-gray-700 mb-6">
                  Fund your next market expansion with structured capital solutions that preserve equity and optimize your balance sheet. Customized terms for established businesses with proven models.
                </p>
                <div className="p-4 bg-gray-50 rounded-lg border border-gray-100 mb-6">
                  <div className="flex items-center">
                    <ArrowUpRight className="text-primary mr-2 flex-shrink-0" size={16} />
                    <span className="text-sm font-medium text-gray-800">Combines elements of debt and equity for optimal capital structure</span>
                  </div>
                </div>
                <a href="/loan-application">
                  <Button className="w-full">Apply Now</Button>
                </a>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-section px-6 bg-gray-50">
        <div className="container-refined mx-auto">
          <div className="max-w-3xl mb-16">
            <h2 className="text-heading-2 text-gray-900 mb-4">
              Why Choose Arcim Financing
            </h2>
            <p className="text-body-large text-gray-700">
              Our approach to business financing is different from traditional lenders.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-lg shadow-subtle">
              <div className="bg-primary-light w-14 h-14 rounded-full flex items-center justify-center mb-6">
                <CheckCircle className="text-primary" size={20} />
              </div>
              <h3 className="text-heading-4 text-gray-900 mb-3">Data-Driven Decisions</h3>
              <p className="text-body text-gray-700">
                We use advanced analytics to understand your business better, resulting in faster approvals and more accurate risk assessment.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-subtle">
              <div className="bg-primary-light w-14 h-14 rounded-full flex items-center justify-center mb-6">
                <CheckCircle className="text-primary" size={20} />
              </div>
              <h3 className="text-heading-4 text-gray-900 mb-3">Flexible Terms</h3>
              <p className="text-body text-gray-700">
                Our financing solutions adapt to your business cycle with customizable repayment schedules that align with your cash flow.
              </p>
            </div>

            <div className="bg-white p-8 rounded-lg shadow-subtle">
              <div className="bg-primary-light w-14 h-14 rounded-full flex items-center justify-center mb-6">
                <CheckCircle className="text-primary" size={20} />
              </div>
              <h3 className="text-heading-4 text-gray-900 mb-3">Beyond Capital</h3>
              <p className="text-body text-gray-700">
                We provide more than just financing - our platform offers insights and analytics to help optimize your business operations.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-section px-6 bg-primary">
        <div className="container-refined mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-heading-2 text-white mb-6">
              Ready to fuel your business growth?
            </h2>
            <p className="text-body-large text-white/80 mb-8">
              Apply today and get a decision within 48 hours.
            </p>
            <a href="/loan-application">
              <Button size="lg" variant="secondary" className="bg-white text-primary hover:bg-gray-50">
                Start Your Application
              </Button>
            </a>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Financing;
