/* Import Inter font - clean and professional like treyd.io */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Treyd.io-inspired color system - clean and minimal */
    --background: 0 0% 100%;
    --foreground: 220 13% 7%;

    /* Card colors */
    --card: 0 0% 100%;
    --card-foreground: 220 13% 7%;

    /* Popover colors */
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 7%;

    /* Primary colors - pure black like treyd.io */
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    /* Secondary colors */
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 13% 7%;

    /* Muted colors */
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    /* Accent colors */
    --accent: 0 0% 0%;
    --accent-foreground: 0 0% 100%;

    /* Destructive colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Border, input, and ring colors */
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 0 0% 0%;

    /* Border radius */
    --radius: 0.375rem;

    /* Sidebar colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 220 13% 7%;
    --foreground: 0 0% 100%;

    --card: 220 13% 7%;
    --card-foreground: 0 0% 100%;

    --popover: 220 13% 7%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 0% 100%;
    --primary-foreground: 220 13% 7%;

    --secondary: 220 13% 15%;
    --secondary-foreground: 0 0% 100%;

    --muted: 220 13% 15%;
    --muted-foreground: 220 9% 65%;

    --accent: 0 0% 100%;
    --accent-foreground: 220 13% 7%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 15%;
    --input: 220 13% 15%;
    --ring: 0 0% 100%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    @apply scroll-smooth;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  body {
    @apply bg-white text-gray-900 font-sans antialiased;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }

  /* Treyd.io-inspired typography hierarchy */
  h1 {
    @apply text-display-lg text-gray-900 font-semibold tracking-tight;
  }

  h2 {
    @apply text-display-md text-gray-900 font-semibold tracking-tight;
  }

  h3 {
    @apply text-heading-xl text-gray-900 font-semibold;
  }

  h4 {
    @apply text-heading-lg text-gray-900 font-semibold;
  }

  h5 {
    @apply text-heading-md text-gray-900 font-semibold;
  }

  h6 {
    @apply text-heading-sm text-gray-900 font-semibold;
  }

  p {
    @apply text-body-md text-gray-600;
  }

  a {
    @apply transition-colors duration-200 hover:text-gray-900;
  }

  /* Clean focus styles like treyd.io */
  *:focus-visible {
    @apply outline-none ring-2 ring-gray-900 ring-offset-2 ring-offset-white;
  }

  /* Remove default button styles */
  button {
    @apply focus:outline-none;
  }
}

/* Treyd.io-inspired component styles */
@layer components {
  /* Clean button styles matching treyd.io */
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-label-lg font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-gray-900 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-white;
  }

  .btn-primary {
    @apply bg-gray-900 text-white hover:bg-gray-800 shadow-button hover:shadow-button-hover;
  }

  .btn-secondary {
    @apply bg-white text-gray-900 border border-gray-300 hover:bg-gray-50 shadow-button hover:shadow-button-hover;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-900 hover:bg-gray-50 shadow-button hover:shadow-button-hover;
  }

  .btn-ghost {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-50;
  }

  /* Clean card styles */
  .card-clean {
    @apply bg-white rounded-lg border border-gray-200 shadow-card transition-all duration-200 hover:shadow-card-hover;
  }

  /* Section styles with proper spacing */
  .section {
    @apply py-section;
  }

  .section-sm {
    @apply py-section-sm;
  }

  .section-xs {
    @apply py-section-xs;
  }

  /* Container styles */
  .container-clean {
    @apply container mx-auto max-w-7xl px-6 lg:px-8;
  }

  /* Form styles matching treyd.io */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply text-label-lg text-gray-900 font-medium;
  }

  .form-input {
    @apply w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-body-md text-gray-900 placeholder:text-gray-500 focus:border-gray-900 focus:outline-none focus:ring-1 focus:ring-gray-900 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .form-textarea {
    @apply w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-body-md text-gray-900 placeholder:text-gray-500 focus:border-gray-900 focus:outline-none focus:ring-1 focus:ring-gray-900 disabled:cursor-not-allowed disabled:opacity-50 resize-none;
  }

  /* Navigation styles */
  .nav-link {
    @apply text-body-md text-gray-600 hover:text-gray-900 transition-colors duration-200;
  }

  .nav-link-active {
    @apply text-body-md text-gray-900 font-medium;
  }

  /* Hero section styles */
  .hero-title {
    @apply text-display-xl font-semibold text-gray-900 tracking-tight;
  }

  .hero-subtitle {
    @apply text-body-xl text-gray-600 max-w-3xl mx-auto;
  }

  /* Feature card styles */
  .feature-card {
    @apply bg-white rounded-lg border border-gray-200 p-6 shadow-card hover:shadow-card-hover transition-all duration-200;
  }

  .feature-icon {
    @apply w-12 h-12 rounded-lg bg-gray-100 flex items-center justify-center mb-4;
  }

  /* Utility classes for consistent spacing */
  .space-y-section > * + * {
    margin-top: theme('spacing.section');
  }

  .space-y-section-sm > * + * {
    margin-top: theme('spacing.section-sm');
  }
}