import React from 'react';

const ARRGrowthChart: React.FC = () => {
  // Mock data for demonstration
  const data = [
    { month: 'Jan', arr: 8500000, growth: 12.5 },
    { month: 'Feb', arr: 9200000, growth: 15.2 },
    { month: 'Mar', arr: 9800000, growth: 18.1 },
    { month: 'Apr', arr: 10500000, growth: 22.3 },
    { month: 'May', arr: 11200000, growth: 28.7 },
    { month: 'Jun', arr: 11800000, growth: 32.1 },
    { month: 'Jul', arr: 12100000, growth: 35.4 },
    { month: 'Aug', arr: 12500000, growth: 38.9 },
    { month: 'Sep', arr: 12800000, growth: 42.1 },
    { month: 'Oct', arr: 13200000, growth: 45.2 },
    { month: 'Nov', arr: 13600000, growth: 48.3 },
    { month: 'Dec', arr: 14000000, growth: 51.7 }
  ];

  const maxARR = Math.max(...data.map(d => d.arr));
  const minARR = Math.min(...data.map(d => d.arr));
  const range = maxARR - minARR;

  return (
    <div className="w-full h-full flex flex-col">
      {/* Chart Area */}
      <div className="flex-1 relative">
        <svg className="w-full h-full" viewBox="0 0 800 300">
          {/* Grid Lines */}
          <defs>
            <pattern id="grid" width="80" height="60" patternUnits="userSpaceOnUse">
              <path d="M 80 0 L 0 0 0 60" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          {/* Y-axis labels */}
          <g className="text-xs fill-gray-500">
            <text x="30" y="50" textAnchor="end">14M</text>
            <text x="30" y="110" textAnchor="end">12M</text>
            <text x="30" y="170" textAnchor="end">10M</text>
            <text x="30" y="230" textAnchor="end">8M</text>
          </g>
          
          {/* ARR Line */}
          <path
            d={`M ${data.map((d, i) => `${60 + i * 60},${280 - ((d.arr - minARR) / range) * 200}`).join(' L ')}`}
            fill="none"
            stroke="#111827"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data Points */}
          {data.map((d, i) => (
            <circle
              key={i}
              cx={60 + i * 60}
              cy={280 - ((d.arr - minARR) / range) * 200}
              r="4"
              fill="#111827"
              className="hover:r-6 transition-all cursor-pointer"
            />
          ))}
          
          {/* X-axis labels */}
          <g className="text-xs fill-gray-500">
            {data.map((d, i) => (
              <text key={i} x={60 + i * 60} y="295" textAnchor="middle">
                {d.month}
              </text>
            ))}
          </g>
        </svg>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-900 rounded-full"></div>
            <span className="text-body-sm text-gray-600">ARR Growth</span>
          </div>
        </div>
        <div className="text-body-sm text-gray-500">
          Last updated: {new Date().toLocaleDateString('sv-SE')}
        </div>
      </div>
    </div>
  );
};

export default ARRGrowthChart;
