# Loan Application Feature

This document provides an overview of the loan application feature, including its components, functionality, and security measures.

## Overview

The loan application feature allows Swedish medium-sized firms and enterprises to apply for loans through a secure, GDPR-compliant form. The application includes:

- Swedish organization number validation
- BankID authentication
- Dynamic form with conditional rendering
- File upload with drag-and-drop functionality
- GDPR-compliant data handling

## Components

### 1. Organization Number Validation

- Uses the `organisationsnummer` npm library to validate Swedish organization numbers
- Automatically detects company type based on organization number patterns
- Provides real-time validation feedback

### 2. BankID Authentication

- Integrates with Signicat's BankID API for secure authentication
- Verifies that the person submitting is authorized
- Handles authentication responses and error states
- Securely stores authentication tokens

### 3. Dynamic Form UI

- Conditionally renders form fields based on company type
- Shows VAT number field only for VAT-registered companies
- Implements progressive disclosure of form sections
- Provides a multi-step form experience

### 4. File Upload

- Drag-and-drop functionality for financial documents
- Supports PDF and Excel file formats
- Validates file size (maximum 10MB per file)
- Shows upload progress and previews

### 5. Supabase Integration

- Stores application data in a `finance_applications` table
- Encrypts PII fields using pgcrypto extension
- Implements Row-Level Security (RLS) policies
- Configures storage buckets for document storage

## GDPR Compliance

### Data Encryption

PII data is encrypted using pgcrypto extension in the database:
- Contact person name
- Contact person email
- Contact person phone
- Personal identity number (personnummer)

### Consent Management

The application includes a comprehensive consent management system:
- Data processing consent
- Credit check consent
- Terms and conditions acceptance
- Optional marketing communications consent

### Data Retention

- Data is stored for the duration of the loan application process
- If approved, data is retained for the duration of the loan plus 7 years
- Data marked for deletion is permanently deleted after the retention period

### User Rights

The application supports GDPR user rights:
- Right to access
- Right to rectification
- Right to erasure
- Right to restrict processing
- Right to data portability

## Security Measures

### Database Security

- Row-Level Security (RLS) policies restrict access to authorized users
- PII data is encrypted using pgcrypto
- Proper indexing for efficient queries

### Storage Security

- Dedicated buckets for document storage
- RLS policies restricting access to authorized users
- Automatic file expiration for documents that are no longer needed

### Authentication Security

- BankID provides strong authentication
- Session management with secure token storage
- Verification of authorized representatives

## Implementation Details

### Database Schema

The `finance_applications` table includes:
- Company information
- Contact person information (encrypted)
- Loan details
- Document references
- BankID verification data
- Consent flags
- Application status

### API Endpoints

The application uses Supabase client library for:
- Creating applications
- Updating applications
- Submitting applications
- Uploading documents

## Future Enhancements

Potential future enhancements include:
- Integration with credit scoring APIs
- Automated document analysis
- Application status tracking
- Email notifications
- Admin dashboard for loan officers
