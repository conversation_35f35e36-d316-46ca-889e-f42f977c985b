import { z } from 'zod';
import Organisationsnummer from 'organisationsnummer';

/**
 * Company types based on Swedish organization number patterns
 */
export enum CompanyType {
  AKTIEBOLAG = 'Aktiebolag',
  HANDELSBOLAG = 'Handelsbolag',
  ENSKILD_FIRMA = 'Enskild firma',
  EKONOMISK_FORENING = 'Ekonomisk förening',
  IDEELL_FORENING = 'Ideell förening',
  STIFTELSE = 'Stiftelse',
  UNKNOWN = 'Unknown'
}

/**
 * Detects company type based on organization number
 * @param orgNumber - Swedish organization number
 * @returns CompanyType enum value
 */
export const detectCompanyType = (orgNumber: string): CompanyType => {
  if (!orgNumber || !Organisationsnummer.valid(orgNumber)) {
    return CompanyType.UNKNOWN;
  }

  // Format the organization number to ensure consistent format
  const formatted = new Organisationsnummer(orgNumber).format();

  // Extract the third digit which often indicates company type
  const thirdDigit = formatted.charAt(2);

  // Check for specific patterns
  if (thirdDigit === '5' || thirdDigit === '6') {
    return CompanyType.AKTIEBOLAG;
  } else if (thirdDigit === '9') {
    return CompanyType.HANDELSBOLAG;
  } else if (thirdDigit === '1' || thirdDigit === '2') {
    return CompanyType.ENSKILD_FIRMA;
  } else if (thirdDigit === '7') {
    return CompanyType.EKONOMISK_FORENING;
  } else if (thirdDigit === '8') {
    return CompanyType.IDEELL_FORENING;
  } else if (thirdDigit === '3') {
    return CompanyType.STIFTELSE;
  }

  return CompanyType.UNKNOWN;
};

/**
 * Validates and formats a Swedish organization number
 * @param orgNumber - Swedish organization number to validate
 * @returns Formatted organization number or null if invalid
 */
export const validateOrganizationNumber = (orgNumber: string): string | null => {
  try {
    if (Organisationsnummer.valid(orgNumber)) {
      return new Organisationsnummer(orgNumber).format();
    }
    return null;
  } catch (error) {
    return null;
  }
};

/**
 * Zod schema for Swedish organization number validation
 */
export const organizationNumberSchema = z.string()
  .min(1, 'Organization number is required')
  .refine(
    (val) => Organisationsnummer.valid(val),
    {
      message: 'Invalid Swedish organization number',
    }
  )
  .transform((val) => new Organisationsnummer(val).format());

/**
 * Zod schema for validating file uploads
 */
export const fileSchema = z.object({
  file: z.instanceof(File)
    .refine((file) => file.size <= 10 * 1024 * 1024, {
      message: 'File size must be less than 10MB',
    })
    .refine(
      (file) => ['application/pdf', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].includes(file.type),
      {
        message: 'Only PDF and Excel files are allowed',
      }
    ),
});

/**
 * Zod schema for validating multiple file uploads
 */
export const filesSchema = z.array(fileSchema)
  .max(5, 'Maximum 5 files can be uploaded');

/**
 * Zod schema for validating Swedish personal identity numbers (personnummer)
 */
export const personnummerSchema = z.string()
  .min(1, 'Personal identity number is required')
  .regex(/^(19|20)?(\d{6}(-|\s)\d{4}|\d{10})$/, 'Invalid format for personal identity number')
  .refine(
    (val) => {
      // Basic validation - in a real implementation, use a dedicated library
      // This is a simplified check
      const cleaned = val.replace(/[^0-9]/g, '');
      if (cleaned.length !== 10 && cleaned.length !== 12) return false;

      // Extract the last 10 digits if 12 digits are provided
      const digits = cleaned.length === 12 ? cleaned.substring(2) : cleaned;

      // Check the checksum (Luhn algorithm)
      let sum = 0;
      for (let i = 0; i < 9; i++) {
        let val = parseInt(digits.charAt(i));
        if (i % 2 === 0) {
          val *= 2;
          if (val > 9) val -= 9;
        }
        sum += val;
      }

      const checksum = (10 - (sum % 10)) % 10;
      return checksum === parseInt(digits.charAt(9));
    },
    {
      message: 'Invalid Swedish personal identity number',
    }
  );

/**
 * Password validation schema with security requirements
 */
export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters long')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character');

/**
 * Email validation schema
 */
export const emailSchema = z.string()
  .min(1, 'Email is required')
  .email('Invalid email address');

/**
 * Mobile phone validation schema for Swedish numbers
 */
export const mobileSchema = z.string()
  .min(1, 'Mobile number is required')
  .regex(/^(\+46|0)7[0-9]{8}$/, 'Invalid Swedish mobile number format');

/**
 * OTP validation schema
 */
export const otpSchema = z.string()
  .length(6, 'OTP must be exactly 6 digits')
  .regex(/^\d{6}$/, 'OTP must contain only numbers');
