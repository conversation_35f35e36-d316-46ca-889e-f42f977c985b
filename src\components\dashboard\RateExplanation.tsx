import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Info, 
  CheckCircle, 
  AlertTriangle,
  XCircle
} from 'lucide-react';
import { RateBreakdown, RateFactors } from '@/utils/rateCalculations';

interface RateExplanationProps {
  rateBreakdown: RateBreakdown;
  rateFactors: RateFactors;
}

const RateExplanation: React.FC<RateExplanationProps> = ({ rateBreakdown, rateFactors }) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  const getImpactIcon = (impact: number) => {
    if (impact < -50) return <TrendingDown className="h-4 w-4 text-green-600" />;
    if (impact > 50) return <TrendingUp className="h-4 w-4 text-red-600" />;
    return <CheckCircle className="h-4 w-4 text-gray-600" />;
  };

  const formatBasisPoints = (bp: number) => {
    const percentage = Math.abs(bp) / 100;
    const sign = bp < 0 ? '-' : '+';
    return `${sign}${percentage.toFixed(2)}%`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <DollarSign className="h-5 w-5 mr-2" />
          Why Your Rate is {rateBreakdown.totalRate.toFixed(2)}%
        </CardTitle>
        <p className="text-sm text-gray-600">
          Your interest rate is calculated transparently based on your actual business performance
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Rate Composition */}
        <div className="space-y-4">
          <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
            <span className="font-medium">Swedish Base Rate</span>
            <span className="font-semibold">{rateBreakdown.baseRate.toFixed(2)}%</span>
          </div>
          
          <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
            <span className="font-medium">Your Risk Premium</span>
            <span className={`font-semibold ${rateBreakdown.riskPremium >= 0 ? 'text-red-600' : 'text-green-600'}`}>
              {rateBreakdown.riskPremium >= 0 ? '+' : ''}{rateBreakdown.riskPremium.toFixed(2)}%
            </span>
          </div>
          
          <div className="border-t pt-3">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold">Your Total Rate</span>
              <span className="text-xl font-bold text-blue-600">
                {rateBreakdown.totalRate.toFixed(2)}%
              </span>
            </div>
          </div>
        </div>

        {/* Performance Breakdown */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900">Performance Breakdown</h4>
          
          {rateBreakdown.components.map((component, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium">{component.name}</span>
                  <Badge variant={getScoreBadgeVariant(component.value)}>
                    {component.value.toFixed(0)}/100
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  {getImpactIcon(component.impact)}
                  <span className={`text-sm font-medium ${component.impact < 0 ? 'text-green-600' : component.impact > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                    {formatBasisPoints(component.impact)}
                  </span>
                </div>
              </div>
              
              <Progress 
                value={component.value} 
                className="h-2"
              />
              
              <p className="text-xs text-gray-500">{component.description}</p>
            </div>
          ))}
        </div>

        {/* Comparison with Industry */}
        <div className="space-y-3 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-900">Industry Comparison</h4>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {rateBreakdown.totalRate.toFixed(2)}%
              </div>
              <div className="text-sm text-gray-600">Your Rate</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {rateBreakdown.industryBenchmark.toFixed(2)}%
              </div>
              <div className="text-sm text-gray-600">Industry Average</div>
            </div>
          </div>
          
          <div className="text-center">
            {rateBreakdown.totalRate < rateBreakdown.industryBenchmark ? (
              <div className="flex items-center justify-center space-x-2 text-green-600">
                <TrendingDown className="h-4 w-4" />
                <span className="text-sm font-medium">
                  You're paying {(rateBreakdown.industryBenchmark - rateBreakdown.totalRate).toFixed(2)}% 
                  less than industry average
                </span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2 text-red-600">
                <TrendingUp className="h-4 w-4" />
                <span className="text-sm font-medium">
                  You're paying {(rateBreakdown.totalRate - rateBreakdown.industryBenchmark).toFixed(2)}% 
                  more than industry average
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Key Insights */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-900">Key Insights</h4>
          
          <div className="space-y-2">
            {rateFactors.liquidityScore >= 80 && (
              <div className="flex items-start space-x-2 text-green-700">
                <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Excellent liquidity position is reducing your rate by up to 1.5%
                </span>
              </div>
            )}
            
            {rateFactors.workingCapitalScore < 60 && (
              <div className="flex items-start space-x-2 text-yellow-700">
                <AlertTriangle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Improving working capital efficiency could reduce your rate by 0.5-1.0%
                </span>
              </div>
            )}
            
            {rateFactors.profitabilityScore >= 75 && (
              <div className="flex items-start space-x-2 text-green-700">
                <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  Strong profitability metrics are keeping your rate competitive
                </span>
              </div>
            )}
            
            {rateFactors.riskScore < 70 && (
              <div className="flex items-start space-x-2 text-red-700">
                <XCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  High risk exposure is adding 1.0-2.5% to your rate
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Rate Update Information */}
        <div className="p-3 bg-blue-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <p className="font-medium mb-1">Automatic Rate Updates</p>
              <p>
                Your rate is recalculated monthly based on your latest financial data. 
                Improvements in your KPIs automatically result in better rates.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RateExplanation;
