import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Arrow<PERSON>pRight,
  CheckCircle,
  TrendingUp,
  Bar<PERSON>hart3
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Platform = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative pt-32 pb-20 px-6 bg-gradient-to-br from-white via-blue-50/10 to-white overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 right-0 w-1/2 h-full opacity-10 bg-[url('https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80')] bg-cover bg-center"></div>
          <div className="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-white to-transparent"></div>
        </div>
        
        <div className="container relative mx-auto">
          <div className="max-w-3xl mx-auto text-center">
            <Badge variant="outline" className="mb-4 bg-blue-50 text-blue-600 border-blue-200 text-sm px-4 py-1">Financial Intelligence Platform</Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Insights That Power Better Decisions
            </h1>
            <p className="text-xl text-gray-700 mb-8 max-w-2xl mx-auto">
              Our proprietary analytics platform provides insights that benefit both your business operations and your financing relationship with Arcim.
            </p>
          </div>
        </div>
      </section>
      
      {/* Platform Features Section */}
      <section className="py-24 px-6 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Powerful Financial Intelligence Tools
            </h2>
            <p className="text-xl text-gray-700 max-w-2xl mx-auto">
              Our platform combines advanced analytics with intuitive interfaces to give you actionable insights.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <div className="space-y-8">
                <div className="flex gap-4">
                  <div className="bg-blue-50 p-3 rounded-lg shadow-sm border border-gray-200">
                    <LineChart className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Cash Flow Forecasting</h3>
                    <p className="text-gray-700">
                      Gain 90-day visibility into your financial future with AI-powered cash flow predictions that help you make proactive decisions.
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <div className="bg-blue-50 p-3 rounded-lg shadow-sm border border-gray-200">
                    <BarChart className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Performance Benchmarking</h3>
                    <p className="text-gray-700">
                      Compare key metrics against similar companies in your sector, identifying opportunities for operational improvement.
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <div className="bg-blue-50 p-3 rounded-lg shadow-sm border border-gray-200">
                    <PieChart className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Scenario Planning</h3>
                    <p className="text-gray-700">
                      Model different business scenarios and understand their financial implications before making critical decisions.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="absolute -top-10 -right-10 w-[300px] h-[300px] bg-blue-600/10 rounded-full opacity-50 blur-3xl"></div>
              <div className="relative bg-white p-6 rounded-xl shadow-xl border border-gray-200">
                <div className="bg-gray-900 p-4 rounded-t-lg">
                  <div className="flex items-center justify-between">
                    <h3 className="text-white font-medium">Financial Dashboard</h3>
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Cash Flow Forecast</h4>
                    <div className="h-40 bg-gray-100 rounded-lg flex items-center justify-center">
                      <div className="w-full px-4">
                        <div className="flex items-end h-24 gap-1">
                          <div className="w-1/12 bg-blue-600 h-1/3 rounded-t"></div>
                          <div className="w-1/12 bg-blue-600 h-1/2 rounded-t"></div>
                          <div className="w-1/12 bg-blue-600 h-2/3 rounded-t"></div>
                          <div className="w-1/12 bg-blue-600 h-1/2 rounded-t"></div>
                          <div className="w-1/12 bg-blue-600 h-3/4 rounded-t"></div>
                          <div className="w-1/12 bg-blue-600 h-full rounded-t"></div>
                          <div className="w-1/12 bg-blue-300 h-3/4 rounded-t"></div>
                          <div className="w-1/12 bg-blue-300 h-1/2 rounded-t"></div>
                          <div className="w-1/12 bg-blue-300 h-2/3 rounded-t"></div>
                          <div className="w-1/12 bg-blue-300 h-1/3 rounded-t"></div>
                          <div className="w-1/12 bg-blue-300 h-1/2 rounded-t"></div>
                          <div className="w-1/12 bg-blue-300 h-2/3 rounded-t"></div>
                        </div>
                        <div className="flex justify-between mt-2 text-xs text-gray-500">
                          <span>Jan</span>
                          <span>Dec</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <h4 className="text-xs font-medium text-gray-500">Working Capital</h4>
                      <p className="text-xl font-bold text-gray-900">€1.2M</p>
                      <div className="flex items-center text-green-600 text-xs">
                        <ArrowUpRight size={12} className="mr-1" />
                        <span>+12% vs last month</span>
                      </div>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <h4 className="text-xs font-medium text-gray-500">Cash Runway</h4>
                      <p className="text-xl font-bold text-gray-900">9.5 mo</p>
                      <div className="flex items-center text-green-600 text-xs">
                        <ArrowUpRight size={12} className="mr-1" />
                        <span>+2.3 mo vs last quarter</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      
      {/* Benefits Section */}
      <section className="py-20 px-6 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-4">
            Optimize Cash Flow, Manage Risk, Make Smarter Decisions
          </h2>
          <p className="text-xl text-blue-100 mb-12 max-w-3xl mx-auto">
            Our platform helps companies future-proof their financial operations with data-driven insights, not gut feelings.
          </p>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-white/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <TrendingUp className="text-white" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Optimize Cash Flow</h3>
              <p className="text-blue-100">
                Real-time visibility into cash positions with predictive forecasting to optimize working capital management.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-white/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="text-white" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Manage Risk</h3>
              <p className="text-blue-100">
                Comprehensive risk assessment tools with automated monitoring and alerts for proactive risk management.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-white/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <BarChart3 className="text-white" size={32} />
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Data-Driven Decisions</h3>
              <p className="text-blue-100">
                Advanced analytics and AI-powered insights to support strategic financial decisions backed by data.
              </p>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 px-6 bg-white">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">
              Ready to transform your financial operations?
            </h2>
            <p className="text-xl text-gray-700 mb-8">
              Book a demo to see how our platform can help your business achieve its financial goals.
            </p>
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
              Request a Demo
            </Button>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default Platform;
