export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      chart_of_accounts: {
        Row: {
          account_code: string
          account_name: string
          account_type: string
          company_id: string | null
          created_at: string | null
          fortnox_account_id: string | null
          id: string
          is_active: boolean | null
          parent_account_code: string | null
          updated_at: string | null
        }
        Insert: {
          account_code: string
          account_name: string
          account_type: string
          company_id?: string | null
          created_at?: string | null
          fortnox_account_id?: string | null
          id?: string
          is_active?: boolean | null
          parent_account_code?: string | null
          updated_at?: string | null
        }
        Update: {
          account_code?: string
          account_name?: string
          account_type?: string
          company_id?: string | null
          created_at?: string | null
          fortnox_account_id?: string | null
          id?: string
          is_active?: boolean | null
          parent_account_code?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chart_of_accounts_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
      companies: {
        Row: {
          annual_revenue: number | null
          base_currency: string | null
          company_name: string
          created_at: string | null
          employee_count: number | null
          fortnox_access_token: string | null
          fortnox_company_id: string | null
          fortnox_refresh_token: string | null
          fortnox_token_expires_at: string | null
          id: string
          industry: string | null
          last_sync_at: string | null
          organization_number: string
          sync_status: string | null
          updated_at: string | null
        }
        Insert: {
          annual_revenue?: number | null
          base_currency?: string | null
          company_name: string
          created_at?: string | null
          employee_count?: number | null
          fortnox_access_token?: string | null
          fortnox_company_id?: string | null
          fortnox_refresh_token?: string | null
          fortnox_token_expires_at?: string | null
          id?: string
          industry?: string | null
          last_sync_at?: string | null
          organization_number: string
          sync_status?: string | null
          updated_at?: string | null
        }
        Update: {
          annual_revenue?: number | null
          base_currency?: string | null
          company_name?: string
          created_at?: string | null
          employee_count?: number | null
          fortnox_access_token?: string | null
          fortnox_company_id?: string | null
          fortnox_refresh_token?: string | null
          fortnox_token_expires_at?: string | null
          id?: string
          industry?: string | null
          last_sync_at?: string | null
          organization_number?: string
          sync_status?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      exchange_rates: {
        Row: {
          base_currency: string
          created_at: string | null
          id: string
          rate: number
          rate_date: string
          source: string | null
          target_currency: string
        }
        Insert: {
          base_currency: string
          created_at?: string | null
          id?: string
          rate: number
          rate_date: string
          source?: string | null
          target_currency: string
        }
        Update: {
          base_currency?: string
          created_at?: string | null
          id?: string
          rate?: number
          rate_date?: string
          source?: string | null
          target_currency?: string
        }
        Relationships: []
      }
      finance_applications: {
        Row: {
          bankid_verification_date: string | null
          bankid_verification_id: string | null
          company_name: string
          company_type: string
          consent_credit_check: boolean
          consent_data_processing: boolean
          consent_marketing: boolean
          consent_terms: boolean
          contact_person_email: string
          contact_person_name: string
          contact_person_personnummer: string
          contact_person_phone: string
          contact_person_role: string
          created_at: string
          deletion_date: string | null
          financial_documents: string[] | null
          has_vat_registration: boolean
          id: string
          loan_amount: number
          loan_purpose: string
          loan_term_months: number
          organization_number: string
          rejection_reason: string | null
          status: string
          updated_at: string
          user_id: string | null
          vat_number: string | null
        }
        Insert: {
          bankid_verification_date?: string | null
          bankid_verification_id?: string | null
          company_name: string
          company_type: string
          consent_credit_check?: boolean
          consent_data_processing?: boolean
          consent_marketing?: boolean
          consent_terms?: boolean
          contact_person_email: string
          contact_person_name: string
          contact_person_personnummer: string
          contact_person_phone: string
          contact_person_role: string
          created_at?: string
          deletion_date?: string | null
          financial_documents?: string[] | null
          has_vat_registration?: boolean
          id?: string
          loan_amount: number
          loan_purpose: string
          loan_term_months: number
          organization_number: string
          rejection_reason?: string | null
          status?: string
          updated_at?: string
          user_id?: string | null
          vat_number?: string | null
        }
        Update: {
          bankid_verification_date?: string | null
          bankid_verification_id?: string | null
          company_name?: string
          company_type?: string
          consent_credit_check?: boolean
          consent_data_processing?: boolean
          consent_marketing?: boolean
          consent_terms?: boolean
          contact_person_email?: string
          contact_person_name?: string
          contact_person_personnummer?: string
          contact_person_phone?: string
          contact_person_role?: string
          created_at?: string
          deletion_date?: string | null
          financial_documents?: string[] | null
          has_vat_registration?: boolean
          id?: string
          loan_amount?: number
          loan_purpose?: string
          loan_term_months?: number
          organization_number?: string
          rejection_reason?: string | null
          status?: string
          updated_at?: string
          user_id?: string | null
          vat_number?: string | null
        }
        Relationships: []
      }
      voucher_rows: {
        Row: {
          account_code: string
          account_name: string | null
          cost_center: string | null
          created_at: string | null
          credit_amount: number | null
          currency_code: string | null
          debit_amount: number | null
          description: string | null
          exchange_rate: number | null
          id: string
          project_code: string | null
          row_number: number
          voucher_id: string | null
        }
        Insert: {
          account_code: string
          account_name?: string | null
          cost_center?: string | null
          created_at?: string | null
          credit_amount?: number | null
          currency_code?: string | null
          debit_amount?: number | null
          description?: string | null
          exchange_rate?: number | null
          id?: string
          project_code?: string | null
          row_number: number
          voucher_id?: string | null
        }
        Update: {
          account_code?: string
          account_name?: string | null
          cost_center?: string | null
          created_at?: string | null
          credit_amount?: number | null
          currency_code?: string | null
          debit_amount?: number | null
          description?: string | null
          exchange_rate?: number | null
          id?: string
          project_code?: string | null
          row_number?: number
          voucher_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voucher_rows_voucher_id_fkey"
            columns: ["voucher_id"]
            isOneToOne: false
            referencedRelation: "vouchers"
            referencedColumns: ["id"]
          },
        ]
      }
      vouchers: {
        Row: {
          company_id: string | null
          created_at: string | null
          currency_code: string | null
          description: string | null
          exchange_rate: number | null
          fortnox_url: string | null
          fortnox_voucher_id: string | null
          id: string
          sync_status: string | null
          total_amount: number
          transaction_date: string
          updated_at: string | null
          voucher_number: number
          voucher_series: string
        }
        Insert: {
          company_id?: string | null
          created_at?: string | null
          currency_code?: string | null
          description?: string | null
          exchange_rate?: number | null
          fortnox_url?: string | null
          fortnox_voucher_id?: string | null
          id?: string
          sync_status?: string | null
          total_amount: number
          transaction_date: string
          updated_at?: string | null
          voucher_number: number
          voucher_series: string
        }
        Update: {
          company_id?: string | null
          created_at?: string | null
          currency_code?: string | null
          description?: string | null
          exchange_rate?: number | null
          fortnox_url?: string | null
          fortnox_voucher_id?: string | null
          id?: string
          sync_status?: string | null
          total_amount?: number
          transaction_date?: string
          updated_at?: string | null
          voucher_number?: number
          voucher_series?: string
        }
        Relationships: [
          {
            foreignKeyName: "vouchers_company_id_fkey"
            columns: ["company_id"]
            isOneToOne: false
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
