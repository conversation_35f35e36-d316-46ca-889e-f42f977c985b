import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  AlertCircle, 
  CheckCircle,
  RefreshCw,
  Calculator,
  BarChart3,
  Clock,
  Award
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import MetricWidget from './MetricWidget';
import RateSimulator from './RateSimulator';
import KPIMonitoring from './KPIMonitoring';
import RateExplanation from './RateExplanation';
import BenchmarkingWidget from './BenchmarkingWidget';
import ScenarioEngine from './ScenarioEngine';
import RecommendationsPanel from './RecommendationsPanel';
import HistoricalTimeline from './HistoricalTimeline';
import { supabase } from '@/integrations/supabase/client';
import {
  calculateLiquidityMetrics,
  calculateWorkingCapitalMetrics,
  calculateProfitabilityMetrics,
  calculateComplianceMetrics,
  LiquidityMetrics,
  WorkingCapitalMetrics,
  ProfitabilityMetrics,
  ComplianceMetrics
} from '@/utils/kpiCalculations';
import { calculateRateFactors, generateRateBreakdown, RateFactors, RateBreakdown } from '@/utils/rateCalculations';

interface CustomerDashboardState {
  liquidityMetrics: LiquidityMetrics | null;
  workingCapitalMetrics: WorkingCapitalMetrics | null;
  profitabilityMetrics: ProfitabilityMetrics | null;
  complianceMetrics: ComplianceMetrics | null;
  rateFactors: RateFactors | null;
  rateBreakdown: RateBreakdown | null;
  loading: boolean;
  error: string | null;
}

const CustomerDashboard: React.FC = () => {
  const [state, setState] = useState<CustomerDashboardState>({
    liquidityMetrics: null,
    workingCapitalMetrics: null,
    profitabilityMetrics: null,
    complianceMetrics: null,
    rateFactors: null,
    rateBreakdown: null,
    loading: true,
    error: null
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // Get demo company
      const { data: companies } = await supabase
        .from('companies')
        .select('*')
        .eq('company_name', 'Nordström GreenTech AB')
        .limit(1);

      const companyId = companies?.[0]?.id || 'demo-company';

      // Load all metrics in parallel
      const [
        liquidityMetrics,
        workingCapitalMetrics,
        profitabilityMetrics,
        complianceMetrics
      ] = await Promise.all([
        calculateLiquidityMetrics(companyId),
        calculateWorkingCapitalMetrics(companyId),
        calculateProfitabilityMetrics(companyId),
        calculateComplianceMetrics(companyId)
      ]);

      // Calculate rate factors
      const rateFactors = calculateRateFactors(
        liquidityMetrics,
        workingCapitalMetrics,
        profitabilityMetrics,
        complianceMetrics
      );

      // Generate rate breakdown
      const rateBreakdown = generateRateBreakdown(rateFactors);

      setState({
        liquidityMetrics,
        workingCapitalMetrics,
        profitabilityMetrics,
        complianceMetrics,
        rateFactors,
        rateBreakdown,
        loading: false,
        error: null
      });

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: 'Failed to load dashboard data. Please try again.'
      }));
    }
  };

  if (state.loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-600">Loading your financial insights...</span>
      </div>
    );
  }

  if (state.error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Dashboard</h3>
        <p className="text-gray-500 mb-4">{state.error}</p>
        <Button onClick={loadDashboardData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  const { rateFactors, rateBreakdown } = state;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-display-sm">Your Interest Rate Dashboard</h1>
          <p className="text-body-lg text-gray-600 mt-2">
            Understand your current rate and discover how to optimize it through improved KPIs
          </p>
          <div className="flex items-center mt-2">
            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            <span className="text-sm text-gray-500">
              Live data • Updated in real-time from your ERP system
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="btn-secondary">
            <BarChart3 className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button className="btn-primary">
            <Calculator className="h-4 w-4 mr-2" />
            Apply for Loan
          </Button>
        </div>
      </div>

      {/* Current Rate Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <MetricWidget
          title="Current Interest Rate"
          value={rateFactors?.currentRate}
          format="percentage"
          change={{
            value: -0.3,
            period: 'vs last month',
            type: 'decrease',
            isGoodChange: true
          }}
          icon={DollarSign}
          size="lg"
          className="md:col-span-2"
        />
        
        <MetricWidget
          title="Industry Benchmark"
          value={rateBreakdown?.industryBenchmark}
          format="percentage"
          subtitle="Swedish SME average"
          icon={Target}
        />
        
        <MetricWidget
          title="Potential Savings"
          value={rateBreakdown?.potentialSavings ? rateBreakdown.potentialSavings * 100 : 0}
          format="percentage"
          subtitle="vs industry average"
          icon={TrendingDown}
          change={{
            value: 15,
            period: 'improvement opportunity',
            type: 'increase',
            isGoodChange: true
          }}
        />
      </div>

      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="h-5 w-5 mr-2" />
            Overall Financial Health Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="text-3xl font-bold text-green-600">
              {rateFactors?.overallScore.toFixed(1)}/100
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Grade</div>
              <div className="text-xl font-semibold text-green-600">A-</div>
            </div>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div 
              className="bg-green-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${rateFactors?.overallScore}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600">
            Your financial health score directly impacts your interest rate. 
            Higher scores unlock better rates automatically.
          </p>
        </CardContent>
      </Card>

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="kpis">KPI Monitoring</TabsTrigger>
          <TabsTrigger value="simulator">Rate Simulator</TabsTrigger>
          <TabsTrigger value="scenarios">What-If Analysis</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <RateExplanation 
              rateBreakdown={rateBreakdown!}
              rateFactors={rateFactors!}
            />
            <BenchmarkingWidget 
              currentScore={rateFactors?.overallScore || 0}
              industryAverage={72}
              topPerformers={88}
            />
          </div>
          <HistoricalTimeline />
        </TabsContent>

        <TabsContent value="kpis" className="space-y-6">
          <KPIMonitoring 
            liquidityMetrics={state.liquidityMetrics!}
            workingCapitalMetrics={state.workingCapitalMetrics!}
            profitabilityMetrics={state.profitabilityMetrics!}
            complianceMetrics={state.complianceMetrics!}
            rateFactors={rateFactors!}
          />
        </TabsContent>

        <TabsContent value="simulator" className="space-y-6">
          <RateSimulator 
            currentFactors={rateFactors!}
            onFactorsChange={(newFactors) => {
              setState(prev => ({ 
                ...prev, 
                rateFactors: newFactors,
                rateBreakdown: generateRateBreakdown(newFactors)
              }));
            }}
          />
        </TabsContent>

        <TabsContent value="scenarios" className="space-y-6">
          <ScenarioEngine 
            currentFactors={rateFactors!}
            loanAmount={5000000}
          />
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-6">
          <RecommendationsPanel 
            rateFactors={rateFactors!}
            liquidityMetrics={state.liquidityMetrics!}
            workingCapitalMetrics={state.workingCapitalMetrics!}
            profitabilityMetrics={state.profitabilityMetrics!}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CustomerDashboard;
