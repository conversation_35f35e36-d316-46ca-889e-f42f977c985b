import React from 'react';

const InventoryTurnover: React.FC = () => {
  // Mock inventory turnover data by category
  const data = [
    { category: 'Raw Materials', turnover: 12.5, target: 15.0, status: 'good' },
    { category: 'Work in Progress', turnover: 8.2, target: 10.0, status: 'warning' },
    { category: 'Finished Goods', turnover: 6.8, target: 8.0, status: 'warning' },
    { category: 'Spare Parts', turnover: 4.2, target: 6.0, status: 'poor' },
    { category: 'Packaging', turnover: 18.5, target: 12.0, status: 'excellent' }
  ];

  const maxValue = Math.max(...data.map(d => Math.max(d.turnover, d.target)));

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return '#10b981';
      case 'good': return '#3b82f6';
      case 'warning': return '#f59e0b';
      case 'poor': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getStatusBg = (status: string) => {
    switch (status) {
      case 'excellent': return 'bg-green-50 text-green-700';
      case 'good': return 'bg-blue-50 text-blue-700';
      case 'warning': return 'bg-yellow-50 text-yellow-700';
      case 'poor': return 'bg-red-50 text-red-700';
      default: return 'bg-gray-50 text-gray-700';
    }
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Chart Area */}
      <div className="flex-1 space-y-4">
        {data.map((item, index) => (
          <div key={index} className="space-y-2">
            {/* Category Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-body-sm font-medium text-gray-900">
                  {item.category}
                </span>
                <span className={`px-2 py-1 rounded text-body-xs font-medium ${getStatusBg(item.status)}`}>
                  {item.status}
                </span>
              </div>
              <div className="text-body-sm text-gray-600">
                {item.turnover}x / {item.target}x
              </div>
            </div>

            {/* Progress Bars */}
            <div className="space-y-1">
              {/* Actual Turnover Bar */}
              <div className="relative">
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="h-3 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${(item.turnover / maxValue) * 100}%`,
                      backgroundColor: getStatusColor(item.status)
                    }}
                  ></div>
                </div>
              </div>
              
              {/* Target Line */}
              <div className="relative h-1">
                <div 
                  className="absolute top-0 w-0.5 h-1 bg-gray-600"
                  style={{ left: `${(item.target / maxValue) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Summary Stats */}
      <div className="pt-4 border-t border-gray-200">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-heading-sm font-semibold text-gray-900">8.2x</div>
            <div className="text-body-xs text-gray-600">Overall Average</div>
          </div>
          <div className="text-center">
            <div className="text-heading-sm font-semibold text-green-600">1</div>
            <div className="text-body-xs text-gray-600">Above Target</div>
          </div>
          <div className="text-center">
            <div className="text-heading-sm font-semibold text-yellow-600">2</div>
            <div className="text-body-xs text-gray-600">Need Attention</div>
          </div>
          <div className="text-center">
            <div className="text-heading-sm font-semibold text-red-600">1</div>
            <div className="text-body-xs text-gray-600">Below Target</div>
          </div>
        </div>
      </div>

      {/* Legend */}
      <div className="pt-2">
        <div className="flex items-center justify-between text-body-xs text-gray-500">
          <span>Actual turnover vs target (times per year)</span>
          <span>Target line: |</span>
        </div>
      </div>
    </div>
  );
};

export default InventoryTurnover;
