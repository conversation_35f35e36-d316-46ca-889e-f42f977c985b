import React from 'react';

const DSODPOTrends: React.FC = () => {
  // Mock DSO/DPO trend data
  const data = [
    { month: 'Jan', dso: 38, dpo: 42, ccc: 35 },
    { month: 'Feb', dso: 36, dpo: 43, ccc: 32 },
    { month: 'Mar', dso: 35, dpo: 44, ccc: 30 },
    { month: 'Apr', dso: 34, dpo: 45, ccc: 29 },
    { month: 'May', dso: 33, dpo: 45, ccc: 28 },
    { month: 'Jun', dso: 32, dpo: 45, ccc: 28 },
    { month: 'Jul', dso: 31, dpo: 46, ccc: 27 },
    { month: 'Aug', dso: 32, dpo: 45, ccc: 28 },
    { month: 'Sep', dso: 31, dpo: 45, ccc: 27 },
    { month: 'Oct', dso: 32, dpo: 45, ccc: 28 },
    { month: 'Nov', dso: 31, dpo: 46, ccc: 27 },
    { month: 'Dec', dso: 32, dpo: 45, ccc: 28 }
  ];

  const maxValue = Math.max(...data.flatMap(d => [d.dso, d.dpo, d.ccc]));
  const minValue = Math.min(...data.flatMap(d => [d.dso, d.dpo, d.ccc]));
  const range = maxValue - minValue;

  const getY = (value: number) => {
    return 250 - ((value - minValue) / range) * 180;
  };

  const createPath = (values: number[]) => {
    return values.map((value, index) => 
      `${index === 0 ? 'M' : 'L'} ${60 + index * 60} ${getY(value)}`
    ).join(' ');
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Chart Area */}
      <div className="flex-1 relative">
        <svg className="w-full h-full" viewBox="0 0 800 300">
          {/* Grid Lines */}
          <defs>
            <pattern id="dsoGrid" width="60" height="30" patternUnits="userSpaceOnUse">
              <path d="M 60 0 L 0 0 0 30" fill="none" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="250" fill="url(#dsoGrid)" />
          
          {/* Y-axis labels */}
          <g className="text-xs fill-gray-500">
            <text x="30" y="80" textAnchor="end">50</text>
            <text x="30" y="125" textAnchor="end">40</text>
            <text x="30" y="170" textAnchor="end">30</text>
            <text x="30" y="215" textAnchor="end">20</text>
          </g>
          
          {/* DSO Line */}
          <path
            d={createPath(data.map(d => d.dso))}
            fill="none"
            stroke="#ef4444"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* DPO Line */}
          <path
            d={createPath(data.map(d => d.dpo))}
            fill="none"
            stroke="#10b981"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Cash Conversion Cycle Line */}
          <path
            d={createPath(data.map(d => d.ccc))}
            fill="none"
            stroke="#3b82f6"
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data Points */}
          {data.map((d, i) => (
            <g key={i}>
              <circle
                cx={60 + i * 60}
                cy={getY(d.dso)}
                r="4"
                fill="#ef4444"
                className="hover:r-6 transition-all cursor-pointer"
              />
              <circle
                cx={60 + i * 60}
                cy={getY(d.dpo)}
                r="4"
                fill="#10b981"
                className="hover:r-6 transition-all cursor-pointer"
              />
              <circle
                cx={60 + i * 60}
                cy={getY(d.ccc)}
                r="4"
                fill="#3b82f6"
                className="hover:r-6 transition-all cursor-pointer"
              />
            </g>
          ))}
          
          {/* X-axis labels */}
          <g className="text-xs fill-gray-500">
            {data.map((d, i) => (
              <text key={i} x={60 + i * 60} y="275" textAnchor="middle">
                {d.month}
              </text>
            ))}
          </g>
        </svg>
      </div>

      {/* Legend and Summary */}
      <div className="pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-body-sm text-gray-600">DSO (Days Sales Outstanding)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-body-sm text-gray-600">DPO (Days Payable Outstanding)</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-body-sm text-gray-600">Cash Conversion Cycle</span>
            </div>
          </div>
          <div className="text-body-sm text-gray-500">
            Current: DSO 32d | DPO 45d | CCC 28d
          </div>
        </div>
      </div>
    </div>
  );
};

export default DSODPOTrends;
