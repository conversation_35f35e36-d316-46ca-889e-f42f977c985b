/**
 * Database Diagnostics
 * Tools to check and verify database state for demo data
 */

import { supabase } from '@/integrations/supabase/client';

export interface DatabaseDiagnostics {
  companies: any[];
  vouchers: any[];
  voucherRows: any[];
  chartOfAccounts: any[];
  exchangeRates: any[];
  errors: string[];
}

/**
 * Run comprehensive database diagnostics
 */
export async function runDatabaseDiagnostics(): Promise<DatabaseDiagnostics> {
  const diagnostics: DatabaseDiagnostics = {
    companies: [],
    vouchers: [],
    voucherRows: [],
    chartOfAccounts: [],
    exchangeRates: [],
    errors: []
  };

  try {
    // Check companies
    const { data: companies, error: companiesError } = await supabase
      .from('companies')
      .select('*')
      .order('created_at', { ascending: false });

    if (companiesError) {
      diagnostics.errors.push(`Companies error: ${companiesError.message}`);
    } else {
      diagnostics.companies = companies || [];
    }

    // Check vouchers
    const { data: vouchers, error: vouchersError } = await supabase
      .from('vouchers')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(50);

    if (vouchersError) {
      diagnostics.errors.push(`Vouchers error: ${vouchersError.message}`);
    } else {
      diagnostics.vouchers = vouchers || [];
    }

    // Check voucher rows
    const { data: voucherRows, error: voucherRowsError } = await supabase
      .from('voucher_rows')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(100);

    if (voucherRowsError) {
      diagnostics.errors.push(`Voucher rows error: ${voucherRowsError.message}`);
    } else {
      diagnostics.voucherRows = voucherRows || [];
    }

    // Check chart of accounts
    const { data: chartOfAccounts, error: chartError } = await supabase
      .from('chart_of_accounts')
      .select('*')
      .order('account_code', { ascending: true });

    if (chartError) {
      diagnostics.errors.push(`Chart of accounts error: ${chartError.message}`);
    } else {
      diagnostics.chartOfAccounts = chartOfAccounts || [];
    }

    // Check exchange rates
    const { data: exchangeRates, error: ratesError } = await supabase
      .from('exchange_rates')
      .select('*')
      .order('rate_date', { ascending: false });

    if (ratesError) {
      diagnostics.errors.push(`Exchange rates error: ${ratesError.message}`);
    } else {
      diagnostics.exchangeRates = exchangeRates || [];
    }

  } catch (error) {
    diagnostics.errors.push(`General error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return diagnostics;
}

/**
 * Test voucher insertion with a single sample voucher
 */
export async function testVoucherInsertion(companyId: string): Promise<{
  success: boolean;
  voucherId?: string;
  error?: string;
  details?: any;
}> {
  try {
    // Create a simple test voucher
    const testVoucher = {
      company_id: companyId,
      voucher_series: 'TEST',
      voucher_number: 9999,
      transaction_date: new Date().toISOString().split('T')[0],
      description: 'Test voucher for diagnostics',
      total_amount: 1000,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      sync_status: 'completed'
    };

    console.log('Inserting test voucher:', testVoucher);

    const { data: voucher, error: voucherError } = await supabase
      .from('vouchers')
      .insert(testVoucher)
      .select()
      .single();

    if (voucherError) {
      return {
        success: false,
        error: voucherError.message,
        details: voucherError
      };
    }

    // Insert test voucher rows
    const testRows = [
      {
        voucher_id: voucher.id,
        account_code: '1930',
        account_name: 'Test Bank Account',
        debit_amount: 1000,
        credit_amount: 0,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Test debit entry',
        row_number: 1
      },
      {
        voucher_id: voucher.id,
        account_code: '3010',
        account_name: 'Test Revenue',
        debit_amount: 0,
        credit_amount: 1000,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Test credit entry',
        row_number: 2
      }
    ];

    console.log('Inserting test voucher rows:', testRows);

    const { error: rowsError } = await supabase
      .from('voucher_rows')
      .insert(testRows);

    if (rowsError) {
      return {
        success: false,
        error: `Voucher rows error: ${rowsError.message}`,
        details: rowsError,
        voucherId: voucher.id
      };
    }

    return {
      success: true,
      voucherId: voucher.id
    };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      details: error
    };
  }
}

/**
 * Generate and insert a small batch of test verifikationer
 */
export async function generateTestVerifikationer(companyId: string, count: number = 5): Promise<{
  success: boolean;
  generated: number;
  errors: string[];
}> {
  const result = {
    success: false,
    generated: 0,
    errors: [] as string[]
  };

  try {
    // Import the daily verifikationer generator
    const { generateDailyVerifikationer } = await import('./daily-verifikationer');
    
    const today = new Date();
    const verifikationer = generateDailyVerifikationer(today, companyId);
    
    console.log(`Generated ${verifikationer.length} verifikationer for testing`);
    
    // Take only the requested count
    const testVerifikationer = verifikationer.slice(0, count);
    
    for (const voucherData of testVerifikationer) {
      try {
        console.log('Inserting voucher:', {
          series: voucherData.voucher_series,
          number: voucherData.voucher_number,
          date: voucherData.transaction_date,
          amount: voucherData.total_amount
        });

        // Insert voucher
        const { data: voucher, error: voucherError } = await supabase
          .from('vouchers')
          .insert({
            company_id: companyId,
            voucher_series: voucherData.voucher_series,
            voucher_number: voucherData.voucher_number,
            transaction_date: voucherData.transaction_date,
            description: voucherData.description,
            total_amount: voucherData.total_amount,
            currency_code: voucherData.currency_code,
            exchange_rate: voucherData.exchange_rate,
            sync_status: 'completed'
          })
          .select()
          .single();

        if (voucherError) {
          result.errors.push(`Voucher ${voucherData.voucher_series}-${voucherData.voucher_number}: ${voucherError.message}`);
          continue;
        }

        console.log('Voucher inserted successfully:', voucher.id);

        // Insert voucher rows
        const rowsWithVoucherId = voucherData.rows.map((row, index) => ({
          ...row,
          voucher_id: voucher.id,
          row_number: index + 1
        }));

        console.log('Inserting voucher rows:', rowsWithVoucherId.length);

        const { error: rowsError } = await supabase
          .from('voucher_rows')
          .insert(rowsWithVoucherId);

        if (rowsError) {
          result.errors.push(`Voucher rows for ${voucherData.voucher_series}-${voucherData.voucher_number}: ${rowsError.message}`);
          continue;
        }

        console.log('Voucher rows inserted successfully');
        result.generated++;

      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : 'Unknown error';
        result.errors.push(`Processing voucher ${voucherData.voucher_series}-${voucherData.voucher_number}: ${errorMsg}`);
        console.error('Error processing voucher:', error);
      }
    }

    result.success = result.generated > 0;
    console.log(`Test generation completed: ${result.generated} vouchers generated, ${result.errors.length} errors`);

  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : 'Unknown error';
    result.errors.push(`General error: ${errorMsg}`);
    console.error('Error in generateTestVerifikationer:', error);
  }

  return result;
}

/**
 * Clean up test data
 */
export async function cleanupTestData(): Promise<void> {
  try {
    // Delete test vouchers and their rows
    const { data: testVouchers } = await supabase
      .from('vouchers')
      .select('id')
      .eq('voucher_series', 'TEST');

    if (testVouchers && testVouchers.length > 0) {
      const voucherIds = testVouchers.map(v => v.id);
      
      // Delete rows first
      await supabase
        .from('voucher_rows')
        .delete()
        .in('voucher_id', voucherIds);

      // Delete vouchers
      await supabase
        .from('vouchers')
        .delete()
        .eq('voucher_series', 'TEST');

      console.log(`Cleaned up ${testVouchers.length} test vouchers`);
    }
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
}

/**
 * Print diagnostic summary
 */
export function printDiagnosticSummary(diagnostics: DatabaseDiagnostics): void {
  console.log('=== DATABASE DIAGNOSTICS SUMMARY ===');
  console.log(`Companies: ${diagnostics.companies.length}`);
  diagnostics.companies.forEach(company => {
    console.log(`  - ${company.company_name} (${company.organization_number})`);
  });
  
  console.log(`Vouchers: ${diagnostics.vouchers.length}`);
  console.log(`Voucher Rows: ${diagnostics.voucherRows.length}`);
  console.log(`Chart of Accounts: ${diagnostics.chartOfAccounts.length}`);
  console.log(`Exchange Rates: ${diagnostics.exchangeRates.length}`);
  
  if (diagnostics.errors.length > 0) {
    console.log('ERRORS:');
    diagnostics.errors.forEach(error => console.log(`  - ${error}`));
  }
  
  console.log('=====================================');
}
