import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { validateOrganizationNumber, detectCompanyType, CompanyType } from '@/lib/validation';

interface SimpleOrganizationNumberInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  onCompanyTypeChange?: (type: CompanyType) => void;
}

const SimpleOrganizationNumberInput: React.FC<SimpleOrganizationNumberInputProps> = ({
  value,
  onChange,
  placeholder = 'XXXXXX-XXXX',
  className,
  onCompanyTypeChange,
}) => {
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [companyType, setCompanyType] = useState<CompanyType>(CompanyType.UNKNOWN);
  const [formattedValue, setFormattedValue] = useState<string>('');

  // Format and validate the organization number on change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Allow only digits and hyphen
    const sanitized = inputValue.replace(/[^\d-]/g, '');

    // Update the parent component with the sanitized input
    onChange(sanitized);

    // Validate the organization number
    const formatted = validateOrganizationNumber(sanitized);
    setIsValid(formatted !== null);

    if (formatted) {
      setFormattedValue(formatted);
      const type = detectCompanyType(formatted);
      setCompanyType(type);
      if (onCompanyTypeChange) {
        onCompanyTypeChange(type);
      }
    } else {
      setFormattedValue(sanitized);
      setCompanyType(CompanyType.UNKNOWN);
      if (onCompanyTypeChange) {
        onCompanyTypeChange(CompanyType.UNKNOWN);
      }
    }
  };

  // Format the initial value if it's valid
  useEffect(() => {
    if (value) {
      const formatted = validateOrganizationNumber(value);
      setIsValid(formatted !== null);

      if (formatted) {
        setFormattedValue(formatted);
        const type = detectCompanyType(formatted);
        setCompanyType(type);
        if (onCompanyTypeChange) {
          onCompanyTypeChange(type);
        }
      } else {
        setFormattedValue(value);
      }
    } else {
      setFormattedValue('');
      setIsValid(null);
      setCompanyType(CompanyType.UNKNOWN);
    }
  }, [value, onCompanyTypeChange]);

  return (
    <div className={className}>
      <div className="relative">
        <Input
          placeholder={placeholder}
          value={formattedValue}
          onChange={handleChange}
          className={cn(
            "mt-1 transition-all duration-200",
            isValid === true && 'pr-10 border-green-500 shadow-[0_0_0_1px_#10B981]',
            isValid === false && 'pr-10 border-red-500 shadow-[0_0_0_1px_#EF4444]'
          )}
        />
        {isValid !== null && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {isValid ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-500" />
            )}
          </div>
        )}
      </div>

      {isValid && companyType !== CompanyType.UNKNOWN && (
        <div className="mt-2">
          <Badge
            variant="outline"
            className={cn(
              'text-sm px-3 py-1 font-medium',
              companyType === CompanyType.AKTIEBOLAG && 'bg-blue-100 text-blue-700 border-blue-200',
              companyType === CompanyType.HANDELSBOLAG && 'bg-purple-100 text-purple-700 border-purple-200',
              companyType === CompanyType.ENSKILD_FIRMA && 'bg-green-100 text-green-700 border-green-200',
              companyType === CompanyType.EKONOMISK_FORENING && 'bg-orange-100 text-orange-700 border-orange-200',
              companyType === CompanyType.IDEELL_FORENING && 'bg-pink-100 text-pink-700 border-pink-200',
              companyType === CompanyType.STIFTELSE && 'bg-indigo-100 text-indigo-700 border-indigo-200'
            )}
          >
            {companyType}
          </Badge>
        </div>
      )}
    </div>
  );
};

export default SimpleOrganizationNumberInput;
