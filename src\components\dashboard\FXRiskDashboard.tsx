/**
 * FX Risk Dashboard Component
 * Multi-currency exposure and risk management for Nordström GreenTech AB
 */

import React, { useState, useEffect } from 'react';
import {
  Globe,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Shield,
  RefreshCw,
  ArrowUpDown,
  Users,
  Target,
  Activity
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import MetricWidget from '@/components/dashboard/MetricWidget';

interface FXExposure {
  currency: string;
  exposure: number;
  exposureSEK: number;
  percentage: number;
  riskLevel: 'low' | 'medium' | 'high';
  volatility: number;
  hedgeRatio: number;
}

interface FXRate {
  currency: string;
  rate: number;
  change24h: number;
  change7d: number;
  trend: 'up' | 'down' | 'stable';
}

interface FXRiskMetrics {
  totalExposure: number;
  valueAtRisk: number;
  hedgeRatio: number;
  riskScore: number;
  exposures: FXExposure[];
  rates: FXRate[];
  historicalRates: any[];
  // Enhanced risk metrics
  counterpartyRisk: Record<string, number>;
  hedgeEffectiveness: number;
  interestRateSensitivity: number;
}

const FXRiskDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<FXRiskMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedCurrency, setSelectedCurrency] = useState<string>('EUR');

  useEffect(() => {
    loadFXData();
  }, []);

  const loadFXData = async () => {
    try {
      setLoading(true);

      // Get demo company
      const { data: companies } = await supabase
        .from('companies')
        .select('*')
        .eq('company_name', 'Nordström GreenTech AB')
        .limit(1);

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].id;

      // Get voucher data for FX exposure calculation
      const { data: vouchers } = await supabase
        .from('vouchers')
        .select(`
          *,
          voucher_rows (*)
        `)
        .eq('company_id', companyId)
        .neq('currency_code', 'SEK');

      // Get exchange rates
      const { data: exchangeRates } = await supabase
        .from('exchange_rates')
        .select('*')
        .order('rate_date', { ascending: false });

      if (!vouchers || !exchangeRates) return;

      // Import enhanced risk calculations
      const { calculateRiskMetrics } = await import('@/utils/kpiCalculations');
      const enhancedRiskMetrics = await calculateRiskMetrics(companyId);

      const calculatedMetrics = calculateFXMetrics(vouchers, exchangeRates, enhancedRiskMetrics);
      setMetrics(calculatedMetrics);

    } catch (error) {
      console.error('Error loading FX data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculateFXMetrics = (vouchers: any[], exchangeRates: any[], enhancedRisk?: any): FXRiskMetrics => {
    // Calculate currency exposures
    const currencyExposures: Record<string, number> = {};

    vouchers.forEach(voucher => {
      const currency = voucher.currency_code;
      if (currency !== 'SEK') {
        currencyExposures[currency] = (currencyExposures[currency] || 0) + voucher.total_amount;
      }
    });

    // Get latest exchange rates
    const latestRates: Record<string, number> = {};
    const rateChanges: Record<string, { change24h: number; change7d: number }> = {};

    ['EUR', 'USD', 'NOK', 'DKK'].forEach(currency => {
      const rates = exchangeRates.filter(r => r.target_currency === currency);
      if (rates.length > 0) {
        latestRates[currency] = rates[0].rate;
        // Mock rate changes for demo
        rateChanges[currency] = {
          change24h: (Math.random() - 0.5) * 2, // -1% to +1%
          change7d: (Math.random() - 0.5) * 10  // -5% to +5%
        };
      }
    });

    // Calculate exposures in SEK
    const totalExposureSEK = Object.entries(currencyExposures).reduce((sum, [currency, amount]) => {
      const rate = latestRates[currency] || 1;
      return sum + (amount / rate);
    }, 0);

    // Create exposure objects
    const exposures: FXExposure[] = Object.entries(currencyExposures).map(([currency, amount]) => {
      const rate = latestRates[currency] || 1;
      const exposureSEK = amount / rate;
      const percentage = (exposureSEK / totalExposureSEK) * 100;

      // Determine risk level based on exposure and volatility
      let riskLevel: 'low' | 'medium' | 'high' = 'low';
      if (percentage > 30) riskLevel = 'high';
      else if (percentage > 15) riskLevel = 'medium';

      return {
        currency,
        exposure: amount,
        exposureSEK,
        percentage,
        riskLevel,
        volatility: Math.random() * 20 + 5, // 5-25% volatility
        hedgeRatio: Math.random() * 60 + 20 // 20-80% hedged
      };
    });

    // Create rate objects
    const rates: FXRate[] = Object.entries(latestRates).map(([currency, rate]) => {
      const changes = rateChanges[currency] || { change24h: 0, change7d: 0 };
      let trend: 'up' | 'down' | 'stable' = 'stable';
      if (Math.abs(changes.change24h) > 0.5) {
        trend = changes.change24h > 0 ? 'up' : 'down';
      }

      return {
        currency,
        rate,
        change24h: changes.change24h,
        change7d: changes.change7d,
        trend
      };
    });

    // Generate historical rates for chart (mock data)
    const historicalRates = [];
    for (let i = 30; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      historicalRates.push({
        date: date.toISOString().split('T')[0],
        EUR: 11.5 + (Math.random() - 0.5) * 0.5,
        USD: 10.5 + (Math.random() - 0.5) * 0.8,
        NOK: 0.95 + (Math.random() - 0.5) * 0.05,
        DKK: 1.54 + (Math.random() - 0.5) * 0.1
      });
    }

    // Calculate risk metrics
    const valueAtRisk = totalExposureSEK * 0.05; // 5% VaR assumption
    const avgHedgeRatio = exposures.reduce((sum, exp) => sum + exp.hedgeRatio, 0) / exposures.length;
    const riskScore = Math.max(0, Math.min(100,
      (totalExposureSEK / 50000000) * 50 + // Exposure component
      (100 - avgHedgeRatio) * 0.3 + // Hedge component
      exposures.reduce((sum, exp) => sum + exp.volatility, 0) / exposures.length * 0.2 // Volatility component
    ));

    return {
      totalExposure: totalExposureSEK,
      valueAtRisk: enhancedRisk?.valueAtRisk || valueAtRisk,
      hedgeRatio: enhancedRisk?.hedgeRatio || avgHedgeRatio,
      riskScore,
      exposures,
      rates,
      historicalRates,
      // Enhanced risk metrics
      counterpartyRisk: enhancedRisk?.counterpartyRisk || {},
      hedgeEffectiveness: 85 + Math.random() * 10, // 85-95%
      interestRateSensitivity: 2.5 + Math.random() * 2 // 2.5-4.5%
    };
  };

  const formatCurrency = (amount: number, currency: string = 'SEK') => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-green-600 bg-green-50 border-green-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <ArrowUpDown className="h-4 w-4 text-gray-400" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No FX Data Available</h3>
        <p className="text-gray-500">Load financial data to see FX risk analysis</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">FX Risk Management</h2>
          <p className="text-gray-500">Multi-currency exposure and hedging analysis</p>
        </div>
        <Button variant="outline" size="sm" onClick={loadFXData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Rates
        </Button>
      </div>

      {/* Risk Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total FX Exposure</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.totalExposure)}</div>
            <p className="text-xs text-muted-foreground">
              Across {metrics.exposures.length} currencies
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Value at Risk (95%)</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.valueAtRisk)}</div>
            <p className="text-xs text-muted-foreground">
              Daily VaR estimate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hedge Ratio</CardTitle>
            <Shield className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.hedgeRatio.toFixed(1)}%</div>
            <Progress value={metrics.hedgeRatio} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
            <div className={`h-4 w-4 rounded-full ${
              metrics.riskScore > 70 ? 'bg-red-500' :
              metrics.riskScore > 40 ? 'bg-yellow-500' : 'bg-green-500'
            }`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.riskScore.toFixed(0)}/100</div>
            <p className="text-xs text-muted-foreground">
              Overall risk assessment
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Currency Exposure */}
        <Card>
          <CardHeader>
            <CardTitle>Currency Exposure</CardTitle>
            <CardDescription>Exposure by currency (SEK equivalent)</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={metrics.exposures}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="currency" />
                <YAxis tickFormatter={(value) => `${value / 1000000}M`} />
                <Tooltip
                  formatter={(value: number) => formatCurrency(value)}
                  labelFormatter={(label) => `Currency: ${label}`}
                />
                <Bar dataKey="exposureSEK" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Exchange Rate Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Exchange Rate Trends</CardTitle>
            <CardDescription>30-day rate history (SEK per unit)</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={metrics.historicalRates}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" tickFormatter={(value) => new Date(value).toLocaleDateString('sv-SE', { month: 'short', day: 'numeric' })} />
                <YAxis />
                <Tooltip
                  labelFormatter={(label) => new Date(label).toLocaleDateString('sv-SE')}
                />
                <Line type="monotone" dataKey="EUR" stroke="#10b981" strokeWidth={2} name="EUR/SEK" />
                <Line type="monotone" dataKey="USD" stroke="#f59e0b" strokeWidth={2} name="USD/SEK" />
                <Line type="monotone" dataKey="NOK" stroke="#8b5cf6" strokeWidth={2} name="NOK/SEK" />
                <Line type="monotone" dataKey="DKK" stroke="#ef4444" strokeWidth={2} name="DKK/SEK" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Exposure Table */}
      <Card>
        <CardHeader>
          <CardTitle>Currency Exposure Details</CardTitle>
          <CardDescription>Detailed breakdown by currency</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics.exposures.map((exposure) => (
              <div key={exposure.currency} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="text-2xl font-bold">{exposure.currency}</div>
                  <div>
                    <p className="font-medium">{formatCurrency(exposure.exposureSEK)} SEK</p>
                    <p className="text-sm text-gray-500">
                      {formatCurrency(exposure.exposure, exposure.currency)} original
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="font-medium">{exposure.percentage.toFixed(1)}%</p>
                    <p className="text-sm text-gray-500">of total exposure</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{exposure.hedgeRatio.toFixed(1)}%</p>
                    <p className="text-sm text-gray-500">hedged</p>
                  </div>
                  <Badge className={getRiskColor(exposure.riskLevel)}>
                    {exposure.riskLevel.toUpperCase()}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Exchange Rates */}
      <Card>
        <CardHeader>
          <CardTitle>Current Exchange Rates</CardTitle>
          <CardDescription>Live rates and recent changes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {metrics.rates.map((rate) => (
              <div key={rate.currency} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{rate.currency}/SEK</span>
                  {getTrendIcon(rate.trend)}
                </div>
                <div className="text-xl font-bold mb-1">{rate.rate.toFixed(4)}</div>
                <div className="text-sm space-y-1">
                  <div className={`flex items-center ${rate.change24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    <span>24h: {rate.change24h >= 0 ? '+' : ''}{rate.change24h.toFixed(2)}%</span>
                  </div>
                  <div className={`flex items-center ${rate.change7d >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    <span>7d: {rate.change7d >= 0 ? '+' : ''}{rate.change7d.toFixed(2)}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FXRiskDashboard;
