/**
 * Payments Operations Dashboard Component
 * Payment processing efficiency and operational metrics
 */

import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  FileText,
  TrendingUp,
  RefreshCw,
  MoreHorizontal
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import MetricWidget from '@/components/dashboard/MetricWidget';
import ChartContainer from '@/components/dashboard/ChartContainer';
import ModuleCard from '@/components/dashboard/ModuleCard';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

interface PaymentMetrics {
  paymentSuccessRate: number;
  averageProcessingTime: number;
  autoReconciliationRate: number;
  averageApprovalTime: number;
  latePayments: number;
  auditTrailCompleteness: number;
  totalTransactions: number;
  monthlyVolume: number;
}

const PaymentsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PaymentMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPaymentData();
  }, []);

  const loadPaymentData = async () => {
    try {
      setLoading(true);

      // Get demo company
      const { data: companies } = await supabase
        .from('companies')
        .select('*')
        .eq('company_name', 'Nordström GreenTech AB')
        .limit(1);

      if (!companies || companies.length === 0) return;

      const companyId = companies[0].id;

      // Import payment calculations
      const { calculatePaymentMetrics } = await import('@/utils/kpiCalculations');
      const paymentMetrics = await calculatePaymentMetrics(companyId);

      // Get additional transaction data
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

      const { data: recentTransactions } = await supabase
        .from('vouchers')
        .select('total_amount, currency_code, exchange_rate')
        .eq('company_id', companyId)
        .gte('transaction_date', oneMonthAgo.toISOString().split('T')[0]);

      const totalTransactions = recentTransactions?.length || 0;
      const monthlyVolume = recentTransactions?.reduce((sum, t) => {
        return sum + (t.total_amount / (t.exchange_rate || 1));
      }, 0) || 0;

      setMetrics({
        ...paymentMetrics,
        totalTransactions,
        monthlyVolume
      });

    } catch (error) {
      console.error('Error loading payment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatTime = (hours: number) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}m`;
    }
    return `${hours.toFixed(1)}h`;
  };

  // Mock data for charts
  const processingTimeData = [
    { month: 'Jan', time: 3.2 },
    { month: 'Feb', time: 2.8 },
    { month: 'Mar', time: 2.5 },
    { month: 'Apr', time: 2.3 },
    { month: 'May', time: 2.1 },
    { month: 'Jun', time: 2.4 }
  ];

  const successRateData = [
    { month: 'Jan', rate: 94.2 },
    { month: 'Feb', rate: 95.1 },
    { month: 'Mar', rate: 96.3 },
    { month: 'Apr', rate: 97.1 },
    { month: 'May', rate: 96.8 },
    { month: 'Jun', rate: 97.5 }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Payment Data</h3>
        <p className="text-gray-500 mb-4">Initialize demo data to see payment insights</p>
        <Button onClick={loadPaymentData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-display-sm">Payments Operations</h1>
          <p className="text-body-lg text-gray-600 mt-2">
            Payment processing efficiency and operational metrics
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="btn-secondary">
            Export Report
          </Button>
          <Button className="btn-primary">
            Process Payments
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Payment Success Rate"
          value={metrics.paymentSuccessRate}
          format="percentage"
          change={{
            value: 1.2,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={CheckCircle}
        />

        <MetricWidget
          title="Avg Processing Time"
          value={formatTime(metrics.averageProcessingTime)}
          change={{
            value: -8.5,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={Clock}
        />

        <MetricWidget
          title="Auto Reconciliation"
          value={metrics.autoReconciliationRate}
          format="percentage"
          change={{
            value: 3.2,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={TrendingUp}
        />

        <MetricWidget
          title="Audit Trail Complete"
          value={metrics.auditTrailCompleteness}
          format="percentage"
          change={{
            value: 0.8,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={FileText}
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Monthly Volume"
          value={metrics.monthlyVolume}
          format="currency"
          change={{
            value: 12.3,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={CreditCard}
        />

        <MetricWidget
          title="Total Transactions"
          value={metrics.totalTransactions}
          format="number"
          change={{
            value: 8.7,
            period: 'vs last month',
            type: 'increase'
          }}
          icon={Users}
        />

        <MetricWidget
          title="Late Payments"
          value={metrics.latePayments}
          format="number"
          change={{
            value: -15.2,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={AlertTriangle}
        />

        <MetricWidget
          title="Avg Approval Time"
          value={formatTime(metrics.averageApprovalTime)}
          change={{
            value: -12.1,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={Clock}
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Processing Time Trend */}
        <ChartContainer
          title="Processing Time Trend"
          description="Average payment processing time over time"
          icon={Clock}
          headerActions={
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          }
        >
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={processingTimeData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value: number) => [`${value}h`, 'Processing Time']} />
              <Line type="monotone" dataKey="time" stroke="#3b82f6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>

        {/* Success Rate Trend */}
        <ChartContainer
          title="Payment Success Rate"
          description="First-time payment success rate trend"
          icon={CheckCircle}
          headerActions={
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          }
        >
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={successRateData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[90, 100]} />
              <Tooltip formatter={(value: number) => [`${value}%`, 'Success Rate']} />
              <Bar dataKey="rate" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>

      {/* Operational Insights */}
      <ModuleCard
        title="Operational Insights"
        description="Key insights and recommendations for payment operations"
        icon={TrendingUp}
        headerActions={
          <Button variant="outline" size="sm" className="btn-secondary">
            View Details
          </Button>
        }
      >
        <div className="space-y-6">
          {/* Performance Score */}
          <div className="text-center p-6 bg-success-50 border border-success-200 rounded-lg">
            <div className="text-4xl font-bold text-success-700 mb-2">92</div>
            <div className="text-body-lg text-success-600 font-medium">Payment Operations Score</div>
            <div className="text-body-sm text-success-600 mt-1">Excellent performance</div>
          </div>

          {/* Recommendations */}
          <div className="space-y-3">
            <h4 className="text-heading-sm">Optimization Opportunities</h4>
            <ul className="space-y-2">
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Implement automated retry logic to improve success rate to 99%+
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Reduce processing time by 20% through batch processing optimization
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Enhance auto-reconciliation to achieve 95%+ automation rate
                </span>
              </li>
            </ul>
          </div>
        </div>
      </ModuleCard>
    </div>
  );
};

export default PaymentsDashboard;
