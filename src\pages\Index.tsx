import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowRight, Shield, TrendingUp, BarChart3, Zap, CheckCircle, Building, Users, DollarSign, Database, Activity, Workflow, Target, CreditCard, Globe, Clock, Package, RefreshCw, Eye, Lightbulb, Link2, Rocket, Brain, Gauge, FileText, Handshake, MousePointer, PieChart, Smartphone, Star, TrendingDown, Wallet } from 'lucide-react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { Link } from 'react-router-dom';
import MetricWidget from '@/components/dashboard/MetricWidget';
const Index = () => {
  return <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="relative pt-24 pb-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 overflow-hidden">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="container-clean relative">
          <div className="grid lg:grid-cols-2 gap-12 items-center max-w-7xl mx-auto">
            {/* Hero Content */}
            <div className="text-left">
              <h1 className="text-5xl md:text-6xl font-bold text-gray-900 tracking-tight mb-6">
                Empower Your Business with{' '}
                <span className="text-neutral-950">Adaptive Financing</span>
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 mb-8 leading-relaxed">
                Unlock fair, transparent loans—powered by your actual performance, not outdated credit scores.
              </p>

              <div className="mb-8">
                <Link to="/register">
                  <Button className="btn-primary px-8 py-4 text-lg h-14 w-full sm:w-auto" aria-label="Set up your platform account">
                    Set Up Your Platform Account
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-gray-500">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  <span>Bank-grade security</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  <span>GDPR compliant</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  <span>Trusted by 500+ SMEs</span>
                </div>
              </div>
            </div>

            {/* Dashboard Preview */}
            <div className="relative">
              <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-6 shadow-2xl border border-gray-200">
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Financial Dashboard</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <MetricWidget title="Cash Position" value={2450000} format="currency" size="sm" change={{
                    value: 12.5,
                    period: "vs last month",
                    type: "increase",
                    isGoodChange: true
                  }} icon={Wallet} />
                    <MetricWidget title="Available Credit" value={5000000} format="currency" size="sm" change={{
                    value: 8.3,
                    period: "vs last month",
                    type: "increase",
                    isGoodChange: true
                  }} icon={CreditCard} />
                    <MetricWidget title="Risk Score" value={85} format="number" size="sm" change={{
                    value: 5.2,
                    period: "vs last month",
                    type: "increase",
                    isGoodChange: true
                  }} icon={Gauge} />
                    <MetricWidget title="Loan Rate" value={3.2} format="percentage" size="sm" change={{
                    value: -0.5,
                    period: "vs last month",
                    type: "decrease",
                    isGoodChange: true
                  }} icon={TrendingDown} />
                  </div>
                </div>
                <div className="text-center">
                  <p className="text-sm text-gray-500">Real-time data from your business</p>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-green-100 rounded-full p-3 shadow-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="absolute -bottom-4 -left-4 bg-blue-100 rounded-full p-3 shadow-lg">
                <Brain className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Value Proposition Section */}
      <section id="value-proposition" className="section">
        <div className="container-clean">
          <div className="text-center mb-16">
            <h2 className="text-display-md mb-4">Why Choose Us?</h2>
            <p className="text-body-lg text-gray-600 max-w-3xl mx-auto">
              Experience the future of business financing with our revolutionary approach to SME lending
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Instant, Data-Driven Decisions */}
            <Card className="p-8 text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-blue-200">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Brain className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-heading-lg mb-4 text-gray-900">Instant, Data-Driven Decisions</h3>
                <p className="text-body-md text-gray-600">
                  Get loan decisions in minutes, not weeks. Our AI analyzes your real business performance to provide instant, accurate assessments.
                </p>
              </CardContent>
            </Card>

            {/* Adaptive Interest Rates */}
            <Card className="p-8 text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-green-200">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Gauge className="h-8 w-8 text-green-600" />
                </div>
                <h3 className="text-heading-lg mb-4 text-gray-900">Adaptive Interest Rates</h3>
                <p className="text-body-md text-gray-600">
                  Your rates improve as your business grows. Our dynamic pricing rewards strong performance with better terms automatically.
                </p>
              </CardContent>
            </Card>

            {/* Total Transparency */}
            <Card className="p-8 text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-purple-200">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Eye className="h-8 w-8 text-purple-600" />
                </div>
                <h3 className="text-heading-lg mb-4 text-gray-900">Total Transparency</h3>
                <p className="text-body-md text-gray-600">
                  No hidden fees, no surprises. See exactly how your rates are calculated and track your financial health in real-time.
                </p>
              </CardContent>
            </Card>

            {/* Actionable Insights */}
            <Card className="p-8 text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-orange-200">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Lightbulb className="h-8 w-8 text-orange-600" />
                </div>
                <h3 className="text-heading-lg mb-4 text-gray-900">Actionable Insights</h3>
                <p className="text-body-md text-gray-600">
                  Get personalized recommendations to improve your financial health and unlock better financing terms.
                </p>
              </CardContent>
            </Card>

            {/* Seamless Experience */}
            <Card className="p-8 text-center hover:shadow-lg transition-all duration-300 border-2 hover:border-indigo-200 md:col-span-2 lg:col-span-1">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Smartphone className="h-8 w-8 text-indigo-600" />
                </div>
                <h3 className="text-heading-lg mb-4 text-gray-900">Seamless Experience</h3>
                <p className="text-body-md text-gray-600">
                  From application to funding in one smooth journey. Our platform integrates with your existing systems effortlessly.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="section bg-gray-50">
        <div className="container-clean">
          <div className="text-center mb-16">
            <h2 className="text-display-md mb-4">How It Works</h2>
            <p className="text-body-lg text-gray-600 max-w-2xl mx-auto">
              From connection to funding in four simple steps—designed for busy business owners
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {/* Step 1: Connect */}
            <div className="text-center relative">
              <div className="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Link2 className="h-10 w-10 text-white" />
              </div>
              <div className="absolute top-8 left-20 hidden lg:block">
                <ArrowRight className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-heading-md mb-4 text-gray-900">1. Connect</h3>
              <p className="text-body-md text-gray-600">
                Securely link your ERP and bank accounts in minutes. Our platform integrates with all major Swedish banking systems.
              </p>
            </div>

            {/* Step 2: Analyze */}
            <div className="text-center relative">
              <div className="w-20 h-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <BarChart3 className="h-10 w-10 text-white" />
              </div>
              <div className="absolute top-8 left-20 hidden lg:block">
                <ArrowRight className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-heading-md mb-4 text-gray-900">2. Analyze</h3>
              <p className="text-body-md text-gray-600">
                Our AI performs a comprehensive financial health assessment, analyzing cash flow, growth trends, and risk factors.
              </p>
            </div>

            {/* Step 3: Apply */}
            <div className="text-center relative">
              <div className="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <FileText className="h-10 w-10 text-white" />
              </div>
              <div className="absolute top-8 left-20 hidden lg:block">
                <ArrowRight className="h-6 w-6 text-gray-400" />
              </div>
              <h3 className="text-heading-md mb-4 text-gray-900">3. Apply</h3>
              <p className="text-body-md text-gray-600">
                Receive a tailored loan offer with transparent terms based on your actual business performance, not just credit scores.
              </p>
            </div>

            {/* Step 4: Grow */}
            <div className="text-center">
              <div className="w-20 h-20 bg-orange-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <TrendingUp className="h-10 w-10 text-white" />
              </div>
              <h3 className="text-heading-md mb-4 text-gray-900">4. Grow</h3>
              <p className="text-body-md text-gray-600">
                Monitor your financial health in real-time and watch your rates improve as your business grows and performs better.
              </p>
            </div>
          </div>

          {/* Process Timeline */}
          <div className="mt-16 text-center">
            <div className="inline-flex items-center gap-4 bg-white rounded-full px-8 py-4 shadow-lg border border-gray-200">
              <Clock className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-900">
                Complete process: <span className="text-blue-600">Under 30 minutes</span>
              </span>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="section">
        <div className="container-clean">
          <div className="text-center mb-16">
            <h2 className="text-display-md mb-4">Irresistible Benefits</h2>
            <p className="text-body-lg text-gray-600 max-w-3xl mx-auto">
              Transform your business financing with benefits that traditional banks simply can't match
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {/* Benefit 1: Lower Costs */}
            <Card className="p-8 bg-gradient-to-br from-green-50 to-emerald-50 border-2 border-green-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-heading-lg mb-3 text-gray-900">Lower Financing Costs</h3>
                    <p className="text-body-md text-gray-600 mb-4">
                      Save up to 40% on interest rates compared to traditional bank loans. Our performance-based pricing means better businesses pay less.
                    </p>
                    <div className="flex items-center text-sm text-green-700 font-medium">
                      <TrendingDown className="h-4 w-4 mr-2" />
                      Average 2.5% lower than bank rates
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Benefit 2: Faster Access */}
            <Card className="p-8 bg-gradient-to-br from-blue-50 to-cyan-50 border-2 border-blue-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Zap className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-heading-lg mb-3 text-gray-900">Lightning-Fast Access</h3>
                    <p className="text-body-md text-gray-600 mb-4">
                      Get funding in hours, not months. Our automated underwriting process eliminates lengthy paperwork and waiting periods.
                    </p>
                    <div className="flex items-center text-sm text-blue-700 font-medium">
                      <Clock className="h-4 w-4 mr-2" />
                      Funding in under 24 hours
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Benefit 3: Flexible Terms */}
            <Card className="p-8 bg-gradient-to-br from-purple-50 to-violet-50 border-2 border-purple-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Handshake className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-heading-lg mb-3 text-gray-900">Flexible Terms</h3>
                    <p className="text-body-md text-gray-600 mb-4">
                      Customize repayment schedules that match your cash flow patterns. No rigid monthly payments that strain your business.
                    </p>
                    <div className="flex items-center text-sm text-purple-700 font-medium">
                      <Workflow className="h-4 w-4 mr-2" />
                      Terms that adapt to your business
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Benefit 4: Growth Support */}
            <Card className="p-8 bg-gradient-to-br from-orange-50 to-amber-50 border-2 border-orange-200 hover:shadow-xl transition-all duration-300">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Rocket className="h-6 w-6 text-orange-600" />
                  </div>
                  <div>
                    <h3 className="text-heading-lg mb-3 text-gray-900">Growth-Focused Support</h3>
                    <p className="text-body-md text-gray-600 mb-4">
                      Beyond funding, get strategic insights and recommendations to accelerate your business growth and improve financial health.
                    </p>
                    <div className="flex items-center text-sm text-orange-700 font-medium">
                      <Target className="h-4 w-4 mr-2" />
                      Dedicated growth advisory
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Secondary CTA Section */}
      <section className="section bg-gray-50">
        <div className="container-clean">
          <div className="text-center max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl p-12 shadow-xl border border-gray-200">
              <h2 className="text-display-md mb-6 text-gray-900">
                Join Our Platform
              </h2>
              <p className="text-body-xl text-gray-600 mb-8">
                Set up your account to access our comprehensive financial platform. Connect your systems, monitor your financial health, and unlock personalized financing opportunities—all in one place.
              </p>

              {/* Trial Benefits */}
              <div className="grid md:grid-cols-3 gap-6 mb-10">
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <p className="text-sm font-medium text-gray-900">No Credit Check</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Shield className="h-6 w-6 text-blue-600" />
                  </div>
                  <p className="text-sm font-medium text-gray-900">100% Secure</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <Clock className="h-6 w-6 text-purple-600" />
                  </div>
                  <p className="text-sm font-medium text-gray-900">5-Minute Setup</p>
                </div>
              </div>

              <Link to="/register">
                <Button className="btn-primary px-10 py-4 text-lg h-14" aria-label="Create your platform account">
                  Create Your Platform Account
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>

              <p className="text-sm text-gray-500 mt-4">
                No hidden fees • Secure setup • Full data privacy guaranteed
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="section bg-gradient-to-br from-gray-900 to-blue-900 text-white">
        <div className="container-clean">
          <div className="text-center max-w-4xl mx-auto">
            <div className="mb-8">
              <h2 className="text-display-md mb-4 text-white">
                Democratizing SME Finance
              </h2>
              <p className="text-body-xl text-blue-100 mb-6">
                We believe every business deserves access to fair, transparent financing based on their actual performance, not outdated credit scores.
              </p>
              <p className="text-lg text-blue-200">
                Join the new era of business lending where your success determines your rates, and your growth unlocks better terms.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Link to="/register">
                <Button className="bg-white text-gray-900 hover:bg-gray-100 px-8 py-4 text-lg h-14">
                  Join Our Platform
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link to="/dashboard">
                <Button variant="outline" className="border-blue-300 text-white hover:bg-blue-800 px-8 py-4 text-lg h-14">
                  Explore Platform Demo
                </Button>
              </Link>
            </div>

            {/* Social Proof */}
            <div className="flex flex-wrap justify-center items-center gap-8 text-blue-200 text-sm">
              <div className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                <span>500+ Swedish SMEs</span>
              </div>
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                <span>€50M+ funded</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4" />
                <span>4.9/5 rating</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>;
};
export default Index;