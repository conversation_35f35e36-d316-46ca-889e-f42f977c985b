/**
 * Cleanup Demo Data
 * Removes old "Demo Company AB" and ensures only Nordström GreenTech AB exists
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Clean up old demo data and ensure only Nordström GreenTech AB exists
 */
export async function cleanupDemoData(): Promise<void> {
  try {
    console.log('Starting demo data cleanup...');

    // 1. Find and delete old "Demo Company AB" and related data
    const { data: oldCompanies } = await supabase
      .from('companies')
      .select('id, company_name, organization_number')
      .or('company_name.eq.Demo Company AB,organization_number.eq.559223-3208');

    if (oldCompanies && oldCompanies.length > 0) {
      console.log(`Found ${oldCompanies.length} old demo companies to clean up:`, oldCompanies);

      for (const company of oldCompanies) {
        console.log(`Deleting company: ${company.company_name} (${company.organization_number})`);

        // Delete related data first (due to foreign key constraints)
        // Only delete from tables that actually exist in the current schema
        const { data: vouchers } = await supabase
          .from('vouchers')
          .select('id')
          .eq('company_id', company.id);

        if (vouchers && vouchers.length > 0) {
          const voucherIds = vouchers.map(v => v.id);
          
          // Delete voucher rows first
          await supabase
            .from('voucher_rows')
            .delete()
            .in('voucher_id', voucherIds);
        }

        // Delete vouchers
        await supabase.from('vouchers').delete().eq('company_id', company.id);
        
        // Delete chart of accounts
        await supabase.from('chart_of_accounts').delete().eq('company_id', company.id);

        // Finally delete the company
        await supabase.from('companies').delete().eq('id', company.id);
      }
    }

    // 2. Check if Nordström GreenTech AB exists
    const { data: nordstromCompany } = await supabase
      .from('companies')
      .select('*')
      .eq('company_name', 'Nordström GreenTech AB')
      .single();

    if (!nordstromCompany) {
      console.log('Nordström GreenTech AB not found, will be created by initializeDemoData()');
    } else {
      console.log('Nordström GreenTech AB found:', nordstromCompany);
    }

    // 3. Clean up any duplicate exchange rates
    const { data: duplicateRates } = await supabase
      .from('exchange_rates')
      .select('id, base_currency, target_currency, rate_date')
      .order('created_at', { ascending: false });

    if (duplicateRates && duplicateRates.length > 0) {
      // Keep only the latest rate for each currency pair and date
      const uniqueRates = new Map();
      const ratesToDelete = [];

      duplicateRates.forEach(rate => {
        const key = `${rate.base_currency}-${rate.target_currency}-${rate.rate_date}`;
        if (uniqueRates.has(key)) {
          ratesToDelete.push(rate.id);
        } else {
          uniqueRates.set(key, rate);
        }
      });

      if (ratesToDelete.length > 0) {
        console.log(`Removing ${ratesToDelete.length} duplicate exchange rates`);
        await supabase.from('exchange_rates').delete().in('id', ratesToDelete);
      }
    }

    console.log('Demo data cleanup completed successfully');

  } catch (error) {
    console.error('Error during demo data cleanup:', error);
    throw error;
  }
}

/**
 * Verify that only Nordström GreenTech AB exists
 */
export async function verifyDemoDataIntegrity(): Promise<{
  nordstromExists: boolean;
  otherCompaniesCount: number;
  vouchersCount: number;
  accountsCount: number;
}> {
  try {
    // Check for Nordström GreenTech AB
    const { data: nordstromCompany } = await supabase
      .from('companies')
      .select('id')
      .eq('company_name', 'Nordström GreenTech AB')
      .single();

    // Count other companies
    const { count: otherCompaniesCount } = await supabase
      .from('companies')
      .select('*', { count: 'exact', head: true })
      .neq('company_name', 'Nordström GreenTech AB');

    let vouchersCount = 0;
    let accountsCount = 0;

    if (nordstromCompany) {
      // Count vouchers for Nordström GreenTech AB
      const { count: vouchers } = await supabase
        .from('vouchers')
        .select('*', { count: 'exact', head: true })
        .eq('company_id', nordstromCompany.id);

      // Count chart of accounts for Nordström GreenTech AB
      const { count: accounts } = await supabase
        .from('chart_of_accounts')
        .select('*', { count: 'exact', head: true })
        .eq('company_id', nordstromCompany.id);

      vouchersCount = vouchers || 0;
      accountsCount = accounts || 0;
    }

    return {
      nordstromExists: !!nordstromCompany,
      otherCompaniesCount: otherCompaniesCount || 0,
      vouchersCount,
      accountsCount
    };

  } catch (error) {
    console.error('Error verifying demo data integrity:', error);
    return {
      nordstromExists: false,
      otherCompaniesCount: 0,
      vouchersCount: 0,
      accountsCount: 0
    };
  }
}

/**
 * Get company status for dashboard display
 */
export async function getCompanyStatus(): Promise<{
  id: string;
  name: string;
  orgNumber: string;
  syncStatus: string;
  lastSync: string | null;
  isConnected: boolean;
  vouchersCount: number;
  accountsCount: number;
} | null> {
  try {
    const { data: company } = await supabase
      .from('companies')
      .select('*')
      .eq('company_name', 'Nordström GreenTech AB')
      .single();

    if (!company) return null;

    // Count vouchers and accounts
    const [vouchersResult, accountsResult] = await Promise.all([
      supabase
        .from('vouchers')
        .select('*', { count: 'exact', head: true })
        .eq('company_id', company.id),
      supabase
        .from('chart_of_accounts')
        .select('*', { count: 'exact', head: true })
        .eq('company_id', company.id)
    ]);

    return {
      id: company.id,
      name: company.company_name,
      orgNumber: company.organization_number,
      syncStatus: company.sync_status || 'pending',
      lastSync: company.last_sync_at,
      isConnected: !!company.fortnox_access_token,
      vouchersCount: vouchersResult.count || 0,
      accountsCount: accountsResult.count || 0
    };

  } catch (error) {
    console.error('Error getting company status:', error);
    return null;
  }
}
