import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Shield, 
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3
} from 'lucide-react';
import MetricWidget from './MetricWidget';
import {
  LiquidityMetrics,
  WorkingCapitalMetrics,
  ProfitabilityMetrics,
  ComplianceMetrics,
  RateFactors
} from '@/utils/kpiCalculations';

interface KPIMonitoringProps {
  liquidityMetrics: LiquidityMetrics;
  workingCapitalMetrics: WorkingCapitalMetrics;
  profitabilityMetrics: ProfitabilityMetrics;
  complianceMetrics: ComplianceMetrics;
  rateFactors: RateFactors;
}

const KPIMonitoring: React.FC<KPIMonitoringProps> = ({
  liquidityMetrics,
  workingCapitalMetrics,
  profitabilityMetrics,
  complianceMetrics,
  rateFactors
}) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5 text-green-600" />;
    if (score >= 60) return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    return <AlertTriangle className="h-5 w-5 text-red-600" />;
  };

  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="space-y-6">
      {/* KPI Category Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              Liquidity & Cash Flow
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl font-bold">{rateFactors.liquidityScore.toFixed(0)}</span>
              {getScoreIcon(rateFactors.liquidityScore)}
            </div>
            <Progress value={rateFactors.liquidityScore} className="h-2 mb-2" />
            <Badge variant={getScoreBadgeVariant(rateFactors.liquidityScore)}>
              {rateFactors.liquidityScore >= 80 ? 'Excellent' : 
               rateFactors.liquidityScore >= 60 ? 'Good' : 'Needs Improvement'}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center">
              <Activity className="h-4 w-4 mr-2" />
              Working Capital
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl font-bold">{rateFactors.workingCapitalScore.toFixed(0)}</span>
              {getScoreIcon(rateFactors.workingCapitalScore)}
            </div>
            <Progress value={rateFactors.workingCapitalScore} className="h-2 mb-2" />
            <Badge variant={getScoreBadgeVariant(rateFactors.workingCapitalScore)}>
              {rateFactors.workingCapitalScore >= 80 ? 'Excellent' : 
               rateFactors.workingCapitalScore >= 60 ? 'Good' : 'Needs Improvement'}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              Profitability & Growth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl font-bold">{rateFactors.profitabilityScore.toFixed(0)}</span>
              {getScoreIcon(rateFactors.profitabilityScore)}
            </div>
            <Progress value={rateFactors.profitabilityScore} className="h-2 mb-2" />
            <Badge variant={getScoreBadgeVariant(rateFactors.profitabilityScore)}>
              {rateFactors.profitabilityScore >= 80 ? 'Excellent' : 
               rateFactors.profitabilityScore >= 60 ? 'Good' : 'Needs Improvement'}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center">
              <Shield className="h-4 w-4 mr-2" />
              Risk & Compliance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-2">
              <span className="text-2xl font-bold">{((rateFactors.riskScore + rateFactors.complianceScore) / 2).toFixed(0)}</span>
              {getScoreIcon((rateFactors.riskScore + rateFactors.complianceScore) / 2)}
            </div>
            <Progress value={(rateFactors.riskScore + rateFactors.complianceScore) / 2} className="h-2 mb-2" />
            <Badge variant={getScoreBadgeVariant((rateFactors.riskScore + rateFactors.complianceScore) / 2)}>
              {((rateFactors.riskScore + rateFactors.complianceScore) / 2) >= 80 ? 'Excellent' : 
               ((rateFactors.riskScore + rateFactors.complianceScore) / 2) >= 60 ? 'Good' : 'Needs Improvement'}
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Liquidity & Cash Flow Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Liquidity & Cash Flow Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <MetricWidget
              title="Liquidity Coverage Ratio"
              value={liquidityMetrics.daysLiquidityCoverage}
              format="days"
              change={{
                value: 5.2,
                period: 'vs last month',
                type: 'increase',
                isGoodChange: true
              }}
              icon={Target}
              size="sm"
            />
            
            <MetricWidget
              title="Cash Flow Coverage"
              value={liquidityMetrics.cashPosition / liquidityMetrics.cashBurnRate}
              format="number"
              change={{
                value: -2.1,
                period: 'vs last month',
                type: 'decrease',
                isGoodChange: false
              }}
              icon={Activity}
              size="sm"
            />
            
            <MetricWidget
              title="Quick Ratio"
              value={liquidityMetrics.totalLiquidity / liquidityMetrics.cashBurnRate}
              format="number"
              change={{
                value: 8.3,
                period: 'vs last month',
                type: 'increase',
                isGoodChange: true
              }}
              icon={BarChart3}
              size="sm"
            />
          </CardContent>
        </Card>

        {/* Working Capital Efficiency */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Working Capital Efficiency
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <MetricWidget
              title="DSO/DPO Balance"
              value={workingCapitalMetrics.dso - workingCapitalMetrics.dpo}
              format="days"
              change={{
                value: -3.2,
                period: 'vs last month',
                type: 'decrease',
                isGoodChange: true
              }}
              icon={Clock}
              size="sm"
            />
            
            <MetricWidget
              title="Inventory Turnover"
              value={365 / workingCapitalMetrics.inventoryDays}
              format="number"
              change={{
                value: 12.5,
                period: 'vs last month',
                type: 'increase',
                isGoodChange: true
              }}
              icon={TrendingUp}
              size="sm"
            />
            
            <MetricWidget
              title="Working Capital Cycle"
              value={workingCapitalMetrics.ccc}
              format="days"
              change={{
                value: -5.8,
                period: 'vs last month',
                type: 'decrease',
                isGoodChange: true
              }}
              icon={Activity}
              size="sm"
            />
          </CardContent>
        </Card>

        {/* Profitability & Growth */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Profitability & Growth
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <MetricWidget
              title="EBITDA Margin"
              value={profitabilityMetrics.ebitdaMargin}
              format="percentage"
              change={{
                value: 2.3,
                period: 'vs last month',
                type: 'increase',
                isGoodChange: true
              }}
              icon={Target}
              size="sm"
            />
            
            <MetricWidget
              title="Revenue Growth"
              value={profitabilityMetrics.revenueGrowth}
              format="percentage"
              change={{
                value: 1.8,
                period: 'vs last month',
                type: 'increase',
                isGoodChange: true
              }}
              icon={TrendingUp}
              size="sm"
            />
            
            <MetricWidget
              title="Customer Retention"
              value={profitabilityMetrics.customerRetention}
              format="percentage"
              change={{
                value: 0.5,
                period: 'vs last month',
                type: 'increase',
                isGoodChange: true
              }}
              icon={Shield}
              size="sm"
            />
          </CardContent>
        </Card>

        {/* Risk Mitigation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Risk Mitigation
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <MetricWidget
              title="FX Hedge Ratio"
              value={72}
              format="percentage"
              change={{
                value: 5.0,
                period: 'vs last month',
                type: 'increase',
                isGoodChange: true
              }}
              icon={Shield}
              size="sm"
            />
            
            <MetricWidget
              title="Covenant Compliance"
              value={complianceMetrics.covenantCompliance}
              format="percentage"
              change={{
                value: 0.2,
                period: 'vs last month',
                type: 'increase',
                isGoodChange: true
              }}
              icon={CheckCircle}
              size="sm"
            />
            
            <MetricWidget
              title="On-Time Tax Payments"
              value={complianceMetrics.onTimeTaxPayments}
              format="percentage"
              change={{
                value: 0,
                period: 'vs last month',
                type: 'neutral',
                isGoodChange: true
              }}
              icon={Target}
              size="sm"
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default KPIMonitoring;
