import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Copy, ExternalLink, CheckCircle } from 'lucide-react';
import { fortnoxConfig } from '@/integrations/fortnox/client';

const FortnoxSetupInfo: React.FC = () => {
  const [copied, setCopied] = useState(false);

  const redirectUri = fortnoxConfig.redirectUri;
  const clientId = fortnoxConfig.clientId;

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <span>Fortnox Integration Setup</span>
          <Badge variant="outline" className="bg-blue-50 text-blue-700">
            Developer Configuration
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">
            Configuration Required in Fortnox Developer Portal
          </h3>
          <p className="text-blue-800 text-sm mb-4">
            To complete the Fortnox integration, please configure the following redirect URI in your Fortnox developer application:
          </p>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-blue-900 mb-2">
                Client ID (Already Configured):
              </label>
              <div className="flex items-center gap-2 p-3 bg-white rounded border">
                <code className="flex-1 text-sm font-mono">{clientId}</code>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(clientId)}
                  className="h-8 px-2"
                >
                  {copied ? <CheckCircle className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                </Button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-blue-900 mb-2">
                Redirect URI (Copy this to Fortnox):
              </label>
              <div className="flex items-center gap-2 p-3 bg-white rounded border">
                <code className="flex-1 text-sm font-mono break-all">{redirectUri}</code>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard(redirectUri)}
                  className="h-8 px-2"
                >
                  {copied ? <CheckCircle className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-3">
          <h4 className="font-semibold text-gray-900">Setup Instructions:</h4>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
            <li>Go to the Fortnox Developer Portal</li>
            <li>Navigate to your application settings</li>
            <li>Add the redirect URI shown above to your application configuration</li>
            <li>Save the changes in the Fortnox portal</li>
            <li>Return here and test the connection</li>
          </ol>
        </div>

        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={() => window.open('https://www.fortnox.se/developer', '_blank')}
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open Fortnox Developer Portal
          </Button>
          <Button
            onClick={() => window.open('https://api.fortnox.se/apidocs', '_blank')}
            variant="outline"
            className="flex items-center gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            API Documentation
          </Button>
        </div>

        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <h4 className="font-semibold text-amber-900 mb-2">Important Notes:</h4>
          <ul className="list-disc list-inside space-y-1 text-sm text-amber-800">
            <li>The redirect URI must match exactly (including protocol and port)</li>
            <li>For production deployment, update the redirect URI accordingly</li>
            <li>Ensure your Fortnox application has the necessary scopes enabled</li>
            <li>The client secret is securely configured in the application</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default FortnoxSetupInfo;
