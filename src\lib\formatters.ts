/**
 * Formatting Utilities
 * Centralized formatting functions for the Swedish fintech platform
 */

import { format, parseISO, isValid } from 'date-fns';
import { sv } from 'date-fns/locale';
import { SWEDISH_LOCALE, DATE_FORMATS, NUMBER_FORMATS } from './constants';

// Number formatters
const currencyFormatter = new Intl.NumberFormat(SWEDISH_LOCALE, NUMBER_FORMATS.CURRENCY);
const integerFormatter = new Intl.NumberFormat(SWEDISH_LOCALE, { maximumFractionDigits: 0 });

/**
 * Formats a number as Swedish currency (SEK)
 */
export function formatCurrency(value: number | string | null | undefined): string {
  if (value === null || value === undefined || value === '') {
    return '0 kr';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numValue)) {
    return '0 kr';
  }

  return currencyFormatter.format(numValue);
}

/**
 * Formats a number as percentage
 */
export function formatPercentage(value: number | string | null | undefined, decimals: number = 1): string {
  if (value === null || value === undefined || value === '') {
    return '0%';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numValue)) {
    return '0%';
  }

  const formatter = new Intl.NumberFormat(SWEDISH_LOCALE, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });

  // Convert to percentage if the value is not already in percentage form
  const percentValue = numValue > 1 ? numValue / 100 : numValue;

  return formatter.format(percentValue);
}

/**
 * Formats a number with specified decimal places
 */
export function formatNumber(value: number | string | null | undefined, decimals: number = 0): string {
  if (value === null || value === undefined || value === '') {
    return '0';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numValue)) {
    return '0';
  }

  const formatter = new Intl.NumberFormat(SWEDISH_LOCALE, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });

  return formatter.format(numValue);
}

/**
 * Formats a number as integer (no decimals)
 */
export function formatInteger(value: number | string | null | undefined): string {
  if (value === null || value === undefined || value === '') {
    return '0';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numValue)) {
    return '0';
  }

  return integerFormatter.format(Math.round(numValue));
}

/**
 * Formats a number with appropriate suffix (K, M, B)
 */
export function formatCompactNumber(value: number | string | null | undefined): string {
  if (value === null || value === undefined || value === '') {
    return '0';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numValue)) {
    return '0';
  }

  const formatter = new Intl.NumberFormat(SWEDISH_LOCALE, {
    notation: 'compact',
    compactDisplay: 'short',
  });

  return formatter.format(numValue);
}

/**
 * Formats days with proper pluralization
 */
export function formatDays(value: number | string | null | undefined): string {
  if (value === null || value === undefined || value === '') {
    return '0 dagar';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(numValue)) {
    return '0 dagar';
  }

  const rounded = Math.round(numValue);
  return `${rounded} ${rounded === 1 ? 'dag' : 'dagar'}`;
}

/**
 * Formats time duration in hours and minutes
 */
export function formatDuration(hours: number | string | null | undefined): string {
  if (hours === null || hours === undefined || hours === '') {
    return '0h 0m';
  }

  const numHours = typeof hours === 'string' ? parseFloat(hours) : hours;

  if (isNaN(numHours)) {
    return '0h 0m';
  }

  const wholeHours = Math.floor(numHours);
  const minutes = Math.round((numHours - wholeHours) * 60);

  if (wholeHours === 0) {
    return `${minutes}m`;
  }

  if (minutes === 0) {
    return `${wholeHours}h`;
  }

  return `${wholeHours}h ${minutes}m`;
}

/**
 * Formats a date using Swedish locale
 */
export function formatDate(
  date: Date | string | null | undefined,
  formatString: string = DATE_FORMATS.SHORT
): string {
  if (!date) {
    return '';
  }

  let dateObj: Date;

  if (typeof date === 'string') {
    dateObj = parseISO(date);
  } else {
    dateObj = date;
  }

  if (!isValid(dateObj)) {
    return '';
  }

  return format(dateObj, formatString, { locale: sv });
}

/**
 * Formats a date for display (long format)
 */
export function formatDateLong(date: Date | string | null | undefined): string {
  return formatDate(date, DATE_FORMATS.LONG);
}

/**
 * Formats a date and time
 */
export function formatDateTime(date: Date | string | null | undefined): string {
  return formatDate(date, DATE_FORMATS.DATETIME);
}

/**
 * Formats time only
 */
export function formatTime(date: Date | string | null | undefined): string {
  return formatDate(date, DATE_FORMATS.TIME);
}

/**
 * Formats a relative date (e.g., "2 days ago")
 */
export function formatRelativeDate(date: Date | string | null | undefined): string {
  if (!date) {
    return '';
  }

  let dateObj: Date;

  if (typeof date === 'string') {
    dateObj = parseISO(date);
  } else {
    dateObj = date;
  }

  if (!isValid(dateObj)) {
    return '';
  }

  const now = new Date();
  const diffInMs = now.getTime() - dateObj.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return 'Idag';
  } else if (diffInDays === 1) {
    return 'Igår';
  } else if (diffInDays < 7) {
    return `${diffInDays} dagar sedan`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return `${weeks} ${weeks === 1 ? 'vecka' : 'veckor'} sedan`;
  } else if (diffInDays < 365) {
    const months = Math.floor(diffInDays / 30);
    return `${months} ${months === 1 ? 'månad' : 'månader'} sedan`;
  } else {
    const years = Math.floor(diffInDays / 365);
    return `${years} ${years === 1 ? 'år' : 'år'} sedan`;
  }
}

/**
 * Formats a Swedish organization number
 */
export function formatOrganizationNumber(orgNumber: string | null | undefined): string {
  if (!orgNumber) {
    return '';
  }

  // Remove all non-digits
  const digits = orgNumber.replace(/\D/g, '');

  if (digits.length === 10) {
    return `${digits.slice(0, 6)}-${digits.slice(6)}`;
  }

  return orgNumber;
}

/**
 * Formats a Swedish personal identity number (personnummer)
 */
export function formatPersonnummer(personnummer: string | null | undefined): string {
  if (!personnummer) {
    return '';
  }

  // Remove all non-digits
  const digits = personnummer.replace(/\D/g, '');

  if (digits.length === 10) {
    return `${digits.slice(0, 6)}-${digits.slice(6)}`;
  } else if (digits.length === 12) {
    return `${digits.slice(0, 8)}-${digits.slice(8)}`;
  }

  return personnummer;
}

/**
 * Formats file size in human readable format
 */
export function formatFileSize(bytes: number | null | undefined): string {
  if (!bytes || bytes === 0) {
    return '0 B';
  }

  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));

  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
}

/**
 * Truncates text with ellipsis
 */
export function truncateText(text: string | null | undefined, maxLength: number = 50): string {
  if (!text) {
    return '';
  }

  if (text.length <= maxLength) {
    return text;
  }

  return `${text.slice(0, maxLength)}...`;
}

/**
 * Capitalizes the first letter of a string
 */
export function capitalize(text: string | null | undefined): string {
  if (!text) {
    return '';
  }

  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}
