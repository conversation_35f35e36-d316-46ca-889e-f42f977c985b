/**
 * BAS 2025 Compliant Financial Statements Generator
 * Generates balance sheet and income statement from verifikationer data
 */

import { supabase } from '@/integrations/supabase/client';
import { isBalanceSheetAccount, isIncomeStatementAccount, getBASCategory } from '../demo/bas-2025-accounts';

export interface BalanceSheetData {
  assets: {
    current_assets: BalanceSheetItem[];
    fixed_assets: BalanceSheetItem[];
    total_assets: number;
  };
  liabilities: {
    current_liabilities: BalanceSheetItem[];
    long_term_liabilities: BalanceSheetItem[];
    total_liabilities: number;
  };
  equity: {
    equity_items: BalanceSheetItem[];
    total_equity: number;
  };
  total_liabilities_and_equity: number;
}

export interface IncomeStatementData {
  revenue: {
    operating_revenue: IncomeStatementItem[];
    total_revenue: number;
  };
  expenses: {
    cost_of_goods_sold: IncomeStatementItem[];
    personnel_costs: IncomeStatementItem[];
    other_operating_expenses: IncomeStatementItem[];
    financial_costs: IncomeStatementItem[];
    total_expenses: number;
  };
  gross_profit: number;
  operating_profit: number;
  net_profit: number;
}

export interface BalanceSheetItem {
  account_code: string;
  account_name: string;
  balance: number;
  bas_category: string;
}

export interface IncomeStatementItem {
  account_code: string;
  account_name: string;
  amount: number;
  bas_category: string;
}

export interface CashFlowData {
  operating_activities: number;
  investing_activities: number;
  financing_activities: number;
  net_cash_flow: number;
  opening_cash: number;
  closing_cash: number;
}

export interface WorkingCapitalMetrics {
  current_assets: number;
  current_liabilities: number;
  working_capital: number;
  current_ratio: number;
  quick_ratio: number;
  days_sales_outstanding: number;
  days_payable_outstanding: number;
  inventory_turnover: number;
}

/**
 * Generate BAS 2025 compliant balance sheet
 */
export async function generateBalanceSheet(
  companyId: string,
  asOfDate: string = new Date().toISOString().split('T')[0]
): Promise<BalanceSheetData> {
  // Get all voucher rows up to the specified date
  const { data: voucherRows } = await supabase
    .from('voucher_rows')
    .select(`
      account_code,
      account_name,
      debit_amount,
      credit_amount,
      exchange_rate,
      vouchers!inner(
        company_id,
        transaction_date
      )
    `)
    .eq('vouchers.company_id', companyId)
    .lte('vouchers.transaction_date', asOfDate);

  if (!voucherRows) throw new Error('Failed to fetch voucher data');

  // Calculate account balances
  const accountBalances: Record<string, { name: string; balance: number }> = {};

  voucherRows.forEach(row => {
    if (!isBalanceSheetAccount(row.account_code)) return;

    const accountCode = row.account_code;
    const debitSEK = row.debit_amount / (row.exchange_rate || 1);
    const creditSEK = row.credit_amount / (row.exchange_rate || 1);

    if (!accountBalances[accountCode]) {
      accountBalances[accountCode] = {
        name: row.account_name || '',
        balance: 0
      };
    }

    // Assets and expenses increase with debits, decrease with credits
    // Liabilities, equity, and revenue increase with credits, decrease with debits
    const accountType = getAccountTypeFromCode(accountCode);
    if (accountType === 'asset') {
      accountBalances[accountCode].balance += debitSEK - creditSEK;
    } else {
      accountBalances[accountCode].balance += creditSEK - debitSEK;
    }
  });

  // Categorize accounts
  const assets = { current_assets: [], fixed_assets: [], total_assets: 0 };
  const liabilities = { current_liabilities: [], long_term_liabilities: [], total_liabilities: 0 };
  const equity = { equity_items: [], total_equity: 0 };

  Object.entries(accountBalances).forEach(([accountCode, data]) => {
    const accountType = getAccountTypeFromCode(accountCode);
    const basCategory = getBASCategory(accountCode);
    const item = {
      account_code: accountCode,
      account_name: data.name,
      balance: data.balance,
      bas_category: basCategory
    };

    if (accountType === 'asset') {
      const code = parseInt(accountCode);
      if (code >= 1000 && code <= 1699) {
        assets.current_assets.push(item);
      } else {
        assets.fixed_assets.push(item);
      }
      assets.total_assets += data.balance;
    } else if (accountType === 'liability') {
      const code = parseInt(accountCode);
      if (code >= 2000 && code <= 2399) {
        liabilities.current_liabilities.push(item);
      } else {
        liabilities.long_term_liabilities.push(item);
      }
      liabilities.total_liabilities += data.balance;
    } else if (accountType === 'equity') {
      equity.equity_items.push(item);
      equity.total_equity += data.balance;
    }
  });

  return {
    assets,
    liabilities,
    equity,
    total_liabilities_and_equity: liabilities.total_liabilities + equity.total_equity
  };
}

/**
 * Generate BAS 2025 compliant income statement
 */
export async function generateIncomeStatement(
  companyId: string,
  fromDate: string,
  toDate: string = new Date().toISOString().split('T')[0]
): Promise<IncomeStatementData> {
  // Get all voucher rows for the period
  const { data: voucherRows } = await supabase
    .from('voucher_rows')
    .select(`
      account_code,
      account_name,
      debit_amount,
      credit_amount,
      exchange_rate,
      vouchers!inner(
        company_id,
        transaction_date
      )
    `)
    .eq('vouchers.company_id', companyId)
    .gte('vouchers.transaction_date', fromDate)
    .lte('vouchers.transaction_date', toDate);

  if (!voucherRows) throw new Error('Failed to fetch voucher data');

  // Calculate account totals
  const accountTotals: Record<string, { name: string; amount: number }> = {};

  voucherRows.forEach(row => {
    if (!isIncomeStatementAccount(row.account_code)) return;

    const accountCode = row.account_code;
    const debitSEK = row.debit_amount / (row.exchange_rate || 1);
    const creditSEK = row.credit_amount / (row.exchange_rate || 1);

    if (!accountTotals[accountCode]) {
      accountTotals[accountCode] = {
        name: row.account_name || '',
        amount: 0
      };
    }

    const accountType = getAccountTypeFromCode(accountCode);
    if (accountType === 'revenue') {
      accountTotals[accountCode].amount += creditSEK - debitSEK;
    } else if (accountType === 'expense') {
      accountTotals[accountCode].amount += debitSEK - creditSEK;
    }
  });

  // Categorize accounts
  const revenue = { operating_revenue: [], total_revenue: 0 };
  const expenses = {
    cost_of_goods_sold: [],
    personnel_costs: [],
    other_operating_expenses: [],
    financial_costs: [],
    total_expenses: 0
  };

  Object.entries(accountTotals).forEach(([accountCode, data]) => {
    const accountType = getAccountTypeFromCode(accountCode);
    const basCategory = getBASCategory(accountCode);
    const item = {
      account_code: accountCode,
      account_name: data.name,
      amount: data.amount,
      bas_category: basCategory
    };

    if (accountType === 'revenue') {
      revenue.operating_revenue.push(item);
      revenue.total_revenue += data.amount;
    } else if (accountType === 'expense') {
      const code = parseInt(accountCode);
      if (code >= 4000 && code <= 4999) {
        expenses.cost_of_goods_sold.push(item);
      } else if (code >= 5000 && code <= 5999) {
        expenses.personnel_costs.push(item);
      } else if (code >= 6000 && code <= 7999) {
        expenses.other_operating_expenses.push(item);
      } else if (code >= 8000 && code <= 8999) {
        expenses.financial_costs.push(item);
      }
      expenses.total_expenses += data.amount;
    }
  });

  const gross_profit = revenue.total_revenue - expenses.cost_of_goods_sold.reduce((sum, item) => sum + item.amount, 0);
  const operating_profit = gross_profit - expenses.personnel_costs.reduce((sum, item) => sum + item.amount, 0) - expenses.other_operating_expenses.reduce((sum, item) => sum + item.amount, 0);
  const net_profit = operating_profit - expenses.financial_costs.reduce((sum, item) => sum + item.amount, 0);

  return {
    revenue,
    expenses,
    gross_profit,
    operating_profit,
    net_profit
  };
}

/**
 * Calculate working capital metrics
 */
export async function calculateWorkingCapitalMetrics(
  companyId: string,
  asOfDate: string = new Date().toISOString().split('T')[0]
): Promise<WorkingCapitalMetrics> {
  const balanceSheet = await generateBalanceSheet(companyId, asOfDate);
  
  const current_assets = balanceSheet.assets.current_assets.reduce((sum, item) => sum + item.balance, 0);
  const current_liabilities = balanceSheet.liabilities.current_liabilities.reduce((sum, item) => sum + item.balance, 0);
  const working_capital = current_assets - current_liabilities;
  const current_ratio = current_liabilities > 0 ? current_assets / current_liabilities : 0;
  
  // Quick ratio (excluding inventory)
  const inventory = balanceSheet.assets.current_assets
    .filter(item => item.account_code.startsWith('14'))
    .reduce((sum, item) => sum + item.balance, 0);
  const quick_assets = current_assets - inventory;
  const quick_ratio = current_liabilities > 0 ? quick_assets / current_liabilities : 0;

  // Calculate DSO and DPO (simplified calculations)
  const accounts_receivable = balanceSheet.assets.current_assets
    .filter(item => item.account_code.startsWith('15'))
    .reduce((sum, item) => sum + item.balance, 0);
  const accounts_payable = balanceSheet.liabilities.current_liabilities
    .filter(item => item.account_code.startsWith('20'))
    .reduce((sum, item) => sum + item.balance, 0);

  // Get annual revenue for DSO calculation
  const yearStart = new Date(asOfDate);
  yearStart.setMonth(0, 1);
  const incomeStatement = await generateIncomeStatement(companyId, yearStart.toISOString().split('T')[0], asOfDate);
  const annual_revenue = incomeStatement.revenue.total_revenue;

  const days_sales_outstanding = annual_revenue > 0 ? (accounts_receivable / annual_revenue) * 365 : 0;
  const days_payable_outstanding = annual_revenue > 0 ? (accounts_payable / annual_revenue) * 365 : 0;
  const inventory_turnover = inventory > 0 ? incomeStatement.expenses.cost_of_goods_sold.reduce((sum, item) => sum + item.amount, 0) / inventory : 0;

  return {
    current_assets,
    current_liabilities,
    working_capital,
    current_ratio,
    quick_ratio,
    days_sales_outstanding,
    days_payable_outstanding,
    inventory_turnover
  };
}

/**
 * Get account type from BAS account code
 */
function getAccountTypeFromCode(accountCode: string): string {
  const code = parseInt(accountCode);
  
  if (code >= 1000 && code <= 1999) return 'asset';
  if (code >= 2000 && code <= 2999) {
    // Special handling for equity accounts in 2000 range
    if (code >= 2010 && code <= 2099) return 'equity';
    return 'liability';
  }
  if (code >= 3000 && code <= 3999) return 'revenue';
  if (code >= 4000 && code <= 8999) return 'expense';
  
  return 'expense'; // Default
}
