import React from 'react';
import { cn } from '@/lib/utils';
import { CheckIcon } from 'lucide-react';

interface ProgressStep {
  id: string;
  label: string;
}

interface ProgressIndicatorProps {
  steps: ProgressStep[];
  currentStep: string;
  completedSteps: string[];
  className?: string;
}

const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  steps,
  currentStep,
  completedSteps,
  className,
}) => {
  return (
    <div className={cn("w-full", className)}>
      <div className="hidden md:flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.includes(step.id);
          const isCurrent = step.id === currentStep;
          
          return (
            <React.Fragment key={step.id}>
              {/* Step circle */}
              <div className="flex flex-col items-center">
                <div 
                  className={cn(
                    "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors",
                    isCompleted ? "bg-primary border-primary" : 
                    isCurrent ? "border-primary bg-white" : 
                    "border-gray-300 bg-white"
                  )}
                >
                  {isCompleted ? (
                    <CheckIcon className="w-5 h-5 text-white" />
                  ) : (
                    <span className={cn(
                      "text-sm font-medium",
                      isCurrent ? "text-primary" : "text-gray-700"
                    )}>
                      {index + 1}
                    </span>
                  )}
                </div>
                <span className={cn(
                  "mt-2 text-body-small font-medium",
                  isCurrent ? "text-primary" : 
                  isCompleted ? "text-gray-900" : "text-gray-700"
                )}>
                  {step.label}
                </span>
              </div>
              
              {/* Connector line */}
              {index < steps.length - 1 && (
                <div className={cn(
                  "flex-1 h-0.5 mx-4",
                  index < steps.indexOf(steps.find(s => s.id === currentStep)!) 
                    ? "bg-primary" : "bg-gray-300"
                )} />
              )}
            </React.Fragment>
          );
        })}
      </div>
      
      {/* Mobile view */}
      <div className="flex md:hidden items-center justify-between px-4 py-3 bg-white rounded-lg border border-gray-300">
        <span className="text-body-small font-medium text-gray-900">
          Step {steps.findIndex(s => s.id === currentStep) + 1} of {steps.length}
        </span>
        <span className="text-body-small font-medium text-primary">
          {steps.find(s => s.id === currentStep)?.label}
        </span>
      </div>
    </div>
  );
};

export default ProgressIndicator;
