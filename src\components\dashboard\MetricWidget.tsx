import React from 'react';
import { LucideIcon, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { formatCurrency, formatPercentage, formatNumber, formatDays, formatCompactNumber } from '@/lib/formatters';
import { cn } from '@/lib/utils';

export type MetricFormat = 'currency' | 'percentage' | 'number' | 'days' | 'compact' | 'custom';
export type MetricSize = 'sm' | 'md' | 'lg';
export type TrendType = 'increase' | 'decrease' | 'neutral';

interface MetricChange {
  value: number;
  period: string;
  type: TrendType;
  isGoodChange?: boolean; // Whether the change is positive for the business
}

interface MetricWidgetProps {
  title: string;
  value: string | number | null | undefined;
  change?: MetricChange;
  icon?: LucideIcon;
  format?: MetricFormat;
  customFormatter?: (value: string | number | null | undefined) => string;
  className?: string;
  size?: MetricSize;
  loading?: boolean;
  error?: boolean;
  subtitle?: string;
  onClick?: () => void;
}

const MetricWidget: React.FC<MetricWidgetProps> = ({
  title,
  value,
  change,
  icon: Icon,
  format = 'number',
  customFormatter,
  className = '',
  size = 'md',
  loading = false,
  error = false,
  subtitle,
  onClick
}) => {
  const formatValue = (val: string | number | null | undefined): string => {
    if (customFormatter) {
      return customFormatter(val);
    }

    switch (format) {
      case 'currency':
        return formatCurrency(val);
      case 'percentage':
        return formatPercentage(val);
      case 'days':
        return formatDays(val);
      case 'compact':
        return formatCompactNumber(val);
      case 'number':
      default:
        return formatNumber(val);
    }
  };

  const getTrendIcon = () => {
    if (!change) {
      return null;
    }

    switch (change.type) {
      case 'increase':
        return <TrendingUp className="h-4 w-4" />;
      case 'decrease':
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <Minus className="h-4 w-4" />;
    }
  };

  const getTrendColor = () => {
    if (!change) {
      return '';
    }

    // If isGoodChange is specified, use that to determine color
    if (change.isGoodChange !== undefined) {
      return change.isGoodChange ? 'text-success-600' : 'text-error-600';
    }

    // Default behavior: increase = green, decrease = red
    switch (change.type) {
      case 'increase':
        return 'text-success-600';
      case 'decrease':
        return 'text-error-600';
      default:
        return 'text-gray-500';
    }
  };

  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const titleSizeClasses = {
    sm: 'text-body-sm',
    md: 'text-body-md',
    lg: 'text-body-lg'
  };

  const valueSizeClasses = {
    sm: 'text-heading-md',
    md: 'text-heading-lg',
    lg: 'text-heading-xl'
  };

  // Loading skeleton
  if (loading) {
    return (
      <div className={cn('card-clean animate-pulse', sizeClasses[size], className)}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <div className="h-4 w-4 bg-gray-200 rounded"></div>
              <div className="h-4 w-24 bg-gray-200 rounded"></div>
            </div>
            <div className="h-8 w-32 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 w-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn('card-clean border-error-200 bg-error-50', sizeClasses[size], className)}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              {Icon && <Icon className="h-4 w-4 text-error-500" />}
              <p className={cn(titleSizeClasses[size], 'text-error-600 font-medium')}>
                {title}
              </p>
            </div>
            <div className={cn(valueSizeClasses[size], 'font-semibold text-error-700 mb-2')}>
              Error loading data
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        'card-clean transition-all duration-200',
        sizeClasses[size],
        onClick && 'cursor-pointer hover:shadow-card-hover hover:scale-[1.02]',
        className
      )}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      tabIndex={onClick ? 0 : undefined}
      onKeyDown={onClick ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick();
        }
      } : undefined}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            {Icon && <Icon className="h-4 w-4 text-gray-500" />}
            <p className={cn(titleSizeClasses[size], 'text-gray-600 font-medium')}>
              {title}
            </p>
          </div>

          {subtitle && (
            <p className="text-body-xs text-gray-500 mb-2">
              {subtitle}
            </p>
          )}

          <div className={cn(valueSizeClasses[size], 'font-semibold text-gray-900 mb-2')}>
            {formatValue(value)}
          </div>

          {change && (
            <div className={cn('flex items-center space-x-1', getTrendColor())}>
              {getTrendIcon()}
              <span className="text-body-sm font-medium">
                {change.value > 0 ? '+' : ''}{formatPercentage(change.value / 100)}
              </span>
              <span className="text-body-sm text-gray-500">
                {change.period}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MetricWidget;
