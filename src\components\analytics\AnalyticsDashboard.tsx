
import React, { useState, useEffect } from 'react';
import { Bar<PERSON>hart3, Clock, Package, TrendingUp, MoreHorizontal, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ModuleCard from '@/components/dashboard/ModuleCard';
import MetricWidget from '@/components/dashboard/MetricWidget';
import ChartContainer from '@/components/dashboard/ChartContainer';
import DSODPOTrends from '@/components/analytics/DSODPOTrends';
import InventoryTurnover from '@/components/analytics/InventoryTurnover';
import { supabase } from '@/integrations/supabase/client';

interface WorkingCapitalMetrics {
  dso: number;
  dpo: number;
  inventoryTurnover: number;
  cashConversionCycle: number;
  workingCapital: number;
  inventoryDays: number;
}

const AnalyticsDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<WorkingCapitalMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadWorkingCapitalData();
  }, []);

  const loadWorkingCapitalData = async () => {
    try {
      setLoading(true);

      // Get demo company
      const { data: companies } = await supabase
        .from('companies')
        .select('*')
        .eq('company_name', 'Nordström GreenTech AB')
        .limit(1);

      if (!companies || companies.length === 0) {
        // Use mock data if no company found
        setMetrics({
          dso: 32,
          dpo: 45,
          inventoryTurnover: 8.2,
          cashConversionCycle: 28,
          workingCapital: 4200000,
          inventoryDays: 30
        });
        return;
      }

      const companyId = companies[0].id;

      // Import working capital calculations
      const { calculateWorkingCapitalMetrics } = await import('@/utils/kpiCalculations');
      const workingCapitalMetrics = await calculateWorkingCapitalMetrics(companyId);

      // Calculate working capital amount (simplified)
      const workingCapital = 4200000; // Would be calculated from current assets - current liabilities

      setMetrics({
        dso: workingCapitalMetrics.dso,
        dpo: workingCapitalMetrics.dpo,
        cashConversionCycle: workingCapitalMetrics.ccc, // Map ccc to cashConversionCycle
        inventoryDays: workingCapitalMetrics.inventoryDays,
        inventoryTurnover: 8.2, // Would be calculated from inventory data
        workingCapital
      });

    } catch (error) {
      console.error('Error loading working capital data:', error);
      // Fallback to mock data
      setMetrics({
        dso: 32,
        dpo: 45,
        inventoryTurnover: 8.2,
        cashConversionCycle: 28,
        workingCapital: 4200000,
        inventoryDays: 30
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Data</h3>
        <p className="text-gray-500 mb-4">Initialize demo data to see analytics insights</p>
        <Button onClick={loadWorkingCapitalData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Data
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-display-sm">Growth Analytics</h1>
          <p className="text-body-lg text-gray-600 mt-2">
            Monitor operational efficiency and working capital optimization
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="btn-secondary">
            Industry Benchmark
          </Button>
          <Button className="btn-primary">
            Optimize Working Capital
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Days Sales Outstanding"
          value={metrics.dso}
          format="days"
          change={{
            value: -3.2,
            period: 'vs last quarter',
            type: 'decrease'
          }}
          icon={Clock}
        />

        <MetricWidget
          title="Days Payable Outstanding"
          value={metrics.dpo}
          format="days"
          change={{
            value: 2.1,
            period: 'vs last quarter',
            type: 'increase'
          }}
          icon={Clock}
        />

        <MetricWidget
          title="Inventory Turnover"
          value={metrics.inventoryTurnover}
          format="number"
          change={{
            value: 12.5,
            period: 'vs last quarter',
            type: 'increase'
          }}
          icon={Package}
        />

        <MetricWidget
          title="Cash Conversion Cycle"
          value={metrics.cashConversionCycle}
          format="days"
          change={{
            value: -5.8,
            period: 'vs last quarter',
            type: 'decrease'
          }}
          icon={TrendingUp}
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* DSO/DPO Trends */}
        <div className="w-full">
          <ChartContainer
            title="DSO/DPO Trends"
            description="Working capital efficiency over time"
            icon={Clock}
            headerActions={
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            }
          >
            <div className="w-full h-full">
              <DSODPOTrends />
            </div>
          </ChartContainer>
        </div>

        {/* Inventory Turnover */}
        <div className="w-full">
          <ChartContainer
            title="Inventory Turnover Analysis"
            description="Inventory efficiency by category"
            icon={Package}
            headerActions={
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            }
          >
            <div className="w-full h-full">
              <InventoryTurnover />
            </div>
          </ChartContainer>
        </div>
      </div>

      {/* Industry Benchmarking */}
      <ModuleCard
        title="Industry Benchmarking"
        description="Compare your performance against Swedish industry standards"
        icon={BarChart3}
        headerActions={
          <Button variant="outline" size="sm" className="btn-secondary">
            View Full Report
          </Button>
        }
      >
        <div className="space-y-6">
          {/* Benchmark Comparison */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="text-body-sm text-gray-600 mb-2">DSO vs Industry</div>
              <div className="flex items-center space-x-2 mb-1">
                <div className="text-heading-md font-semibold text-success-600">{metrics.dso} days</div>
                <div className="text-body-sm text-success-600">-8 days</div>
              </div>
              <div className="text-body-xs text-gray-500">Industry avg: 40 days</div>
            </div>

            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="text-body-sm text-gray-600 mb-2">DPO vs Industry</div>
              <div className="flex items-center space-x-2 mb-1">
                <div className="text-heading-md font-semibold text-success-600">{metrics.dpo} days</div>
                <div className="text-body-sm text-success-600">+8 days</div>
              </div>
              <div className="text-body-xs text-gray-500">Industry avg: 37 days</div>
            </div>

            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="text-body-sm text-gray-600 mb-2">Inventory Turnover</div>
              <div className="flex items-center space-x-2 mb-1">
                <div className="text-heading-md font-semibold text-yellow-600">{metrics.inventoryTurnover}x</div>
                <div className="text-body-sm text-yellow-600">-1.3x</div>
              </div>
              <div className="text-body-xs text-gray-500">Industry avg: 9.5x</div>
            </div>

            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="text-body-sm text-gray-600 mb-2">Cash Conversion</div>
              <div className="flex items-center space-x-2 mb-1">
                <div className="text-heading-md font-semibold text-success-600">{metrics.cashConversionCycle} days</div>
                <div className="text-body-sm text-success-600">-12 days</div>
              </div>
              <div className="text-body-xs text-gray-500">Industry avg: 40 days</div>
            </div>
          </div>

          {/* Performance Score */}
          <div className="text-center p-6 bg-success-50 border border-success-200 rounded-lg">
            <div className="text-4xl font-bold text-success-700 mb-2">85</div>
            <div className="text-body-lg text-success-600 font-medium">Working Capital Efficiency Score</div>
            <div className="text-body-sm text-success-600 mt-1">Top 25% in your industry</div>
          </div>

          {/* Optimization Opportunities */}
          <div className="space-y-3">
            <h4 className="text-heading-sm">Optimization Opportunities</h4>
            <ul className="space-y-2">
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Improve inventory turnover by 15% to match industry leaders (target: 9.5x)
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Negotiate extended payment terms with suppliers (target: 50+ days DPO)
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Implement automated invoicing to reduce DSO by additional 3-5 days
                </span>
              </li>
            </ul>
          </div>
        </div>
      </ModuleCard>
    </div>
  );
};

export default AnalyticsDashboard;
