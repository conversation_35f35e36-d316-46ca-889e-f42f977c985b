import React from 'react';

const CashFlowWaterfall: React.FC = () => {
  // Mock cash flow data
  const data = [
    { label: 'Starting Cash', value: 16600000, type: 'start', cumulative: 16600000 },
    { label: 'Revenue', value: 1200000, type: 'positive', cumulative: 17800000 },
    { label: 'Operating Expenses', value: -1850000, type: 'negative', cumulative: 15950000 },
    { label: 'Marketing', value: -300000, type: 'negative', cumulative: 15650000 },
    { label: 'R&D', value: -450000, type: 'negative', cumulative: 15200000 },
    { label: 'Other', value: -150000, type: 'negative', cumulative: 15050000 },
    { label: 'Ending Cash', value: 15050000, type: 'end', cumulative: 15050000 }
  ];

  const maxValue = Math.max(...data.map(d => Math.abs(d.cumulative)));
  const chartHeight = 200;
  const barWidth = 80;
  const spacing = 20;

  const getBarColor = (type: string) => {
    switch (type) {
      case 'start':
      case 'end':
        return '#111827';
      case 'positive':
        return '#10b981';
      case 'negative':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(Math.abs(value));
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Chart Area */}
      <div className="flex-1 relative overflow-x-auto">
        <svg 
          className="w-full h-full min-w-[600px]" 
          viewBox={`0 0 ${data.length * (barWidth + spacing)} ${chartHeight + 100}`}
        >
          {/* Grid Lines */}
          <defs>
            <pattern id="waterfallGrid" width="100" height="40" patternUnits="userSpaceOnUse">
              <path d="M 0 40 L 100 40" stroke="#f3f4f6" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height={chartHeight} fill="url(#waterfallGrid)" />
          
          {/* Bars */}
          {data.map((item, index) => {
            const x = index * (barWidth + spacing) + spacing;
            const barHeight = Math.abs(item.type === 'start' || item.type === 'end' 
              ? item.cumulative 
              : item.value) / maxValue * chartHeight * 0.8;
            
            let y;
            if (item.type === 'start' || item.type === 'end') {
              y = chartHeight - barHeight;
            } else {
              const prevCumulative = index > 0 ? data[index - 1].cumulative : 0;
              if (item.value > 0) {
                y = chartHeight - (prevCumulative / maxValue * chartHeight * 0.8) - barHeight;
              } else {
                y = chartHeight - (prevCumulative / maxValue * chartHeight * 0.8);
              }
            }

            return (
              <g key={index}>
                {/* Bar */}
                <rect
                  x={x}
                  y={y}
                  width={barWidth}
                  height={barHeight}
                  fill={getBarColor(item.type)}
                  className="hover:opacity-80 transition-opacity cursor-pointer"
                />
                
                {/* Value Label */}
                <text
                  x={x + barWidth / 2}
                  y={y - 5}
                  textAnchor="middle"
                  className="text-xs fill-gray-700 font-medium"
                >
                  {formatCurrency(item.type === 'start' || item.type === 'end' ? item.cumulative : item.value)}
                </text>
                
                {/* Category Label */}
                <text
                  x={x + barWidth / 2}
                  y={chartHeight + 20}
                  textAnchor="middle"
                  className="text-xs fill-gray-600"
                >
                  {item.label}
                </text>
                
                {/* Connecting Lines */}
                {index < data.length - 1 && item.type !== 'end' && (
                  <line
                    x1={x + barWidth}
                    y1={chartHeight - (item.cumulative / maxValue * chartHeight * 0.8)}
                    x2={x + barWidth + spacing}
                    y2={chartHeight - (item.cumulative / maxValue * chartHeight * 0.8)}
                    stroke="#d1d5db"
                    strokeWidth="1"
                    strokeDasharray="3,3"
                  />
                )}
              </g>
            );
          })}
        </svg>
      </div>

      {/* Legend */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded"></div>
            <span className="text-body-sm text-gray-600">Inflows</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded"></div>
            <span className="text-body-sm text-gray-600">Outflows</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-900 rounded"></div>
            <span className="text-body-sm text-gray-600">Net Position</span>
          </div>
        </div>
        <div className="text-body-sm text-gray-500">
          Net Change: {formatCurrency(data[data.length - 1].cumulative - data[0].cumulative)}
        </div>
      </div>
    </div>
  );
};

export default CashFlowWaterfall;
