
import React from 'react';
import { LucideIcon } from 'lucide-react';
import { Link } from 'react-router-dom';

interface ModuleCardProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  children?: React.ReactNode;
  className?: string;
  headerActions?: React.ReactNode;
  color?: string;
  link?: string;
}

const ModuleCard: React.FC<ModuleCardProps> = ({
  title,
  description,
  icon: Icon,
  children,
  className = '',
  headerActions,
  color = 'text-gray-600',
  link
}) => {
  const CardContent = (
    <div className={`card-clean hover:shadow-lg transition-shadow cursor-pointer ${className}`}>
      {/* Card Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {Icon && (
              <div className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center">
                <Icon className={`h-5 w-5 ${color}`} />
              </div>
            )}
            <div>
              <h3 className="text-heading-lg">{title}</h3>
              {description && (
                <p className="text-body-sm text-gray-600 mt-1">{description}</p>
              )}
            </div>
          </div>
          {headerActions && (
            <div className="flex items-center space-x-2">
              {headerActions}
            </div>
          )}
        </div>
      </div>

      {/* Card Content */}
      {children && (
        <div className="p-6">
          {children}
        </div>
      )}
    </div>
  );

  if (link) {
    return (
      <Link to={link} className="block">
        {CardContent}
      </Link>
    );
  }

  return CardContent;
};

export default ModuleCard;
