/**
 * MetricWidget Component Tests
 */

import { describe, it, expect, vi } from 'vitest';
import { TrendingUp, DollarSign } from 'lucide-react';
import { renderWithProviders, expectElementToBeVisible, expectElementToHaveText, userEvent } from '@/test/utils';
import MetricWidget from '../MetricWidget';

describe('MetricWidget', () => {
  const defaultProps = {
    title: 'Test Metric',
    value: 1000,
  };

  it('renders basic metric widget', () => {
    const { getByText } = renderWithProviders(<MetricWidget {...defaultProps} />);

    expectElementToHaveText(getByText('Test Metric'), 'Test Metric');
    expectElementToHaveText(getByText('1 000'), '1 000');
  });

  it('formats currency correctly', () => {
    const { getByText } = renderWithProviders(
      <MetricWidget {...defaultProps} format="currency" />
    );

    expectElementToHaveText(getByText(/1 000 kr/), '1 000 kr');
  });

  it('formats percentage correctly', () => {
    const { getByText } = renderWithProviders(
      <MetricWidget {...defaultProps} value={0.15} format="percentage" />
    );

    expectElementToHaveText(getByText('15,0 %'), '15,0 %');
  });

  it('formats days correctly', () => {
    const { getByText } = renderWithProviders(
      <MetricWidget {...defaultProps} value={30} format="days" />
    );

    expectElementToHaveText(getByText(/30 dagar/), '30 dagar');
  });

  it('displays icon when provided', () => {
    const { container } = renderWithProviders(
      <MetricWidget {...defaultProps} icon={DollarSign} />
    );

    const icon = container.querySelector('svg');
    expectElementToBeVisible(icon!);
  });

  it('displays subtitle when provided', () => {
    const { getByText } = renderWithProviders(
      <MetricWidget {...defaultProps} subtitle="Additional info" />
    );

    expectElementToHaveText(getByText('Additional info'), 'Additional info');
  });

  it('displays trend change correctly', () => {
    const change = {
      value: 5.2,
      period: 'vs last month',
      type: 'increase' as const,
    };

    const { getByText, container } = renderWithProviders(
      <MetricWidget {...defaultProps} change={change} />
    );

    // Check that the percentage is displayed somewhere in the component
    expect(container.textContent).toContain('5,2');
    expectElementToHaveText(getByText('vs last month'), 'vs last month');

    // Check for trending up icon
    const trendIcon = container.querySelector('[data-testid="trending-up"]');
    expect(trendIcon || container.querySelector('svg')).toBeInTheDocument();
  });

  it('shows good/bad change colors correctly', () => {
    const goodChange = {
      value: 5.2,
      period: 'vs last month',
      type: 'increase' as const,
      isGoodChange: true,
    };

    const { container: goodContainer } = renderWithProviders(
      <MetricWidget {...defaultProps} change={goodChange} />
    );

    const badChange = {
      value: 5.2,
      period: 'vs last month',
      type: 'increase' as const,
      isGoodChange: false,
    };

    const { container: badContainer } = renderWithProviders(
      <MetricWidget {...defaultProps} change={badChange} />
    );

    // Check that different color classes are applied
    const goodElement = goodContainer.querySelector('.text-success-600');
    const badElement = badContainer.querySelector('.text-error-600');

    expect(goodElement).toBeInTheDocument();
    expect(badElement).toBeInTheDocument();
  });

  it('displays loading state', () => {
    const { container } = renderWithProviders(
      <MetricWidget {...defaultProps} loading={true} />
    );

    const loadingElement = container.querySelector('.animate-pulse');
    expectElementToBeVisible(loadingElement!);
  });

  it('displays error state', () => {
    const { getByText } = renderWithProviders(
      <MetricWidget {...defaultProps} error={true} />
    );

    expectElementToHaveText(getByText('Error loading data'), 'Error loading data');
  });

  it('handles click events when onClick is provided', async () => {
    const handleClick = vi.fn();
    const { getByRole } = renderWithProviders(
      <MetricWidget {...defaultProps} onClick={handleClick} />
    );

    const button = getByRole('button');
    expectElementToBeVisible(button);

    await userEvent.click(button);
    expect(handleClick).toHaveBeenCalledOnce();
  });

  it('handles keyboard navigation when clickable', async () => {
    const handleClick = vi.fn();
    const { getByRole } = renderWithProviders(
      <MetricWidget {...defaultProps} onClick={handleClick} />
    );

    const button = getByRole('button');
    button.focus();

    await userEvent.keyboard('{Enter}');
    expect(handleClick).toHaveBeenCalledOnce();

    await userEvent.keyboard(' ');
    expect(handleClick).toHaveBeenCalledTimes(2);
  });

  it('uses custom formatter when provided', () => {
    const customFormatter = (value: any) => `Custom: ${value}`;

    const { getByText } = renderWithProviders(
      <MetricWidget
        {...defaultProps}
        customFormatter={customFormatter}
        format="currency" // Should be ignored when customFormatter is provided
      />
    );

    expectElementToHaveText(getByText('Custom: 1000'), 'Custom: 1000');
  });

  it('handles null and undefined values gracefully', () => {
    const { getByText } = renderWithProviders(
      <div>
        <MetricWidget title="Null Value" value={null} format="currency" />
        <MetricWidget title="Undefined Value" value={undefined} format="currency" />
      </div>
    );

    const nullElements = getByText('Null Value').closest('.card-clean');
    const undefinedElements = getByText('Undefined Value').closest('.card-clean');

    expect(nullElements?.textContent).toContain('0 kr');
    expect(undefinedElements?.textContent).toContain('0 kr');
  });

  it('applies different sizes correctly', () => {
    const { container: smallContainer } = renderWithProviders(
      <MetricWidget {...defaultProps} size="sm" />
    );

    const { container: largeContainer } = renderWithProviders(
      <MetricWidget {...defaultProps} size="lg" />
    );

    const smallWidget = smallContainer.querySelector('.p-4');
    const largeWidget = largeContainer.querySelector('.p-8');

    expectElementToBeVisible(smallWidget!);
    expectElementToBeVisible(largeWidget!);
  });

  it('applies custom className', () => {
    const { container } = renderWithProviders(
      <MetricWidget {...defaultProps} className="custom-class" />
    );

    const widget = container.querySelector('.custom-class');
    expectElementToBeVisible(widget!);
  });
});
