
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useNavigate } from 'react-router-dom';
import { ChevronLeft, ChevronRight, Save, Send, Shield, FileText, Building, User, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

import OrganizationNumberInput from '@/components/loan/OrganizationNumberInput';
import FileUpload from '@/components/loan/FileUpload';
import BankIDAuth from '@/components/loan/BankIDAuth';
import ConsentManagement from '@/components/loan/ConsentManagement';
import ProgressIndicator from '@/components/loan/ProgressIndicator';

import { organizationNumberSchema, personnummerSchema, CompanyType } from '@/lib/validation';
import { BankIDResponse } from '@/lib/bankid';
import { createFinanceApplication, updateFinanceApplication, submitFinanceApplication } from '@/integrations/supabase/finance';

// Define the form schema with Zod
const formSchema = z.object({
  // Company Information
  organization_number: organizationNumberSchema,
  company_name: z.string().min(1, 'Company name is required'),
  company_type: z.string().min(1, 'Company type is required'),
  has_vat_registration: z.boolean().default(false),
  vat_number: z.string().optional(),

  // Contact Person
  contact_person_name: z.string().min(1, 'Contact person name is required'),
  contact_person_email: z.string().email('Invalid email address'),
  contact_person_phone: z.string().min(1, 'Phone number is required'),
  contact_person_role: z.string().min(1, 'Role is required'),
  contact_person_personnummer: personnummerSchema,

  // Loan Details
  loan_amount: z.coerce.number().min(10000, 'Minimum loan amount is 10,000 SEK'),
  loan_purpose: z.string().min(10, 'Please provide a detailed purpose for the loan'),
  loan_term_months: z.coerce.number().min(3, 'Minimum term is 3 months').max(60, 'Maximum term is 60 months'),

  // Consent
  consent_marketing: z.boolean().default(false),
  consent_data_processing: z.boolean().refine(val => val === true, {
    message: 'You must consent to data processing',
  }),
  consent_credit_check: z.boolean().refine(val => val === true, {
    message: 'You must consent to credit check',
  }),
  consent_terms: z.boolean().refine(val => val === true, {
    message: 'You must accept the terms and conditions',
  }),
})
.refine(
  (data) => {
    // If has_vat_registration is true, vat_number is required
    return !data.has_vat_registration || (data.vat_number && data.vat_number.length > 0);
  },
  {
    message: 'VAT number is required for VAT registered companies',
    path: ['vat_number'],
  }
);

// Define the form data type
type FormData = z.infer<typeof formSchema>;

const LoanApplication: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('company');
  const [applicationId, setApplicationId] = useState<string | null>(null);
  const [financialDocuments, setFinancialDocuments] = useState<string[]>([]);
  const [bankIdVerified, setBankIdVerified] = useState(false);
  const [bankIdData, setBankIdData] = useState<BankIDResponse | null>(null);
  const [showBankId, setShowBankId] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize the form
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      organization_number: '',
      company_name: '',
      company_type: '',
      has_vat_registration: false,
      vat_number: '',
      contact_person_name: '',
      contact_person_email: '',
      contact_person_phone: '',
      contact_person_role: '',
      contact_person_personnummer: '',
      loan_amount: 100000,
      loan_purpose: '',
      loan_term_months: 12,
      consent_marketing: false,
      consent_data_processing: false,
      consent_credit_check: false,
      consent_terms: false,
    },
  });

  // Watch for form values that affect conditional rendering
  const hasVatRegistration = form.watch('has_vat_registration');
  const companyType = form.watch('company_type');

  // Handle company type change from organization number
  const handleCompanyTypeChange = (type: CompanyType) => {
    if (type !== CompanyType.UNKNOWN) {
      form.setValue('company_type', type);
    }
  };

  // Handle file upload completion
  const handleFileUploadComplete = (urls: string[]) => {
    setFinancialDocuments(prev => [...prev, ...urls]);
    toast.success(`${urls.length} file(s) uploaded successfully`);
  };

  // Handle BankID authentication success
  const handleBankIdSuccess = (data: BankIDResponse) => {
    setBankIdVerified(true);
    setBankIdData(data);
    setShowBankId(false);

    if (data.userInfo) {
      // Pre-fill personal information if available
      form.setValue('contact_person_personnummer', data.userInfo.personalNumber);
      form.setValue('contact_person_name', data.userInfo.name);
    }

    toast.success('Identity verified successfully');
  };

  // Handle BankID authentication cancel
  const handleBankIdCancel = () => {
    setShowBankId(false);
  };

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);

      // Create or update the application
      if (!applicationId) {
        // Create new application
        const formData = {
          ...data,
          financial_documents: financialDocuments,
          bankid_verification_id: bankIdData?.orderRef || null,
          bankid_verification_date: bankIdData ? new Date().toISOString() : null,
          status: 'draft',
          // Ensure all required fields are explicitly provided
          organization_number: data.organization_number,
          company_name: data.company_name,
          company_type: data.company_type,
          contact_person_name: data.contact_person_name,
          contact_person_email: data.contact_person_email,
          contact_person_phone: data.contact_person_phone,
          contact_person_role: data.contact_person_role,
          contact_person_personnummer: data.contact_person_personnummer,
          loan_amount: data.loan_amount,
          loan_purpose: data.loan_purpose,
          loan_term_months: data.loan_term_months
        };

        const { data: newApplication, error } = await createFinanceApplication(formData);

        if (error) throw error;

        setApplicationId(newApplication!.id);
        toast.success('Application saved successfully');
      } else {
        // Update existing application
        const { error } = await updateFinanceApplication(applicationId, {
          ...data,
          financial_documents: financialDocuments,
          bankid_verification_id: bankIdData?.orderRef || null,
          bankid_verification_date: bankIdData ? new Date().toISOString() : null,
        });

        if (error) throw error;

        toast.success('Application updated successfully');
      }
    } catch (error) {
      console.error('Error saving application:', error);
      toast.error('Failed to save application');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle final submission
  const handleSubmitApplication = async () => {
    try {
      // Validate all fields first
      await form.trigger();

      if (!form.formState.isValid) {
        toast.error('Please fill in all required fields');
        return;
      }

      if (!bankIdVerified) {
        toast.error('Please verify your identity with BankID');
        return;
      }

      if (financialDocuments.length === 0) {
        toast.error('Please upload at least one financial document');
        return;
      }

      setIsSubmitting(true);

      // Save the form first if not already saved
      if (!applicationId) {
        await onSubmit(form.getValues());
      }

      // Submit the application
      const { data, error } = await submitFinanceApplication(applicationId!);

      if (error) throw error;

      toast.success('Application submitted successfully');
      navigate('/');
    } catch (error) {
      console.error('Error submitting application:', error);
      toast.error('Failed to submit application');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Define the steps for the progress indicator
  const steps = [
    { id: 'company', label: 'Company' },
    { id: 'contact', label: 'Contact' },
    { id: 'documents', label: 'Documents' },
    { id: 'verify', label: 'Verify' },
  ];

  // Determine completed steps
  const completedSteps = React.useMemo(() => {
    const completed: string[] = [];

    // Company step is completed if organization number and loan details are filled
    if (form.getValues('organization_number') &&
        form.getValues('company_name') &&
        form.getValues('loan_amount') &&
        form.getValues('loan_purpose')) {
      completed.push('company');
    }

    // Contact step is completed if contact person details are filled
    if (form.getValues('contact_person_name') &&
        form.getValues('contact_person_email') &&
        form.getValues('contact_person_phone') &&
        form.getValues('contact_person_role')) {
      completed.push('contact');
    }

    // Documents step is completed if at least one document is uploaded
    if (financialDocuments.length > 0) {
      completed.push('documents');
    }

    // Verify step is completed if BankID is verified and consents are given
    if (bankIdVerified &&
        form.getValues('consent_data_processing') &&
        form.getValues('consent_credit_check') &&
        form.getValues('consent_terms')) {
      completed.push('verify');
    }

    return completed;
  }, [form, financialDocuments, bankIdVerified]);

  return (
    <div className="min-h-screen bg-white py-12">
      <div className="container-clean max-w-4xl">
        <div className="mb-12 text-center">
          <h1 className="text-display-md mb-4">Business Loan Application</h1>
          <p className="text-body-lg text-gray-600 max-w-2xl mx-auto">
            Complete the form below to apply for a business loan
          </p>
        </div>

        {/* Progress indicator */}
        <div className="mb-8">
          <ProgressIndicator
            steps={steps}
            currentStep={activeTab}
            completedSteps={completedSteps}
          />
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="hidden">
                <TabsTrigger value="company">Company</TabsTrigger>
                <TabsTrigger value="contact">Contact</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                <TabsTrigger value="verify">Verify</TabsTrigger>
              </TabsList>

              {/* Company Information Tab */}
              <TabsContent value="company">
                <div className="card-clean overflow-hidden">
                  <div className="bg-gray-50 p-6 border-b border-gray-200">
                    <h2 className="text-heading-lg mb-2">Company Information</h2>
                    <p className="text-body-md text-gray-600">
                      Please provide your company details
                    </p>
                  </div>
                  <div className="p-6 space-y-6">

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="organization_number"
                        render={({ field }) => (
                          <OrganizationNumberInput
                            name="organization_number"
                            control={form.control}
                            onCompanyTypeChange={handleCompanyTypeChange}
                          />
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="company_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Company Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter company name" className="form-input" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="has_vat_registration"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border border-gray-200 p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="form-label">VAT Registration</FormLabel>
                              <FormDescription className="text-body-sm text-gray-500">
                                Is your company registered for VAT?
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      {hasVatRegistration && (
                        <FormField
                          control={form.control}
                          name="vat_number"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="form-label">VAT Number</FormLabel>
                              <FormControl>
                                <Input placeholder="Enter VAT number" className="form-input" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      <Separator className="my-4" />

                      <FormField
                        control={form.control}
                        name="loan_amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Loan Amount (SEK)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={10000}
                                step={10000}
                                className="form-input"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription className="text-body-sm text-gray-500">
                              Minimum loan amount is 10,000 SEK
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="loan_term_months"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Loan Term (Months)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min={3}
                                max={60}
                                className="form-input"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription className="text-body-sm text-gray-500">
                              Choose between 3 and 60 months
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="loan_purpose"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Loan Purpose</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder="Describe the purpose of the loan"
                                className="form-textarea min-h-[100px]"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription className="text-body-sm text-gray-500">
                              Please provide a detailed description of how you plan to use the loan
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-between pt-6 mt-4 border-t border-gray-200">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => navigate('/')}
                        className="btn-secondary h-12 px-6"
                      >
                        <ChevronLeft className="mr-2 h-5 w-5" />
                        Cancel
                      </Button>
                      <Button
                        type="button"
                        onClick={() => setActiveTab('contact')}
                        className="btn-primary h-12 px-6"
                      >
                        Next
                        <ChevronRight className="ml-2 h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Contact Person Tab */}
              <TabsContent value="contact">
                <div className="card-clean overflow-hidden">
                  <div className="bg-gray-50 p-6 border-b border-gray-200">
                    <h2 className="text-heading-lg mb-2">Contact Person</h2>
                    <p className="text-body-md text-gray-600">
                      Please provide details of the authorized representative
                    </p>
                  </div>
                  <div className="p-6 space-y-6">

                    <div className="space-y-4">
                      <FormField
                        control={form.control}
                        name="contact_person_name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Full Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter full name" className="form-input" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="contact_person_role"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Role in Company</FormLabel>
                            <FormControl>
                              <Input placeholder="e.g. CEO, CFO, Owner" className="form-input" {...field} />
                            </FormControl>
                            <FormDescription className="text-body-sm text-gray-500">
                              Must be an authorized representative of the company
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="contact_person_email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Email Address</FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="Enter email address"
                                className="form-input"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="contact_person_phone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Phone Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter phone number" className="form-input" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="contact_person_personnummer"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="form-label">Personal Identity Number</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="YYYYMMDD-XXXX"
                                className="form-input"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription className="text-body-sm text-gray-500">
                              Swedish personal identity number (personnummer)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-between pt-6 mt-4 border-t border-gray-200">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setActiveTab('company')}
                        className="btn-secondary h-12 px-6"
                      >
                        <ChevronLeft className="mr-2 h-5 w-5" />
                        Previous
                      </Button>
                      <Button
                        type="button"
                        onClick={() => setActiveTab('documents')}
                        className="btn-primary h-12 px-6"
                      >
                        Next
                        <ChevronRight className="ml-2 h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Documents Tab */}
              <TabsContent value="documents">
                <div className="card-clean overflow-hidden">
                  <div className="bg-gray-50 p-6 border-b border-gray-200">
                    <h2 className="text-heading-lg mb-2">Financial Documents</h2>
                    <p className="text-body-md text-gray-600">
                      Please upload relevant financial documents
                    </p>
                  </div>
                  <div className="p-6 space-y-6">

                    <div className="space-y-4">
                      {applicationId ? (
                        <FileUpload
                          applicationId={applicationId}
                          onUploadComplete={handleFileUploadComplete}
                        />
                      ) : (
                        <div className="text-center p-6 border border-dashed border-gray-300 rounded-lg">
                          <p className="text-body-md text-gray-500">
                            Please save your application first to enable file uploads
                          </p>
                          <Button
                            type="submit"
                            className="btn-primary mt-4"
                            disabled={isSubmitting}
                          >
                            <Save className="mr-2 h-4 w-4" />
                            Save Application
                          </Button>
                        </div>
                      )}

                      <div className="mt-6 p-5 bg-gray-50 rounded-lg border border-gray-200">
                        <h3 className="text-heading-md mb-3">Required Documents</h3>
                        <ul className="space-y-2 text-body-sm text-gray-600">
                          <li className="flex items-start">
                            <div className="mr-2 mt-0.5 text-gray-400">•</div>
                            <span>Latest annual financial statement</span>
                          </li>
                          <li className="flex items-start">
                            <div className="mr-2 mt-0.5 text-gray-400">•</div>
                            <span>Last 6 months of bank statements</span>
                          </li>
                          <li className="flex items-start">
                            <div className="mr-2 mt-0.5 text-gray-400">•</div>
                            <span>Business plan (for loans over 500,000 SEK)</span>
                          </li>
                          <li className="flex items-start">
                            <div className="mr-2 mt-0.5 text-gray-400">•</div>
                            <span>Cash flow forecast</span>
                          </li>
                          <li className="flex items-start">
                            <div className="mr-2 mt-0.5 text-gray-400">•</div>
                            <span>Tax certificate (Skatteverket)</span>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <div className="flex justify-between pt-6 mt-4 border-t border-gray-200">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setActiveTab('contact')}
                        className="btn-secondary h-12 px-6"
                      >
                        <ChevronLeft className="mr-2 h-5 w-5" />
                        Previous
                      </Button>
                      <Button
                        type="button"
                        onClick={() => setActiveTab('verify')}
                        className="btn-primary h-12 px-6"
                      >
                        Next
                        <ChevronRight className="ml-2 h-5 w-5" />
                      </Button>
                    </div>
                  </div>
                </div>
              </TabsContent>

              {/* Verify Tab */}
              <TabsContent value="verify">
                <div className="card-clean overflow-hidden">
                  <div className="bg-gray-50 p-6 border-b border-gray-200">
                    <h2 className="text-heading-lg mb-2">Verify & Submit</h2>
                    <p className="text-body-md text-gray-600">
                      Verify your identity and submit your application
                    </p>
                  </div>
                  <div className="p-6 space-y-6">

                    <div className="space-y-6">
                      {/* BankID Authentication */}
                      <div className="space-y-4">
                        <h3 className="text-heading-md">Identity Verification</h3>

                        {bankIdVerified ? (
                          <div className="flex items-center p-5 rounded-lg bg-success-50 text-success-600 border border-success-200">
                            <div className="w-10 h-10 rounded-full bg-success-100 flex items-center justify-center mr-4">
                              <CheckCircle className="h-6 w-6" />
                            </div>
                            <div>
                              <p className="font-medium text-body-md">Identity verified successfully</p>
                              <p className="text-body-sm text-gray-600 mt-1">Your identity has been confirmed via BankID</p>
                            </div>
                          </div>
                        ) : (
                          <div>
                            {showBankId ? (
                              <BankIDAuth
                                onSuccess={handleBankIdSuccess}
                                onCancel={handleBankIdCancel}
                                personalNumber={form.getValues('contact_person_personnummer')}
                              />
                            ) : (
                              <div className="p-5 rounded-lg border border-gray-200 bg-white">
                                <div className="text-center space-y-4">
                                  <div className="w-16 h-16 rounded-full bg-gray-100 mx-auto flex items-center justify-center">
                                    <Shield className="h-8 w-8 text-gray-600" />
                                  </div>
                                  <div>
                                    <h4 className="text-heading-md">Verify Your Identity</h4>
                                    <p className="text-body-md text-gray-600 mt-1">Use BankID to securely verify your identity</p>
                                  </div>
                                  <Button
                                    type="button"
                                    onClick={() => setShowBankId(true)}
                                    className="btn-primary w-full h-12 mt-2"
                                  >
                                    <Shield className="mr-2 h-5 w-5" />
                                    Verify with BankID
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Consent Management */}
                      <ConsentManagement control={form.control} />

                      {/* Submit Application */}
                      <div className="pt-6 mt-4 border-t border-gray-200 space-y-4">
                        <Button
                          type="submit"
                          variant="outline"
                          className="btn-secondary w-full h-12"
                          disabled={isSubmitting}
                        >
                          <Save className="mr-2 h-5 w-5" />
                          Save Application
                        </Button>

                        <Button
                          type="button"
                          className="btn-primary w-full h-14 text-label-lg"
                          onClick={handleSubmitApplication}
                          disabled={
                            isSubmitting ||
                            !bankIdVerified ||
                            !form.formState.isValid ||
                            financialDocuments.length === 0
                          }
                        >
                          <Send className="mr-2 h-5 w-5" />
                          Submit Application
                        </Button>

                        <div className="flex justify-start pt-2">
                          <Button
                            type="button"
                            variant="ghost"
                            onClick={() => setActiveTab('documents')}
                            className="btn-ghost text-gray-600"
                          >
                            <ChevronLeft className="mr-2 h-4 w-4" />
                            Back to Documents
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default LoanApplication;
