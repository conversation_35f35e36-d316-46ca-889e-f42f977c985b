/**
 * Application Constants
 * Centralized constants for the Swedish fintech platform
 */

// Swedish locale settings
export const SWEDISH_LOCALE = 'sv-SE';
export const SWEDISH_CURRENCY = 'SEK';

// Date formats
export const DATE_FORMATS = {
  SHORT: 'yyyy-MM-dd',
  LONG: 'dd MMMM yyyy',
  DATETIME: 'yyyy-MM-dd HH:mm',
  TIME: 'HH:mm',
} as const;

// Number formats
export const NUMBER_FORMATS = {
  CURRENCY: {
    style: 'currency' as const,
    currency: SWEDISH_CURRENCY,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  },
  PERCENTAGE: {
    style: 'percent' as const,
    minimumFractionDigits: 1,
    maximumFractionDigits: 1,
  },
  DECIMAL: {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  },
} as const;

// File upload constraints
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['application/pdf', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  ALLOWED_EXTENSIONS: ['.pdf', '.xls', '.xlsx'],
} as const;

// API endpoints and timeouts
export const API = {
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const;

// Dashboard refresh intervals
export const REFRESH_INTERVALS = {
  REAL_TIME: 5000, // 5 seconds
  FREQUENT: 30000, // 30 seconds
  NORMAL: 60000, // 1 minute
  SLOW: 300000, // 5 minutes
} as const;

// Chart colors following the design system
export const CHART_COLORS = {
  PRIMARY: '#000000',
  SECONDARY: '#6b7280',
  SUCCESS: '#10b981',
  WARNING: '#f59e0b',
  ERROR: '#ef4444',
  INFO: '#3b82f6',
  NEUTRAL: '#9ca3af',
} as const;

// Metric widget sizes
export const METRIC_SIZES = {
  SM: 'sm',
  MD: 'md',
  LG: 'lg',
} as const;

// Company types for Swedish organizations
export const COMPANY_TYPES = {
  AKTIEBOLAG: 'Aktiebolag',
  HANDELSBOLAG: 'Handelsbolag',
  ENSKILD_FIRMA: 'Enskild firma',
  EKONOMISK_FORENING: 'Ekonomisk förening',
  IDEELL_FORENING: 'Ideell förening',
  STIFTELSE: 'Stiftelse',
  UNKNOWN: 'Unknown',
} as const;

// BankID status constants
export const BANKID_STATUS = {
  PENDING: 'PENDING',
  COMPLETE: 'COMPLETE',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  TIMEOUT: 'TIMEOUT',
} as const;

// GDPR request types
export const GDPR_REQUEST_TYPES = {
  ACCESS: 'access',
  RECTIFICATION: 'rectification',
  ERASURE: 'erasure',
  PORTABILITY: 'portability',
} as const;

// Application status constants
export const APPLICATION_STATUS = {
  DRAFT: 'draft',
  SUBMITTED: 'submitted',
  UNDER_REVIEW: 'under_review',
  APPROVED: 'approved',
  REJECTED: 'rejected',
  WITHDRAWN: 'withdrawn',
} as const;

// Risk levels
export const RISK_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

// Compliance status
export const COMPLIANCE_STATUS = {
  COMPLIANT: 'compliant',
  NON_COMPLIANT: 'non_compliant',
  PENDING_REVIEW: 'pending_review',
  REQUIRES_ACTION: 'requires_action',
} as const;

// Default pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
} as const;

// Cache keys for React Query
export const CACHE_KEYS = {
  COMPANIES: 'companies',
  VOUCHERS: 'vouchers',
  CHART_OF_ACCOUNTS: 'chart_of_accounts',
  EXCHANGE_RATES: 'exchange_rates',
  FINANCE_APPLICATIONS: 'finance_applications',
  METRICS: 'metrics',
  DASHBOARD_DATA: 'dashboard_data',
} as const;

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'An unexpected error occurred. Please try again later.',
  TIMEOUT: 'Request timed out. Please try again.',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  SAVED: 'Changes saved successfully.',
  SUBMITTED: 'Application submitted successfully.',
  UPDATED: 'Updated successfully.',
  DELETED: 'Deleted successfully.',
  UPLOADED: 'File uploaded successfully.',
} as const;

// Loading states
export const LOADING_STATES = {
  IDLE: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
} as const;
