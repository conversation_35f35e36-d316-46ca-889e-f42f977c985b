
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  ChevronLeft,
  ChevronRight,
  Check,
  Sparkles,
  User,
  Building2,
  Shield,
  Zap,
  CheckCircle,
  Eye,
  EyeOff,
  CreditCard
} from 'lucide-react';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import SimpleOrganizationNumberInput from '@/components/ui/SimpleOrganizationNumberInput';
import OTPVerification from '@/components/ui/OTPVerification';
import CompanyDataFetcher from '@/components/ui/CompanyDataFetcher';
import ConsentCompliance from '@/components/ui/ConsentCompliance';
import { emailSchema, mobileSchema, passwordSchema, CompanyType } from '@/lib/validation';

const Register: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Step 2: Account Creation
    fullName: '',
    email: '',
    mobile: '',
    password: '',
    confirmPassword: '',
    emailVerified: false,
    mobileVerified: false,

    // Step 3: Company Identification
    organizationNumber: '',
    companyData: null as any,
    companyConfirmed: false,

    // Step 4: Consent & Compliance
    consents: {} as Record<string, boolean>,
    kycResult: null as any,

    // Step 5: Integrations
    integrations: {} as Record<string, boolean>,
  });

  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const steps = [
    { id: 1, title: 'Welcome', icon: Sparkles, description: 'Platform introduction' },
    { id: 2, title: 'Account Creation', icon: User, description: 'Personal information & verification' },
    { id: 3, title: 'Company Identification', icon: Building2, description: 'Organization details' },
    { id: 4, title: 'Consent & Compliance', icon: Shield, description: 'Terms & verification' },
    { id: 5, title: 'Integrations', icon: Zap, description: 'Connect your systems' }
  ];

  const progress = (currentStep / 5) * 100;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateStep = (step: number): boolean => {
    // Allow navigation without validation for testing/demo purposes
    // In production, you can enable strict validation by setting this to true
    const strictValidation = false;

    if (!strictValidation) {
      setValidationErrors({});
      return true;
    }

    const errors: Record<string, string> = {};

    switch (step) {
      case 2: // Account Creation
        try {
          emailSchema.parse(formData.email);
        } catch {
          errors.email = 'Please enter a valid email address';
        }

        try {
          mobileSchema.parse(formData.mobile);
        } catch {
          errors.mobile = 'Please enter a valid Swedish mobile number';
        }

        try {
          passwordSchema.parse(formData.password);
        } catch {
          errors.password = 'Password must meet security requirements';
        }

        if (formData.password !== formData.confirmPassword) {
          errors.confirmPassword = 'Passwords do not match';
        }

        if (!formData.fullName.trim()) {
          errors.fullName = 'Full name is required';
        }

        if (!formData.emailVerified) {
          errors.emailVerified = 'Please verify your email address';
        }

        if (!formData.mobileVerified) {
          errors.mobileVerified = 'Please verify your mobile number';
        }
        break;

      case 3: // Company Identification
        if (!formData.organizationNumber) {
          errors.organizationNumber = 'Organization number is required';
        }

        if (!formData.companyConfirmed) {
          errors.companyConfirmed = 'Please confirm your company data';
        }
        break;

      case 4: // Consent & Compliance
        if (!formData.kycResult || formData.kycResult.status !== 'passed') {
          errors.kycResult = 'KYC verification must be completed';
        }
        break;
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const nextStep = () => {
    if (currentStep < 5 && validateStep(currentStep)) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleFortnoxConnect = () => {
    try {
      // Generate Fortnox OAuth URL with all required scopes
      const scopes = [
        'salary',
        'bookkeeping',
        'archive',
        'connectfile',
        'article',
        'companyinformation',
        'settings',
        'invoice',
        'costcenter',
        'currency',
        'customer',
        'inbox',
        'payment',
        'noxfinansinvoice',
        'offer',
        'order',
        'price',
        'print',
        'project',
        'profile',
        'supplierinvoice',
        'supplier',
        'timereporting'
      ].join(' ');

      const params = new URLSearchParams({
        client_id: '1Y9FA35cHyB3',
        redirect_uri: 'https://arcim-fintech-nexus.lovableproject.com/auth/fortnox/callback',
        response_type: 'code',
        scope: scopes,
        state: `onboarding-${Date.now()}`
      });

      const authUrl = `https://api.fortnox.se/oauth-v1/auth?${params.toString()}`;

      // Open Fortnox OAuth in new window
      window.open(authUrl, 'fortnox-auth', 'width=600,height=700,scrollbars=yes,resizable=yes');

      console.log('Fortnox OAuth initiated:', authUrl);
    } catch (error) {
      console.error('Fortnox connection failed:', error);
    }
  };

  const handleOpenBankingConnect = () => {
    try {
      console.log('Open Banking integration clicked - placeholder implementation');
      // Placeholder for Open Banking integration
      // In production, this would integrate with Tink or Nordigen APIs
    } catch (error) {
      console.error('Open Banking connection failed:', error);
    }
  };

  const handleSubmit = async () => {
    try {
      // Here you would typically send the data to your backend
      console.log('Registration completed:', formData);

      // Redirect to dashboard or success page
      window.location.href = '/dashboard';
    } catch (error) {
      console.error('Registration failed:', error);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1: // Welcome & Introduction
        return (
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900">
                Welcome to Arcim
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                The all-in-one treasury and lending platform designed specifically for Swedish SMEs.
                Get access to advanced financial tools, real-time analytics, and streamlined lending solutions.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="p-6 bg-blue-50 rounded-lg">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Building2 className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Treasury Management</h3>
                <p className="text-sm text-gray-600">
                  Advanced cash flow forecasting, multi-currency support, and real-time liquidity monitoring.
                </p>
              </div>

              <div className="p-6 bg-green-50 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Adaptive Lending</h3>
                <p className="text-sm text-gray-600">
                  AI-powered credit decisions, flexible terms, and streamlined application processes.
                </p>
              </div>

              <div className="p-6 bg-purple-50 rounded-lg">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Zap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">Financial Analytics</h3>
                <p className="text-sm text-gray-600">
                  Comprehensive KPI tracking, regulatory reporting, and business intelligence.
                </p>
              </div>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg max-w-2xl mx-auto">
              <h4 className="font-semibold text-gray-900 mb-2">What to expect:</h4>
              <ul className="text-sm text-gray-600 space-y-1 text-left">
                <li>• Quick 5-minute setup process</li>
                <li>• Secure verification through Swedish authorities</li>
                <li>• Optional integrations with your existing systems</li>
                <li>• Immediate access to financial insights</li>
              </ul>
            </div>
          </div>
        );

      case 2: // Account Creation
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Create Your Account</h3>
              <p className="text-gray-600">
                We'll need to verify your email and mobile number for security.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="fullName">Full Name *</Label>
                  <Input
                    id="fullName"
                    value={formData.fullName}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    placeholder="Enter your full name"
                    className="mt-1"
                  />
                  {validationErrors.fullName && (
                    <p className="text-red-600 text-sm mt-1">{validationErrors.fullName}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className="mt-1"
                  />
                  {validationErrors.email && (
                    <p className="text-red-600 text-sm mt-1">{validationErrors.email}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="mobile">Mobile Number *</Label>
                  <Input
                    id="mobile"
                    value={formData.mobile}
                    onChange={(e) => handleInputChange('mobile', e.target.value)}
                    placeholder="+46 70 123 45 67"
                    className="mt-1"
                  />
                  {validationErrors.mobile && (
                    <p className="text-red-600 text-sm mt-1">{validationErrors.mobile}</p>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label htmlFor="password">Password *</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? 'text' : 'password'}
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      placeholder="Create a secure password"
                      className="mt-1 pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-1 h-10 px-3"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                  {validationErrors.password && (
                    <p className="text-red-600 text-sm mt-1">{validationErrors.password}</p>
                  )}
                  <div className="text-xs text-gray-500 mt-1">
                    Must contain: 8+ characters, uppercase, lowercase, number, special character
                  </div>
                </div>

                <div>
                  <Label htmlFor="confirmPassword">Confirm Password *</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={formData.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    placeholder="Confirm your password"
                    className="mt-1"
                  />
                  {validationErrors.confirmPassword && (
                    <p className="text-red-600 text-sm mt-1">{validationErrors.confirmPassword}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Email Verification */}
            {formData.email && emailSchema.safeParse(formData.email).success && (
              <div className="border-t pt-6">
                <h4 className="font-medium text-gray-900 mb-4">Email Verification</h4>
                {formData.emailVerified ? (
                  <div className="flex items-center p-3 bg-green-50 text-green-700 rounded-lg">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Email verified successfully
                  </div>
                ) : (
                  <OTPVerification
                    type="email"
                    target={formData.email}
                    onVerified={() => handleInputChange('emailVerified', 'true')}
                  />
                )}
              </div>
            )}

            {/* Mobile Verification */}
            {formData.mobile && mobileSchema.safeParse(formData.mobile).success && (
              <div className="border-t pt-6">
                <h4 className="font-medium text-gray-900 mb-4">Mobile Verification</h4>
                {formData.mobileVerified ? (
                  <div className="flex items-center p-3 bg-green-50 text-green-700 rounded-lg">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Mobile verified successfully
                  </div>
                ) : (
                  <OTPVerification
                    type="mobile"
                    target={formData.mobile}
                    onVerified={() => handleInputChange('mobileVerified', 'true')}
                  />
                )}
              </div>
            )}
          </div>
        );

      case 3: // Company Identification
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Company Identification</h3>
              <p className="text-gray-600">
                We'll fetch your company details from Bolagsverket using your organization number.
              </p>
            </div>

            <div>
              <Label htmlFor="organizationNumber">Swedish Organization Number *</Label>
              <SimpleOrganizationNumberInput
                value={formData.organizationNumber}
                onChange={(value) => handleInputChange('organizationNumber', value)}
                placeholder="XXXXXX-XXXX"
                className="mt-1"
              />
              {validationErrors.organizationNumber && (
                <p className="text-red-600 text-sm mt-1">{validationErrors.organizationNumber}</p>
              )}
            </div>

            {formData.organizationNumber && (
              <CompanyDataFetcher
                organizationNumber={formData.organizationNumber}
                onDataFetched={(data) => {
                  setFormData(prev => ({
                    ...prev,
                    companyData: data,
                    companyConfirmed: true
                  }));
                }}
              />
            )}

            {validationErrors.companyConfirmed && (
              <p className="text-red-600 text-sm">{validationErrors.companyConfirmed}</p>
            )}
          </div>
        );

      case 4: // Consent & Compliance
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Consent & Compliance</h3>
              <p className="text-gray-600">
                Please review and accept our terms, and complete the required verification checks.
              </p>
            </div>

            <ConsentCompliance
              onConsentComplete={(consents, kycResult) => {
                setFormData(prev => ({
                  ...prev,
                  consents,
                  kycResult
                }));
              }}
            />

            {validationErrors.kycResult && (
              <p className="text-red-600 text-sm">{validationErrors.kycResult}</p>
            )}
          </div>
        );

      case 5: // ERP & Bank Integration
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Connect Your Systems</h3>
              <p className="text-gray-600">
                Integrate with your existing tools to unlock powerful insights. These are optional and can be set up later.
              </p>
            </div>

            {/* Simple fallback integration setup */}
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <Badge variant="outline" className="text-blue-600 border-blue-200">
                  Optional - You can set these up later
                </Badge>
              </div>

              <div className="grid gap-6">
                {/* Fortnox Integration */}
                <Card className="relative overflow-hidden">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Building2 className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900">Fortnox ERP</h4>
                          <p className="text-sm text-gray-600 font-normal">
                            Connect your Fortnox accounting system for automated financial data sync.
                          </p>
                        </div>
                      </div>
                      <Badge className="bg-gray-100 text-gray-700 border-gray-200">
                        Available
                      </Badge>
                    </CardTitle>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2 flex items-center">
                        <Zap className="h-4 w-4 mr-1 text-yellow-500" />
                        Key Benefits
                      </h5>
                      <ul className="space-y-1">
                        <li className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                          Automated financial health monitoring
                        </li>
                        <li className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                          Real-time KPI tracking and analytics
                        </li>
                        <li className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                          Simplified regulatory reporting
                        </li>
                      </ul>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={handleFortnoxConnect}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <Building2 className="h-4 w-4 mr-2" />
                          Connect Fortnox
                        </Button>

                        <Button
                          variant="ghost"
                          className="text-gray-600"
                        >
                          Skip for now
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Open Banking Integration */}
                <Card className="relative overflow-hidden">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <CreditCard className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900">Open Banking</h4>
                          <p className="text-sm text-gray-600 font-normal">
                            Connect your Swedish bank accounts for comprehensive financial analysis.
                          </p>
                        </div>
                      </div>
                      <Badge className="bg-gray-100 text-gray-700 border-gray-200">
                        Available
                      </Badge>
                    </CardTitle>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2 flex items-center">
                        <Zap className="h-4 w-4 mr-1 text-yellow-500" />
                        Key Benefits
                      </h5>
                      <ul className="space-y-1">
                        <li className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                          Real-time cash flow analytics
                        </li>
                        <li className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                          Automated transaction categorization
                        </li>
                        <li className="flex items-center text-sm text-gray-600">
                          <CheckCircle className="h-3 w-3 text-green-500 mr-2 flex-shrink-0" />
                          Enhanced lending decisions
                        </li>
                      </ul>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={handleOpenBankingConnect}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          Connect Bank
                        </Button>

                        <Button
                          variant="ghost"
                          className="text-gray-600"
                        >
                          Skip for now
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="text-center space-y-4 pt-6 border-t">
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">
                    No worries! You can set up these integrations anytime from your dashboard.
                  </p>

                  <Button
                    onClick={() => {
                      setFormData(prev => ({
                        ...prev,
                        integrations: {}
                      }));
                    }}
                    className="w-full max-w-md"
                  >
                    Continue to Platform
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <section className="pt-36 pb-20 px-6">
        <div className="container-clean mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Join the Arcim Platform
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Set up your account in just 5 minutes and unlock advanced treasury management,
                adaptive lending, and comprehensive financial analytics for your Swedish SME.
              </p>
            </div>

            {/* Progress Indicator */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                <span className="text-sm font-medium text-gray-700">
                  Step {currentStep} of {steps.length}
                </span>
                <span className="text-sm text-gray-500">
                  {Math.round(progress)}% Complete
                </span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>

            {/* Step Navigation */}
            <div className="mb-8">
              <div className="flex flex-wrap justify-center gap-2 md:gap-4">
                {steps.map((step, index) => {
                  const isActive = currentStep === step.id;
                  const isCompleted = currentStep > step.id;
                  const Icon = step.icon;

                  return (
                    <div
                      key={step.id}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
                        isActive
                          ? 'bg-blue-100 text-blue-700'
                          : isCompleted
                          ? 'bg-green-100 text-green-700'
                          : 'bg-gray-100 text-gray-500'
                      }`}
                    >
                      <div className={`flex items-center justify-center w-6 h-6 rounded-full ${
                        isActive
                          ? 'bg-blue-600 text-white'
                          : isCompleted
                          ? 'bg-green-600 text-white'
                          : 'bg-gray-400 text-white'
                      }`}>
                        {isCompleted ? (
                          <Check className="w-4 h-4" />
                        ) : (
                          <Icon className="w-4 h-4" />
                        )}
                      </div>
                      <span className="text-sm font-medium hidden md:block">
                        {step.title}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Form Content */}
            <Card className="mb-8">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  {React.createElement(steps[currentStep - 1].icon, { className: "w-5 h-5" })}
                  <span>{steps[currentStep - 1].title}</span>
                </CardTitle>
                <p className="text-gray-600">{steps[currentStep - 1].description}</p>
              </CardHeader>
              <CardContent>
                {renderStepContent()}
              </CardContent>
            </Card>

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
                className="flex items-center space-x-2"
              >
                <ChevronLeft className="w-4 h-4" />
                <span>Previous</span>
              </Button>

              {currentStep === 1 ? (
                <Button
                  onClick={nextStep}
                  className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                >
                  <span>Get Started</span>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              ) : currentStep === 5 ? (
                <Button
                  onClick={handleSubmit}
                  className="flex items-center space-x-2 bg-green-600 hover:bg-green-700"
                >
                  <Check className="w-4 h-4" />
                  <span>Complete Setup</span>
                </Button>
              ) : (
                <Button
                  onClick={nextStep}
                  className="flex items-center space-x-2"
                >
                  <span>Continue</span>
                  <ChevronRight className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Register;
