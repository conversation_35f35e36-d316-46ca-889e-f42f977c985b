import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { X, FileText, Upload, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { uploadFinancialDocument } from '@/integrations/supabase/finance';

interface FileUploadProps {
  applicationId: string;
  onUploadComplete: (urls: string[]) => void;
  maxFiles?: number;
  maxSize?: number; // in bytes
  accept?: Record<string, string[]>;
  className?: string;
}

interface FileWithPreview extends File {
  preview?: string;
  progress?: number;
  error?: string;
  uploaded?: boolean;
  url?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  applicationId,
  onUploadComplete,
  maxFiles = 5,
  maxSize = 10 * 1024 * 1024, // 10MB
  accept = {
    'application/pdf': ['.pdf'],
    'application/vnd.ms-excel': ['.xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  },
  className,
}) => {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      // Filter out files that would exceed the maxFiles limit
      const newFiles = acceptedFiles.slice(0, maxFiles - files.length);

      // Create preview for each file
      const filesWithPreview = newFiles.map((file) =>
        Object.assign(file, {
          preview: file.type.startsWith('image/')
            ? URL.createObjectURL(file)
            : undefined,
          progress: 0,
          error: undefined,
          uploaded: false,
        })
      );

      setFiles((prev) => [...prev, ...filesWithPreview]);
    },
    [files.length, maxFiles]
  );

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    maxFiles: maxFiles - files.length,
    maxSize,
    accept,
    disabled: files.length >= maxFiles || isUploading,
  });

  const removeFile = (index: number) => {
    setFiles((prev) => {
      const newFiles = [...prev];
      // Revoke the preview URL to avoid memory leaks
      if (newFiles[index].preview) {
        URL.revokeObjectURL(newFiles[index].preview!);
      }
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const uploadFiles = async () => {
    if (files.length === 0 || isUploading) return;

    setIsUploading(true);
    const uploadedUrls: string[] = [];

    // Create a copy of files to update progress
    const updatedFiles = [...files];

    for (let i = 0; i < updatedFiles.length; i++) {
      const file = updatedFiles[i];

      // Skip already uploaded files
      if (file.uploaded) {
        uploadedUrls.push(file.url!);
        continue;
      }

      try {
        // Update progress to 50% to show upload started
        updatedFiles[i] = { ...file, progress: 50 };
        setFiles([...updatedFiles]);

        // Upload the file
        const { data, error } = await uploadFinancialDocument(applicationId, file);

        if (error) throw error;

        // Update file with upload status
        updatedFiles[i] = {
          ...file,
          progress: 100,
          uploaded: true,
          url: data!,
        };
        uploadedUrls.push(data!);
      } catch (error) {
        console.error('Error uploading file:', error);
        updatedFiles[i] = {
          ...file,
          progress: 0,
          error: 'Upload failed',
        };
      }

      setFiles([...updatedFiles]);
    }

    setIsUploading(false);
    onUploadComplete(uploadedUrls);
  };

  // Clean up previews when component unmounts
  React.useEffect(() => {
    return () => {
      files.forEach((file) => {
        if (file.preview) {
          URL.revokeObjectURL(file.preview);
        }
      });
    };
  }, [files]);

  return (
    <div className={cn('space-y-4', className)}>
      <div
        {...getRootProps()}
        className={cn(
          'border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors',
          isDragActive
            ? 'border-primary bg-primary-light'
            : 'border-gray-300 hover:border-primary/50 hover:bg-gray-50',
          isDragReject && 'border-error bg-error/5',
          files.length >= maxFiles && 'opacity-50 cursor-not-allowed',
          className
        )}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center space-y-3">
          <div className={cn(
            "w-16 h-16 rounded-full flex items-center justify-center",
            isDragActive ? "bg-primary-light" : "bg-gray-100",
          )}>
            <Upload
              className={cn(
                'h-8 w-8',
                isDragActive ? 'text-primary' : 'text-gray-700'
              )}
            />
          </div>
          <p className="text-body font-medium text-gray-900">
            {isDragActive
              ? 'Drop the files here...'
              : files.length >= maxFiles
              ? `Maximum ${maxFiles} files reached`
              : 'Drag & drop files here, or click to select files'}
          </p>
          <p className="text-body-small text-gray-700">
            PDF and Excel files only (max {maxFiles} files, {maxSize / (1024 * 1024)}MB each)
          </p>
        </div>
      </div>

      {files.length > 0 && (
        <div className="space-y-4">
          <div className="space-y-2">
            {files.map((file, index) => (
              <div
                key={`${file.name}-${index}`}
                className="flex items-center justify-between p-4 border border-gray-300 rounded-lg bg-white shadow-sm hover:shadow-card transition-shadow"
              >
                <div className="flex items-center space-x-4 overflow-hidden">
                  <div className="w-10 h-10 rounded-full bg-primary-light flex items-center justify-center flex-shrink-0">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="text-body font-medium truncate text-gray-900">{file.name}</p>
                    <p className="text-body-small text-gray-700">
                      {(file.size / 1024).toFixed(1)} KB
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  {file.error ? (
                    <div className="flex items-center space-x-1 text-error">
                      <AlertCircle className="h-5 w-5" />
                      <span className="text-body-small">Error</span>
                    </div>
                  ) : file.uploaded ? (
                    <div className="flex items-center space-x-1 text-success">
                      <CheckCircle className="h-5 w-5" />
                      <span className="text-body-small">Uploaded</span>
                    </div>
                  ) : file.progress && file.progress > 0 ? (
                    <div className="w-20">
                      <Progress value={file.progress} className="h-2" />
                    </div>
                  ) : null}

                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeFile(index)}
                    disabled={isUploading}
                    className="text-gray-700 hover:text-error hover:bg-error/10"
                  >
                    <X className="h-4 w-4" />
                    <span className="sr-only">Remove file</span>
                  </Button>
                </div>
              </div>
            ))}
          </div>

          <Button
            onClick={uploadFiles}
            disabled={files.length === 0 || isUploading || files.every((f) => f.uploaded)}
            className="w-full h-12"
            size="lg"
          >
            {isUploading ? (
              <div className="flex items-center">
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                <span>Uploading...</span>
              </div>
            ) : (
              <div className="flex items-center">
                <Upload className="mr-2 h-4 w-4" />
                <span>Upload Files</span>
              </div>
            )}
          </Button>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
