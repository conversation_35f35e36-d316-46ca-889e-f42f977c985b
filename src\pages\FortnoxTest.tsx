import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Copy, ExternalLink, CheckCircle, AlertTriangle, Play } from 'lucide-react';
import { fortnoxClient, fortnoxConfig } from '@/integrations/fortnox/client';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const FortnoxTest: React.FC = () => {
  const [testResults, setTestResults] = useState<{
    configTest: 'pending' | 'success' | 'error';
    urlTest: 'pending' | 'success' | 'error';
    authUrl: string;
    error?: string;
  }>({
    configTest: 'pending',
    urlTest: 'pending',
    authUrl: ''
  });

  const [copied, setCopied] = useState(false);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const runTests = () => {
    console.log('🧪 Running Fortnox integration tests...');

    try {
      // Test 1: Configuration validation
      console.log('📋 Testing configuration...');
      const config = fortnoxConfig;

      if (!config.clientId || !config.clientSecret || !config.redirectUri) {
        setTestResults(prev => ({
          ...prev,
          configTest: 'error',
          error: 'Missing required configuration values'
        }));
        return;
      }

      setTestResults(prev => ({ ...prev, configTest: 'success' }));
      console.log('✅ Configuration test passed');

      // Test 2: URL generation
      console.log('🔗 Testing OAuth URL generation...');
      const state = 'test-state-123';
      const authUrl = fortnoxClient.getAuthorizationUrl(state);

      console.log('📋 Configuration details:');
      console.log('  - Client ID:', config.clientId);
      console.log('  - Redirect URI:', config.redirectUri);
      console.log('  - Base URL:', config.baseUrl);
      console.log('🌐 Generated URL:', authUrl);

      if (!authUrl || !authUrl.includes('api.fortnox.se')) {
        setTestResults(prev => ({
          ...prev,
          urlTest: 'error',
          error: 'Invalid OAuth URL generated'
        }));
        return;
      }

      setTestResults(prev => ({
        ...prev,
        urlTest: 'success',
        authUrl
      }));
      console.log('✅ OAuth URL test passed:', authUrl);

    } catch (error) {
      console.error('❌ Test failed:', error);
      setTestResults(prev => ({
        ...prev,
        configTest: 'error',
        urlTest: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  };

  const testRedirect = () => {
    if (testResults.authUrl) {
      console.log('🚀 Testing redirect to:', testResults.authUrl);
      window.location.href = testResults.authUrl;
    }
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <div className="h-4 w-4 rounded-full bg-gray-300" />;
    }
  };

  const getStatusColor = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <section className="pt-36 pb-20 px-6">
        <div className="container-clean mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                Fortnox Integration Test
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Test the Fortnox OAuth integration to diagnose any connection issues.
              </p>
            </div>

            <div className="grid gap-6">
              {/* Configuration Display */}
              <Card>
                <CardHeader>
                  <CardTitle>Current Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Client ID:
                      </label>
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded border">
                        <code className="flex-1 text-sm">{fortnoxConfig.clientId}</code>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Base URL:
                      </label>
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded border">
                        <code className="flex-1 text-sm">{fortnoxConfig.baseUrl}</code>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Redirect URI:
                    </label>
                    <div className="flex items-center gap-2 p-2 bg-gray-50 rounded border">
                      <code className="flex-1 text-sm break-all">{fortnoxConfig.redirectUri}</code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(fortnoxConfig.redirectUri)}
                        className="h-8 px-2"
                      >
                        {copied ? <CheckCircle className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Test Results */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    Integration Tests
                    <Button onClick={runTests} className="flex items-center gap-2">
                      <Play className="h-4 w-4" />
                      Run Tests
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(testResults.configTest)}
                        <span className="font-medium">Configuration Test</span>
                      </div>
                      <Badge className={getStatusColor(testResults.configTest)}>
                        {testResults.configTest}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(testResults.urlTest)}
                        <span className="font-medium">OAuth URL Generation</span>
                      </div>
                      <Badge className={getStatusColor(testResults.urlTest)}>
                        {testResults.urlTest}
                      </Badge>
                    </div>
                  </div>

                  {testResults.error && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded">
                      <p className="text-sm text-red-700">
                        <strong>Error:</strong> {testResults.error}
                      </p>
                    </div>
                  )}

                  {testResults.authUrl && (
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Generated OAuth URL:
                        </label>
                        <div className="p-3 bg-green-50 border border-green-200 rounded">
                          <code className="text-sm break-all text-green-800">
                            {testResults.authUrl}
                          </code>
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <Button
                          onClick={testRedirect}
                          className="flex items-center gap-2"
                        >
                          <ExternalLink className="h-4 w-4" />
                          Test Redirect to Fortnox
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => copyToClipboard(testResults.authUrl)}
                          className="flex items-center gap-2"
                        >
                          <Copy className="h-4 w-4" />
                          Copy URL
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Instructions */}
              <Card>
                <CardHeader>
                  <CardTitle>Troubleshooting Steps</CardTitle>
                </CardHeader>
                <CardContent>
                  <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                    <li>Run the tests above to verify configuration</li>
                    <li>Check browser console for JavaScript errors</li>
                    <li>Verify the redirect URI is configured in Fortnox Developer Portal</li>
                    <li>Test the redirect manually using the "Test Redirect" button</li>
                    <li>Check network tab for failed requests</li>
                  </ol>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default FortnoxTest;
