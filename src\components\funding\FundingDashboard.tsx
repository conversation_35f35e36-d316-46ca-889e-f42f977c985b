import React from 'react';
import { TrendingUp, Users, DollarSign, Target, MoreHorizontal } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import ModuleCard from '@/components/dashboard/ModuleCard';
import MetricWidget from '@/components/dashboard/MetricWidget';
import ChartContainer from '@/components/dashboard/ChartContainer';
import ARRGrowthChart from '@/components/funding/ARRGrowthChart';
import CohortHeatmap from '@/components/funding/CohortHeatmap';

const FundingDashboard: React.FC = () => {
  // Mock data - in real implementation, this would come from API
  const fundingMetrics = {
    arr: 12500000,
    arrGrowth: 45.2,
    nrr: 118.5,
    cohortRetention: 89.3,
    burnRate: 850000,
    runway: 18
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-display-sm">Funding Readiness</h1>
          <p className="text-body-lg text-gray-600 mt-2">
            Track key metrics that investors care about most
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" className="btn-secondary">
            Generate Report
          </Button>
          <Button className="btn-primary">
            Request Funding
          </Button>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricWidget
          title="Annual Recurring Revenue"
          value={fundingMetrics.arr}
          format="currency"
          change={{
            value: fundingMetrics.arrGrowth,
            period: 'vs last year',
            type: 'increase'
          }}
          icon={DollarSign}
        />
        
        <MetricWidget
          title="Net Revenue Retention"
          value={fundingMetrics.nrr}
          format="percentage"
          change={{
            value: 8.3,
            period: 'vs last quarter',
            type: 'increase'
          }}
          icon={TrendingUp}
        />
        
        <MetricWidget
          title="Cohort Retention"
          value={fundingMetrics.cohortRetention}
          format="percentage"
          change={{
            value: 2.1,
            period: 'vs last quarter',
            type: 'increase'
          }}
          icon={Users}
        />
        
        <MetricWidget
          title="Runway"
          value={fundingMetrics.runway}
          format="number"
          change={{
            value: -2,
            period: 'vs last month',
            type: 'decrease'
          }}
          icon={Target}
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* ARR Growth Chart */}
        <ChartContainer
          title="ARR Growth Trend"
          description="Monthly recurring revenue growth over time"
          icon={TrendingUp}
          headerActions={
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          }
        >
          <ARRGrowthChart />
        </ChartContainer>

        {/* Cohort Retention Heatmap */}
        <ChartContainer
          title="Cohort Retention Analysis"
          description="Customer retention by signup cohort"
          icon={Users}
          headerActions={
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          }
        >
          <CohortHeatmap />
        </ChartContainer>
      </div>

      {/* Funding Readiness Score */}
      <ModuleCard
        title="Funding Readiness Score"
        description="Based on key investor metrics and market benchmarks"
        icon={Target}
        headerActions={
          <Button variant="outline" size="sm" className="btn-secondary">
            View Details
          </Button>
        }
      >
        <div className="space-y-6">
          {/* Score Display */}
          <div className="text-center">
            <div className="text-6xl font-bold text-gray-900 mb-2">8.4</div>
            <div className="text-body-lg text-gray-600">out of 10</div>
            <div className="text-body-sm text-success-600 font-medium mt-2">
              Strong funding position
            </div>
          </div>

          {/* Score Breakdown */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-heading-md font-semibold text-gray-900">9.2</div>
              <div className="text-body-sm text-gray-600">Growth Metrics</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-heading-md font-semibold text-gray-900">8.1</div>
              <div className="text-body-sm text-gray-600">Unit Economics</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-heading-md font-semibold text-gray-900">7.9</div>
              <div className="text-body-sm text-gray-600">Market Position</div>
            </div>
          </div>

          {/* Recommendations */}
          <div className="space-y-3">
            <h4 className="text-heading-sm">Recommendations to improve score:</h4>
            <ul className="space-y-2">
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Increase customer acquisition efficiency by 15% to reach top quartile
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Improve gross margin to 75%+ through pricing optimization
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-body-sm text-gray-600">
                  Extend runway to 24+ months before next funding round
                </span>
              </li>
            </ul>
          </div>
        </div>
      </ModuleCard>
    </div>
  );
};

export default FundingDashboard;
