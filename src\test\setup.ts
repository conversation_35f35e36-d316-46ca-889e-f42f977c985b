/**
 * Test Setup
 * Global test configuration and mocks
 */

import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock environment variables
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          order: vi.fn(() => Promise.resolve({ data: [], error: null })),
        })),
        order: vi.fn(() => Promise.resolve({ data: [], error: null })),
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: {}, error: null })),
        })),
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => Promise.resolve({ data: {}, error: null })),
          })),
        })),
      })),
    })),
  },
}));

// Mock React Query
vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual('@tanstack/react-query');
  return {
    ...actual,
    useQuery: vi.fn(() => ({
      data: undefined,
      error: null,
      isLoading: false,
      isError: false,
      refetch: vi.fn(),
    })),
    useMutation: vi.fn(() => ({
      mutate: vi.fn(),
      mutateAsync: vi.fn(),
      isLoading: false,
      isError: false,
      error: null,
    })),
    useQueryClient: vi.fn(() => ({
      invalidateQueries: vi.fn(),
      setQueryData: vi.fn(),
      getQueryData: vi.fn(),
    })),
  };
});

// Mock BankID
vi.mock('@/lib/bankid', () => ({
  initiateBankIDAuth: vi.fn(() => Promise.resolve({
    status: 'PENDING',
    orderRef: 'test-order-ref',
    autoStartToken: 'test-token',
  })),
  pollBankIDStatus: vi.fn(() => Promise.resolve({
    status: 'COMPLETE',
    orderRef: 'test-order-ref',
    userInfo: {
      personalNumber: '************',
      name: 'Test Testsson',
      givenName: 'Test',
      surname: 'Testsson',
    },
  })),
  cancelBankIDAuth: vi.fn(() => Promise.resolve({
    status: 'CANCELLED',
    orderRef: 'test-order-ref',
  })),
  BankIDStatus: {
    PENDING: 'PENDING',
    COMPLETE: 'COMPLETE',
    FAILED: 'FAILED',
    CANCELLED: 'CANCELLED',
    TIMEOUT: 'TIMEOUT',
  },
}));

// Mock organization number validation
vi.mock('organisationsnummer', () => ({
  default: {
    valid: vi.fn(() => true),
  },
}));

// Mock date-fns
vi.mock('date-fns', async () => {
  const actual = await vi.importActual('date-fns');
  return {
    ...actual,
    format: vi.fn(() => '2024-01-01'),
    parseISO: vi.fn(() => new Date('2024-01-01')),
    isValid: vi.fn(() => true),
  };
});

// Mock recharts
vi.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: any }) => children,
  LineChart: ({ children }: { children: any }) => ({ type: 'div', props: { 'data-testid': 'line-chart', children } }),
  Line: () => ({ type: 'div', props: { 'data-testid': 'line' } }),
  XAxis: () => ({ type: 'div', props: { 'data-testid': 'x-axis' } }),
  YAxis: () => ({ type: 'div', props: { 'data-testid': 'y-axis' } }),
  CartesianGrid: () => ({ type: 'div', props: { 'data-testid': 'cartesian-grid' } }),
  Tooltip: () => ({ type: 'div', props: { 'data-testid': 'tooltip' } }),
  Legend: () => ({ type: 'div', props: { 'data-testid': 'legend' } }),
  BarChart: ({ children }: { children: any }) => ({ type: 'div', props: { 'data-testid': 'bar-chart', children } }),
  Bar: () => ({ type: 'div', props: { 'data-testid': 'bar' } }),
  PieChart: ({ children }: { children: any }) => ({ type: 'div', props: { 'data-testid': 'pie-chart', children } }),
  Pie: () => ({ type: 'div', props: { 'data-testid': 'pie' } }),
  Cell: () => ({ type: 'div', props: { 'data-testid': 'cell' } }),
}));

// Console warnings that we want to suppress in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is deprecated') ||
       args[0].includes('Warning: React.createFactory() is deprecated'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
