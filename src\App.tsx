
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import LoanApplication from "./pages/LoanApplication";
import Dashboard from "./pages/Dashboard";
import Financing from "./pages/Financing";
import Platform from "./pages/Platform";
import Contact from "./pages/Contact";
import Testimonials from "./pages/Testimonials";
import Register from "./pages/Register";
import FortnoxCallback from "./pages/FortnoxCallback";
import FortnoxSetup from "./pages/FortnoxSetup";
import FortnoxTest from "./pages/FortnoxTest";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/register" element={<Register />} />
          <Route path="/loan-application" element={<LoanApplication />} />
          <Route path="/dashboard/*" element={<Dashboard />} />
          <Route path="/financing" element={<Financing />} />
          <Route path="/platform" element={<Platform />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/testimonials" element={<Testimonials />} />
          <Route path="/auth/fortnox/callback" element={<FortnoxCallback />} />
          <Route path="/fortnox-setup" element={<FortnoxSetup />} />
          <Route path="/fortnox-test" element={<FortnoxTest />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
