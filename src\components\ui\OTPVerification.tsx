import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp';
import { Label } from '@/components/ui/label';
import { CheckCircle, Clock, RefreshCw, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OTPVerificationProps {
  type: 'email' | 'mobile';
  target: string; // email address or mobile number
  onVerified: () => void;
  onResend?: () => void;
  className?: string;
}

const OTPVerification: React.FC<OTPVerificationProps> = ({
  type,
  target,
  onVerified,
  onResend,
  className,
}) => {
  const [otp, setOtp] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState(300); // 5 minutes
  const [canResend, setCanResend] = useState(false);

  // Countdown timer
  useEffect(() => {
    if (timeLeft > 0 && !isVerified) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0) {
      setCanResend(true);
    }
  }, [timeLeft, isVerified]);

  // Auto-verify when OTP is complete
  useEffect(() => {
    if (otp.length === 6) {
      handleVerify();
    }
  }, [otp]);

  const handleVerify = async () => {
    if (otp.length !== 6) return;

    setIsVerifying(true);
    setError(null);

    try {
      // Simulate API call for OTP verification
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock verification - in real implementation, call your OTP verification API
      const isValid = otp === '123456'; // Mock: accept 123456 as valid OTP
      
      if (isValid) {
        setIsVerified(true);
        onVerified();
      } else {
        setError('Invalid verification code. Please try again.');
        setOtp('');
      }
    } catch (error) {
      setError('Verification failed. Please try again.');
      setOtp('');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResend = async () => {
    if (!canResend) return;

    try {
      // Simulate API call for resending OTP
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset timer and state
      setTimeLeft(300);
      setCanResend(false);
      setError(null);
      setOtp('');
      
      if (onResend) {
        onResend();
      }
    } catch (error) {
      setError('Failed to resend code. Please try again.');
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const maskTarget = (target: string) => {
    if (type === 'email') {
      const [local, domain] = target.split('@');
      return `${local.slice(0, 2)}***@${domain}`;
    } else {
      return `***${target.slice(-4)}`;
    }
  };

  if (isVerified) {
    return (
      <div className={cn("flex items-center p-4 rounded-lg bg-green-50 text-green-700 border border-green-200", className)}>
        <CheckCircle className="h-5 w-5 mr-3" />
        <div>
          <p className="font-medium">Verification successful!</p>
          <p className="text-sm text-green-600">
            Your {type} has been verified.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div>
        <Label className="text-sm font-medium text-gray-700">
          Enter verification code sent to {maskTarget(target)}
        </Label>
        <p className="text-xs text-gray-500 mt-1">
          We've sent a 6-digit code to your {type}. Enter it below to verify.
        </p>
      </div>

      <div className="space-y-4">
        <InputOTP
          maxLength={6}
          value={otp}
          onChange={setOtp}
          disabled={isVerifying}
        >
          <InputOTPGroup>
            <InputOTPSlot index={0} />
            <InputOTPSlot index={1} />
            <InputOTPSlot index={2} />
            <InputOTPSlot index={3} />
            <InputOTPSlot index={4} />
            <InputOTPSlot index={5} />
          </InputOTPGroup>
        </InputOTP>

        {error && (
          <div className="flex items-center text-red-600 text-sm">
            <AlertCircle className="h-4 w-4 mr-2" />
            {error}
          </div>
        )}

        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center text-gray-500">
            <Clock className="h-4 w-4 mr-1" />
            {timeLeft > 0 ? `Code expires in ${formatTime(timeLeft)}` : 'Code expired'}
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleResend}
            disabled={!canResend || isVerifying}
            className="text-blue-600 hover:text-blue-700"
          >
            <RefreshCw className={cn("h-4 w-4 mr-1", isVerifying && "animate-spin")} />
            Resend code
          </Button>
        </div>
      </div>

      {isVerifying && (
        <div className="flex items-center justify-center p-4 text-gray-600">
          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          Verifying code...
        </div>
      )}
    </div>
  );
};

export default OTPVerification;
