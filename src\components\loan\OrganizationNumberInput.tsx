import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FormItem, FormLabel, FormControl, FormMessage, FormDescription } from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { validateOrganizationNumber, detectCompanyType, CompanyType } from '@/lib/validation';
import { Control, FieldPath, FieldValues, useController } from 'react-hook-form';

interface OrganizationNumberInputProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
> {
  name: TName;
  control: Control<TFieldValues>;
  label?: string;
  placeholder?: string;
  description?: string;
  className?: string;
  onCompanyTypeChange?: (type: CompanyType) => void;
}

const OrganizationNumberInput = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>
>({
  name,
  control,
  label = 'Organization Number',
  placeholder = '123456-7890',
  description = 'Enter your Swedish organization number',
  className,
  onCompanyTypeChange,
}: OrganizationNumberInputProps<TFieldValues, TName>) => {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [companyType, setCompanyType] = useState<CompanyType>(CompanyType.UNKNOWN);
  const [formattedValue, setFormattedValue] = useState<string>('');

  // Format and validate the organization number on change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Allow only digits and hyphen
    const sanitized = value.replace(/[^\d-]/g, '');

    // Update the field value with the sanitized input
    field.onChange(sanitized);

    // Validate the organization number
    const formatted = validateOrganizationNumber(sanitized);
    setIsValid(formatted !== null);

    if (formatted) {
      setFormattedValue(formatted);
      const type = detectCompanyType(formatted);
      setCompanyType(type);
      if (onCompanyTypeChange) {
        onCompanyTypeChange(type);
      }
    } else {
      setFormattedValue(sanitized);
      setCompanyType(CompanyType.UNKNOWN);
      if (onCompanyTypeChange) {
        onCompanyTypeChange(CompanyType.UNKNOWN);
      }
    }
  };

  // Format the initial value if it's valid
  useEffect(() => {
    if (field.value) {
      const formatted = validateOrganizationNumber(field.value);
      setIsValid(formatted !== null);

      if (formatted) {
        setFormattedValue(formatted);
        const type = detectCompanyType(formatted);
        setCompanyType(type);
        if (onCompanyTypeChange) {
          onCompanyTypeChange(type);
        }
      } else {
        setFormattedValue(field.value);
      }
    }
  }, [field.value, onCompanyTypeChange]);

  return (
    <FormItem className={className}>
      <FormLabel>{label}</FormLabel>
      <div className="relative">
        <FormControl>
          <Input
            placeholder={placeholder}
            value={formattedValue}
            onChange={handleChange}
            onBlur={field.onBlur}
            className={cn(
              "transition-all duration-200",
              isValid === true && 'pr-10 border-success shadow-[0_0_0_1px_#10B981]',
              isValid === false && 'pr-10 border-error shadow-[0_0_0_1px_#EF4444]'
            )}
          />
        </FormControl>
        {isValid !== null && (
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            {isValid ? (
              <CheckCircle className="h-5 w-5 text-success" />
            ) : (
              <AlertCircle className="h-5 w-5 text-error" />
            )}
          </div>
        )}
      </div>

      {error ? (
        <FormMessage>{error.message}</FormMessage>
      ) : (
        <FormDescription>{description}</FormDescription>
      )}

      {isValid && companyType !== CompanyType.UNKNOWN && (
        <div className="mt-2">
          <Badge
            variant="outline"
            className={cn(
              'text-sm px-3 py-1 font-medium',
              companyType === CompanyType.AKTIEBOLAG && 'bg-blue-100 text-blue-700 border-blue-200',
              companyType === CompanyType.HANDELSBOLAG && 'bg-purple-100 text-purple-700 border-purple-200',
              companyType === CompanyType.ENSKILD_FIRMA && 'bg-green-100 text-green-700 border-green-200',
              companyType === CompanyType.EKONOMISK_FORENING && 'bg-orange-100 text-orange-700 border-orange-200',
              companyType === CompanyType.IDEELL_FORENING && 'bg-pink-100 text-pink-700 border-pink-200',
              companyType === CompanyType.STIFTELSE && 'bg-indigo-100 text-indigo-700 border-indigo-200'
            )}
          >
            {companyType}
          </Badge>
        </div>
      )}
    </FormItem>
  );
};

export default OrganizationNumberInput;
