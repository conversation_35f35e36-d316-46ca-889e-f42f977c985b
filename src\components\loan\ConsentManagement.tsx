import React from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { FormItem, FormControl, FormDescription, FormField, FormMessage } from '@/components/ui/form';
import { Card, CardContent } from '@/components/ui/card';
import { Control, FieldPath, FieldValues } from 'react-hook-form';
import { cn } from '@/lib/utils';

interface ConsentOption {
  id: string;
  label: string;
  description: string;
  required: boolean;
}

interface ConsentManagementProps<
  TFieldValues extends FieldValues = FieldValues
> {
  control: Control<TFieldValues>;
  className?: string;
}

const ConsentManagement = <TFieldValues extends FieldValues = FieldValues>({
  control,
  className,
}: ConsentManagementProps<TFieldValues>) => {
  // Define consent options
  const consentOptions: ConsentOption[] = [
    {
      id: 'consent_data_processing',
      label: 'Data Processing Consent',
      description:
        'I consent to the processing of my personal data as described in the privacy policy. This consent is necessary for us to process your loan application.',
      required: true,
    },
    {
      id: 'consent_credit_check',
      label: 'Credit Check Consent',
      description:
        'I consent to a credit check being performed to assess my creditworthiness. This is required to process your loan application.',
      required: true,
    },
    {
      id: 'consent_terms',
      label: 'Terms and Conditions',
      description:
        'I agree to the terms and conditions of the loan agreement. You must accept these terms to proceed with your application.',
      required: true,
    },
    {
      id: 'consent_marketing',
      label: 'Marketing Communications',
      description:
        'I would like to receive marketing communications about products and services that may be of interest to me. This consent is optional and can be withdrawn at any time.',
      required: false,
    },
  ];

  return (
    <Card className={cn('border-gray-300 overflow-hidden', className)}>
      <div className="bg-primary-gradient text-white p-6">
        <h3 className="text-heading-3 font-semibold">Consent Management</h3>
        <p className="text-body-small mt-2 opacity-90">
          Please review and provide your consent to the following items. Items marked with * are required.
        </p>
      </div>
      <CardContent className="p-6 space-y-6">

        <div className="space-y-4">
          {consentOptions.map((option) => (
            <FormField
              key={option.id}
              control={control}
              name={option.id as FieldPath<TFieldValues>}
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-lg border border-gray-300 p-5 hover:bg-gray-50 transition-colors">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="mt-1 h-5 w-5 border-gray-300 text-primary rounded"
                    />
                  </FormControl>
                  <div className="space-y-2 leading-none">
                    <Label
                      htmlFor={option.id}
                      className="text-body font-medium text-gray-900 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {option.label}
                      {option.required && <span className="text-error ml-1">*</span>}
                    </Label>
                    <FormDescription className="text-body-small text-gray-700">
                      {option.description}
                    </FormDescription>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          ))}
        </div>

        <div className="mt-8 p-5 bg-gray-50 rounded-lg border border-gray-300">
          <h4 className="text-heading-4 font-semibold text-primary-dark mb-3">Your Privacy Rights</h4>
          <div className="space-y-3 text-body-small text-gray-700">
            <p>
              <span className="font-semibold text-gray-900">Data Retention:</span> Your personal data will be stored for the duration of the loan application process and, if approved, for the duration of the loan plus an additional 7 years as required by financial regulations.
            </p>
            <p>
              <span className="font-semibold text-gray-900">Your Rights:</span> You have the right to access, rectify, and erase your personal data, as well as the right to restrict or object to processing. You also have the right to data portability.
            </p>
            <p>
              <span className="font-semibold text-gray-900">Data Protection Officer:</span> For any questions regarding your personal data, please contact our Data Protection Officer at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ConsentManagement;
