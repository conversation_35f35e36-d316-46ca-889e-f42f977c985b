import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <Header />

      <div className="flex-grow flex items-center justify-center py-section px-6">
        <div className="container-refined mx-auto">
          <div className="max-w-lg mx-auto text-center space-y-6">
            <h1 className="text-heading-1 text-gray-900">404</h1>
            <h2 className="text-heading-3 text-gray-800">Page not found</h2>
            <p className="text-body text-gray-600 mt-4">
              The page you are looking for doesn't exist or has been moved.
            </p>
            <div className="pt-6">
              <a href="/">
                <Button className="inline-flex items-center gap-2">
                  <ArrowLeft size={16} />
                  Return to Home
                </Button>
              </a>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default NotFound;
