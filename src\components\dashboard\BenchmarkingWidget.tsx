import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Target, 
  TrendingUp, 
  Award, 
  Users,
  BarChart3,
  Star
} from 'lucide-react';

interface BenchmarkingWidgetProps {
  currentScore: number;
  industryAverage: number;
  topPerformers: number;
}

const BenchmarkingWidget: React.FC<BenchmarkingWidgetProps> = ({
  currentScore,
  industryAverage,
  topPerformers
}) => {
  const getPercentile = (score: number) => {
    if (score >= 90) return 95;
    if (score >= 80) return 85;
    if (score >= 70) return 70;
    if (score >= 60) return 55;
    return 30;
  };

  const getGrade = (score: number) => {
    if (score >= 90) return 'A+';
    if (score >= 85) return 'A';
    if (score >= 80) return 'A-';
    if (score >= 75) return 'B+';
    if (score >= 70) return 'B';
    if (score >= 65) return 'B-';
    if (score >= 60) return 'C+';
    return 'C';
  };

  const percentile = getPercentile(currentScore);
  const grade = getGrade(currentScore);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Target className="h-5 w-5 mr-2" />
          Industry Benchmarking
        </CardTitle>
        <p className="text-sm text-gray-600">
          See how you compare to other Swedish SMEs in the fintech sector
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Performance Grade */}
        <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg">
          <div className="flex items-center justify-center space-x-4">
            <div>
              <div className="text-3xl font-bold text-blue-600">{grade}</div>
              <div className="text-sm text-gray-600">Performance Grade</div>
            </div>
            <div className="h-12 w-px bg-gray-300"></div>
            <div>
              <div className="text-3xl font-bold text-green-600">{percentile}th</div>
              <div className="text-sm text-gray-600">Percentile</div>
            </div>
          </div>
        </div>

        {/* Benchmark Comparison */}
        <div className="space-y-4">
          <h4 className="font-semibold text-gray-900">Performance Comparison</h4>
          
          {/* Your Score */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Star className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">Your Score</span>
              </div>
              <Badge variant="default">{currentScore.toFixed(1)}</Badge>
            </div>
            <Progress value={currentScore} className="h-2" />
          </div>

          {/* Industry Average */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium">Industry Average</span>
              </div>
              <Badge variant="secondary">{industryAverage.toFixed(1)}</Badge>
            </div>
            <Progress value={industryAverage} className="h-2" />
          </div>

          {/* Top Performers */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Award className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium">Top 10% Performers</span>
              </div>
              <Badge variant="outline">{topPerformers.toFixed(1)}</Badge>
            </div>
            <Progress value={topPerformers} className="h-2" />
          </div>
        </div>

        {/* Performance Insights */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-900">Performance Insights</h4>
          
          <div className="space-y-2">
            {currentScore > industryAverage && (
              <div className="flex items-start space-x-2 text-green-700">
                <TrendingUp className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  You're performing {(currentScore - industryAverage).toFixed(1)} points above industry average
                </span>
              </div>
            )}
            
            {currentScore < topPerformers && (
              <div className="flex items-start space-x-2 text-blue-700">
                <Target className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <span className="text-sm">
                  {(topPerformers - currentScore).toFixed(1)} points away from top 10% performance
                </span>
              </div>
            )}
            
            <div className="flex items-start space-x-2 text-gray-700">
              <BarChart3 className="h-4 w-4 mt-0.5 flex-shrink-0" />
              <span className="text-sm">
                Based on analysis of 500+ Swedish SMEs in similar sectors
              </span>
            </div>
          </div>
        </div>

        {/* Sector Breakdown */}
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-900">Sector Performance</h4>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">78.2</div>
              <div className="text-xs text-gray-600">Fintech Average</div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">74.8</div>
              <div className="text-xs text-gray-600">Tech SME Average</div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">69.5</div>
              <div className="text-xs text-gray-600">All SME Average</div>
            </div>
            
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">82.1</div>
              <div className="text-xs text-gray-600">Growth Companies</div>
            </div>
          </div>
        </div>

        {/* Improvement Potential */}
        <div className="p-3 bg-yellow-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <Target className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">Improvement Opportunity</p>
              <p>
                Reaching top 10% performance could reduce your interest rate by an additional 
                {((topPerformers - currentScore) * 0.02).toFixed(2)}% annually.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BenchmarkingWidget;
