
import React, { useState } from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Shield,
  Menu,
  X,
  Building,
  Calendar,
  Download,
  Settings,
  CreditCard,
  FileText,
  Gauge
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import FundingDashboard from '@/components/funding/FundingDashboard';
import TreasuryDashboard from '@/components/treasury/TreasuryDashboard';
import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard';
import ComplianceDashboard from '@/components/compliance/ComplianceDashboard';
import PaymentsDashboard from '@/components/payments/PaymentsDashboard';
import FinancingApplicationDashboard from '@/components/financing/FinancingApplicationDashboard';
import CustomerDashboard from '@/components/dashboard/CustomerDashboard';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Extract current module from path
  const currentPath = location.pathname.split('/dashboard/')[1] || 'customer';

  const modules = [
    {
      id: 'customer',
      name: 'Rate Dashboard',
      icon: Gauge,
      path: '/dashboard/customer',
      description: 'Interest rate transparency and KPI optimization'
    },
    {
      id: 'treasury',
      name: 'Treasury Management',
      icon: DollarSign,
      path: '/dashboard/treasury',
      description: 'Cash flow, FX risk, liquidity monitoring'
    },
    {
      id: 'payments',
      name: 'Payments Operations',
      icon: CreditCard,
      path: '/dashboard/payments',
      description: 'Payment processing, reconciliation, audit trails'
    },
    {
      id: 'financing',
      name: 'Apply for Financing',
      icon: FileText,
      path: '/dashboard/financing',
      description: 'Loan applications, credit assessment, financing options'
    },
    {
      id: 'analytics',
      name: 'Growth Analytics',
      icon: BarChart3,
      path: '/dashboard/analytics',
      description: 'DSO/DPO trends, benchmarking, KPIs'
    },
    {
      id: 'funding',
      name: 'Funding Readiness',
      icon: TrendingUp,
      path: '/dashboard/funding',
      description: 'ARR growth, retention metrics, funding insights'
    },
    {
      id: 'compliance',
      name: 'Compliance & Reporting',
      icon: Shield,
      path: '/dashboard/compliance',
      description: 'Audit trails, regulatory reports, GDPR'
    }
  ];

  const handleModuleChange = (path: string) => {
    navigate(path);
    setSidebarOpen(false);
  };

  const currentModule = modules.find(m => m.id === currentPath) || modules[0];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                type="button"
                className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-6 w-6" />
              </button>
              <div className="flex items-center space-x-4">
                <h1 className="text-2xl font-bold text-gray-900">Arcim</h1>
                <div className="hidden lg:block">
                  <span className="text-gray-400">|</span>
                  <span className="ml-4 text-lg font-medium text-gray-700">
                    {currentModule.name}
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="sm">
                <Calendar className="h-4 w-4 mr-2" />
                Last 30 days
              </Button>
              <Button variant="ghost" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <div className={`${sidebarOpen ? 'block' : 'hidden'} lg:block lg:w-64 bg-white border-r border-gray-200 min-h-screen`}>
          <div className="p-4">
            <div className="flex items-center justify-between lg:hidden mb-4">
              <h2 className="text-lg font-semibold">Navigation</h2>
              <button
                onClick={() => setSidebarOpen(false)}
                className="p-2 rounded-md text-gray-400 hover:text-gray-500"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <nav className="space-y-2">
              {modules.map((module) => {
                const Icon = module.icon;
                const isActive = currentPath === module.id;

                return (
                  <button
                    key={module.id}
                    onClick={() => handleModuleChange(module.path)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      isActive
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className={`h-5 w-5 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                      <div>
                        <div className="font-medium">{module.name}</div>
                        <div className="text-xs text-gray-500">{module.description}</div>
                      </div>
                    </div>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Main Content */}
        <main className="flex-1 min-w-0">
          <DashboardLayout>
            <Routes>
              <Route path="/" element={<CustomerDashboard />} />
              <Route path="/customer" element={<CustomerDashboard />} />
              <Route path="/treasury" element={<TreasuryDashboard />} />
              <Route path="/payments" element={<PaymentsDashboard />} />
              <Route path="/financing" element={<FinancingApplicationDashboard />} />
              <Route path="/analytics" element={<AnalyticsDashboard />} />
              <Route path="/funding" element={<FundingDashboard />} />
              <Route path="/compliance" element={<ComplianceDashboard />} />
            </Routes>
          </DashboardLayout>
        </main>
      </div>
    </div>
  );
};

export default Dashboard;
