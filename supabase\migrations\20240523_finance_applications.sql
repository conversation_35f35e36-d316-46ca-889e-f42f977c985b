-- Enable the pgcrypto extension for encryption functions
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create the finance_applications table
CREATE TABLE IF NOT EXISTS public.finance_applications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  deletion_date TIMESTAMPTZ,
  
  -- Company Information
  organization_number TEXT NOT NULL,
  company_name TEXT NOT NULL,
  company_type TEXT NOT NULL,
  has_vat_registration BOOLEAN NOT NULL DEFAULT FALSE,
  vat_number TEXT,
  
  -- Contact Person Information (PII data encrypted)
  contact_person_name TEXT NOT NULL,
  contact_person_email TEXT NOT NULL,
  contact_person_phone TEXT NOT NULL,
  contact_person_role TEXT NOT NULL,
  contact_person_personnummer TEXT NOT NULL,
  
  -- Loan Details
  loan_amount NUMERIC NOT NULL,
  loan_purpose TEXT NOT NULL,
  loan_term_months INTEGER NOT NULL,
  
  -- Documents
  financial_documents TEXT[] DEFAULT '{}',
  
  -- BankID Verification
  bankid_verification_id TEXT,
  bankid_verification_date TIMESTAMPTZ,
  
  -- Consent
  consent_marketing BOOLEAN NOT NULL DEFAULT FALSE,
  consent_data_processing BOOLEAN NOT NULL DEFAULT FALSE,
  consent_credit_check BOOLEAN NOT NULL DEFAULT FALSE,
  consent_terms BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- Application Status
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected')),
  rejection_reason TEXT,
  
  -- User Reference
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS finance_applications_organization_number_idx ON public.finance_applications(organization_number);
CREATE INDEX IF NOT EXISTS finance_applications_status_idx ON public.finance_applications(status);
CREATE INDEX IF NOT EXISTS finance_applications_user_id_idx ON public.finance_applications(user_id);
CREATE INDEX IF NOT EXISTS finance_applications_created_at_idx ON public.finance_applications(created_at);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the updated_at timestamp
CREATE TRIGGER update_finance_applications_updated_at
BEFORE UPDATE ON public.finance_applications
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to encrypt PII data
CREATE OR REPLACE FUNCTION encrypt_pii()
RETURNS TRIGGER AS $$
BEGIN
  -- Encrypt PII data using pgcrypto
  NEW.contact_person_name = pgp_sym_encrypt(NEW.contact_person_name, current_setting('app.encryption_key'));
  NEW.contact_person_email = pgp_sym_encrypt(NEW.contact_person_email, current_setting('app.encryption_key'));
  NEW.contact_person_phone = pgp_sym_encrypt(NEW.contact_person_phone, current_setting('app.encryption_key'));
  NEW.contact_person_personnummer = pgp_sym_encrypt(NEW.contact_person_personnummer, current_setting('app.encryption_key'));
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a function to decrypt PII data
CREATE OR REPLACE FUNCTION decrypt_pii()
RETURNS TRIGGER AS $$
BEGIN
  -- Decrypt PII data using pgcrypto
  NEW.contact_person_name = pgp_sym_decrypt(NEW.contact_person_name::bytea, current_setting('app.encryption_key'));
  NEW.contact_person_email = pgp_sym_decrypt(NEW.contact_person_email::bytea, current_setting('app.encryption_key'));
  NEW.contact_person_phone = pgp_sym_decrypt(NEW.contact_person_phone::bytea, current_setting('app.encryption_key'));
  NEW.contact_person_personnummer = pgp_sym_decrypt(NEW.contact_person_personnummer::bytea, current_setting('app.encryption_key'));
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to encrypt PII data on insert and update
CREATE TRIGGER encrypt_finance_applications_pii
BEFORE INSERT OR UPDATE ON public.finance_applications
FOR EACH ROW
EXECUTE FUNCTION encrypt_pii();

-- Create a trigger to decrypt PII data on select
CREATE TRIGGER decrypt_finance_applications_pii
AFTER SELECT ON public.finance_applications
FOR EACH ROW
EXECUTE FUNCTION decrypt_pii();

-- Create a storage bucket for financial documents
INSERT INTO storage.buckets (id, name, public)
VALUES ('finance-applications', 'Finance Applications', FALSE)
ON CONFLICT (id) DO NOTHING;

-- Set up Row Level Security (RLS) policies
ALTER TABLE public.finance_applications ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their own applications
CREATE POLICY "Users can view their own applications"
ON public.finance_applications
FOR SELECT
USING (auth.uid() = user_id);

-- Policy for users to insert their own applications
CREATE POLICY "Users can insert their own applications"
ON public.finance_applications
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- Policy for users to update their own applications
CREATE POLICY "Users can update their own applications"
ON public.finance_applications
FOR UPDATE
USING (auth.uid() = user_id);

-- Set up RLS for storage
CREATE POLICY "Users can upload their own documents"
ON storage.objects
FOR INSERT
WITH CHECK (
  bucket_id = 'finance-applications' AND
  (storage.foldername(name))[1] = auth.uid()::text
);

CREATE POLICY "Users can view their own documents"
ON storage.objects
FOR SELECT
USING (
  bucket_id = 'finance-applications' AND
  (storage.foldername(name))[1] = auth.uid()::text
);

-- Create a function for GDPR data deletion
CREATE OR REPLACE FUNCTION delete_user_data()
RETURNS TRIGGER AS $$
BEGIN
  -- Set deletion_date instead of actually deleting the data
  UPDATE public.finance_applications
  SET deletion_date = now()
  WHERE user_id = OLD.id;
  
  RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to mark data for deletion when a user is deleted
CREATE TRIGGER on_auth_user_deleted
AFTER DELETE ON auth.users
FOR EACH ROW
EXECUTE FUNCTION delete_user_data();

-- Create a scheduled function to permanently delete data after retention period
CREATE OR REPLACE FUNCTION permanently_delete_expired_data()
RETURNS void AS $$
BEGIN
  -- Delete data that has been marked for deletion for more than 7 years
  DELETE FROM public.finance_applications
  WHERE deletion_date IS NOT NULL
  AND deletion_date < now() - INTERVAL '7 years';
END;
$$ LANGUAGE plpgsql;
