/**
 * Comprehensive Data Insert for Nordström GreenTech AB
 * Generates and inserts complete daily verifikationer dataset (300-600 entries/month)
 * Bypasses UI and directly populates Supabase with realistic BAS 2025 compliant data
 */

import { supabase } from '@/integrations/supabase/client';

// Nordström GreenTech AB company profile - minimal required fields only
const nordstromProfile = {
  organization_number: '556123-4567',
  company_name: 'Nordström GreenTech AB'
};

// Exchange rates for multi-currency operations
const exchangeRates = [
  { base_currency: 'SEK', target_currency: 'EUR', rate: 0.087, rate_date: '2024-01-15' },
  { base_currency: 'SEK', target_currency: 'USD', rate: 0.095, rate_date: '2024-01-15' },
  { base_currency: 'SEK', target_currency: 'NOK', rate: 1.05, rate_date: '2024-01-15' },
  { base_currency: 'SEK', target_currency: 'DKK', rate: 0.65, rate_date: '2024-01-15' },
  { base_currency: 'EUR', target_currency: 'SEK', rate: 11.5, rate_date: '2024-01-15' },
  { base_currency: 'USD', target_currency: 'SEK', rate: 10.5, rate_date: '2024-01-15' },
  { base_currency: 'NOK', target_currency: 'SEK', rate: 0.95, rate_date: '2024-01-15' },
  { base_currency: 'DKK', target_currency: 'SEK', rate: 1.54, rate_date: '2024-01-15' }
];

// BAS 2025 compliant chart of accounts
const chartOfAccounts = [
  // Assets (1000-1999)
  { account_code: '1910', account_name: 'Kassa', account_type: 'asset' },
  { account_code: '1930', account_name: 'Handelsbanken Företagskonto', account_type: 'asset' },
  { account_code: '1931', account_name: 'SEB Valutakonto EUR', account_type: 'asset' },
  { account_code: '1932', account_name: 'SEB Valutakonto USD', account_type: 'asset' },
  { account_code: '1933', account_name: 'Nordea Valutakonto NOK', account_type: 'asset' },
  { account_code: '1510', account_name: 'Kundfordringar', account_type: 'asset' },
  { account_code: '1513', account_name: 'Kundfordringar EUR', account_type: 'asset' },
  { account_code: '1514', account_name: 'Kundfordringar USD', account_type: 'asset' },
  { account_code: '1515', account_name: 'Kundfordringar NOK', account_type: 'asset' },
  { account_code: '1410', account_name: 'Råmaterial och förnödenheter', account_type: 'asset' },
  { account_code: '1420', account_name: 'Produkter i arbete', account_type: 'asset' },
  { account_code: '1430', account_name: 'Färdiga varor', account_type: 'asset' },
  { account_code: '1220', account_name: 'Maskiner och andra tekniska anläggningar', account_type: 'asset' },

  // Liabilities (2000-2999)
  { account_code: '2010', account_name: 'Leverantörsskulder', account_type: 'liability' },
  { account_code: '2013', account_name: 'Leverantörsskulder EUR', account_type: 'liability' },
  { account_code: '2014', account_name: 'Leverantörsskulder USD', account_type: 'liability' },
  { account_code: '2440', account_name: 'Utgående moms', account_type: 'liability' },
  { account_code: '2450', account_name: 'Ingående moms', account_type: 'asset' },
  { account_code: '2710', account_name: 'Personalskatt', account_type: 'liability' },
  { account_code: '2730', account_name: 'Avräkning för sociala avgifter', account_type: 'liability' },

  // Equity (2000-2099)
  { account_code: '2010', account_name: 'Aktiekapital', account_type: 'equity' },
  { account_code: '2091', account_name: 'Balanserad vinst eller förlust', account_type: 'equity' },

  // Revenue (3000-3999)
  { account_code: '3010', account_name: 'Försäljning inom Sverige', account_type: 'revenue' },
  { account_code: '3011', account_name: 'Försäljning inom EU', account_type: 'revenue' },
  { account_code: '3012', account_name: 'Försäljning utanför EU', account_type: 'revenue' },
  { account_code: '3740', account_name: 'Valutakursvinster', account_type: 'revenue' },

  // Expenses (4000-8999)
  { account_code: '4010', account_name: 'Inköp av råmaterial och förnödenheter', account_type: 'expense' },
  { account_code: '4011', account_name: 'Inköp stål från Tyskland (EUR)', account_type: 'expense' },
  { account_code: '4012', account_name: 'Inköp kompositmaterial från Kina (USD)', account_type: 'expense' },
  { account_code: '5010', account_name: 'Löner till arbetare', account_type: 'expense' },
  { account_code: '5020', account_name: 'Löner till tjänstemän', account_type: 'expense' },
  { account_code: '5410', account_name: 'Sociala avgifter', account_type: 'expense' },
  { account_code: '6110', account_name: 'Kontorsmaterial', account_type: 'expense' },
  { account_code: '6250', account_name: 'Transportkostnader', account_type: 'expense' },
  { account_code: '6540', account_name: 'IT-kostnader', account_type: 'expense' },
  { account_code: '7740', account_name: 'Valutakursförluster', account_type: 'expense' },
  { account_code: '8310', account_name: 'Ränteintäkter', account_type: 'revenue' },
  { account_code: '8410', account_name: 'Räntekostnader', account_type: 'expense' }
];

// Global voucher counters to ensure unique numbers across the entire period
const globalVoucherCounters = {
  LF: 1000, // Leverantörsfaktura (Supplier invoices)
  KF: 2000, // Kundfaktura (Customer invoices)
  LÖ: 3000, // Löner (Payroll)
  BK: 4000, // Bank
  LG: 5000, // Lager (Inventory)
  SK: 6000, // Skatt (Tax)
  VK: 7000, // Valutakurs (FX)
  DV: 8000  // Diverse (Operational)
};

/**
 * Generate daily verifikationer for a specific date
 */
function generateDailyVerifikationer(date: Date, companyId: string): any[] {
  const verifikationer: any[] = [];
  const dateStr = date.toISOString().split('T')[0];
  const dayOfWeek = date.getDay();
  const dayOfMonth = date.getDate();
  const year = date.getFullYear();

  // Skip weekends
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return verifikationer;
  }

  // Use global counters for unique voucher numbers
  const voucherCounter = { ...globalVoucherCounters };

  // Generate 15-30 verifikationer per working day
  const dailyCount = Math.floor(Math.random() * 16) + 15; // 15-30 entries

  for (let i = 0; i < dailyCount; i++) {
    const transactionType = Math.random();

    if (transactionType < 0.25) {
      // Supplier invoices (25%)
      verifikationer.push(generateSupplierInvoice(dateStr, globalVoucherCounters.LF++, companyId));
    } else if (transactionType < 0.4) {
      // Customer invoices (15%)
      verifikationer.push(generateCustomerInvoice(dateStr, globalVoucherCounters.KF++, companyId));
    } else if (transactionType < 0.7) {
      // Bank transactions (30%)
      verifikationer.push(generateBankTransaction(dateStr, globalVoucherCounters.BK++, companyId));
    } else if (transactionType < 0.85) {
      // Inventory adjustments (15%)
      verifikationer.push(generateInventoryAdjustment(dateStr, globalVoucherCounters.LG++, companyId));
    } else {
      // Operational expenses (15%)
      verifikationer.push(generateOperationalExpense(dateStr, globalVoucherCounters.DV++, companyId));
    }
  }

  // Monthly payroll (25th of month)
  if (dayOfMonth === 25) {
    verifikationer.push(generatePayrollEntry(dateStr, globalVoucherCounters.LÖ++, companyId));
  }

  // Monthly tax entries (last working day)
  if (isLastWorkingDay(date)) {
    verifikationer.push(generateTaxEntry(dateStr, globalVoucherCounters.SK++, companyId));
  }

  // FX adjustments (random, 1-2 per week)
  if (Math.random() < 0.3) {
    verifikationer.push(generateFXAdjustment(dateStr, globalVoucherCounters.VK++, companyId));
  }

  return verifikationer;
}

/**
 * Generate supplier invoice
 */
function generateSupplierInvoice(date: string, voucherNumber: number, companyId: string): any {
  const suppliers = [
    { name: 'ThyssenKrupp Steel Europe AG', currency: 'EUR', account: '4011', amount: [50000, 200000] },
    { name: 'Sinocomp Materials Ltd', currency: 'USD', account: '4012', amount: [30000, 150000] },
    { name: 'Sandvik AB', currency: 'SEK', account: '4010', amount: [25000, 100000] },
    { name: 'Scania Logistics AB', currency: 'SEK', account: '6250', amount: [5000, 25000] }
  ];

  const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];
  const baseAmount = Math.floor(Math.random() * (supplier.amount[1] - supplier.amount[0])) + supplier.amount[0];
  const vatRate = supplier.currency === 'SEK' ? 0.25 : 0;
  const vatAmount = baseAmount * vatRate;
  const totalAmount = baseAmount + vatAmount;

  const exchangeRate = supplier.currency === 'SEK' ? 1.0 :
                      supplier.currency === 'EUR' ? 11.5 : 10.5;

  const rows = [
    {
      account_code: supplier.account,
      account_name: getAccountName(supplier.account),
      debit_amount: baseAmount,
      credit_amount: 0,
      currency_code: supplier.currency,
      exchange_rate: exchangeRate,
      description: `Inköp från ${supplier.name}`,
      row_number: 1
    }
  ];

  if (vatAmount > 0) {
    rows.push({
      account_code: '2450',
      account_name: 'Ingående moms',
      debit_amount: vatAmount,
      credit_amount: 0,
      currency_code: supplier.currency,
      exchange_rate: exchangeRate,
      description: 'Ingående moms 25%',
      row_number: 2
    });
  }

  rows.push({
    account_code: supplier.currency === 'SEK' ? '2010' :
                  supplier.currency === 'EUR' ? '2013' : '2014',
    account_name: `Leverantörsskulder ${supplier.currency}`,
    debit_amount: 0,
    credit_amount: totalAmount,
    currency_code: supplier.currency,
    exchange_rate: exchangeRate,
    description: supplier.name,
    row_number: rows.length + 1
  });

  return {
    company_id: companyId,
    voucher_series: 'LF',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Leverantörsfaktura - ${supplier.name}`,
    total_amount: totalAmount,
    currency_code: supplier.currency,
    exchange_rate: exchangeRate,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate customer invoice
 */
function generateCustomerInvoice(date: string, voucherNumber: number, companyId: string): any {
  const customers = [
    { name: 'Norsk Vindkraft AS', currency: 'NOK', amount: [500000, 2000000] },
    { name: 'Ørsted Wind Power A/S', currency: 'DKK', amount: [400000, 1800000] },
    { name: 'Vattenfall Wind Power GmbH', currency: 'EUR', amount: [600000, 2500000] },
    { name: 'American Wind Solutions Inc', currency: 'USD', amount: [800000, 3000000] },
    { name: 'Svensk Vindenergi AB', currency: 'SEK', amount: [300000, 1500000] }
  ];

  const customer = customers[Math.floor(Math.random() * customers.length)];
  const baseAmount = Math.floor(Math.random() * (customer.amount[1] - customer.amount[0])) + customer.amount[0];
  const vatRate = customer.currency === 'SEK' ? 0.25 : 0;
  const vatAmount = baseAmount * vatRate;
  const totalAmount = baseAmount + vatAmount;

  const exchangeRate = customer.currency === 'SEK' ? 1.0 :
                      customer.currency === 'EUR' ? 11.5 :
                      customer.currency === 'USD' ? 10.5 :
                      customer.currency === 'NOK' ? 0.95 : 1.54;

  const accountCode = customer.currency === 'SEK' ? '3010' :
                     ['NOK', 'DKK'].includes(customer.currency) ? '3011' : '3012';

  const rows = [
    {
      account_code: customer.currency === 'SEK' ? '1510' :
                    customer.currency === 'EUR' ? '1513' :
                    customer.currency === 'USD' ? '1514' : '1515',
      account_name: `Kundfordringar ${customer.currency}`,
      debit_amount: totalAmount,
      credit_amount: 0,
      currency_code: customer.currency,
      exchange_rate: exchangeRate,
      description: customer.name,
      row_number: 1
    },
    {
      account_code: accountCode,
      account_name: getAccountName(accountCode),
      debit_amount: 0,
      credit_amount: baseAmount,
      currency_code: customer.currency,
      exchange_rate: exchangeRate,
      description: 'Försäljning turbinkomponenter',
      row_number: 2
    }
  ];

  if (vatAmount > 0) {
    rows.push({
      account_code: '2440',
      account_name: 'Utgående moms',
      debit_amount: 0,
      credit_amount: vatAmount,
      currency_code: customer.currency,
      exchange_rate: exchangeRate,
      description: 'Utgående moms 25%',
      row_number: 3
    });
  }

  return {
    company_id: companyId,
    voucher_series: 'KF',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Kundfaktura - ${customer.name}`,
    total_amount: totalAmount,
    currency_code: customer.currency,
    exchange_rate: exchangeRate,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate bank transaction
 */
function generateBankTransaction(date: string, voucherNumber: number, companyId: string): any {
  const transactions = [
    { type: 'payment', description: 'Betalning leverantörsfaktura', account: '2010', amount: [10000, 100000] },
    { type: 'receipt', description: 'Kundbetalning', account: '1510', amount: [50000, 500000] },
    { type: 'fee', description: 'Bankavgift', account: '6540', amount: [100, 1000] },
    { type: 'interest', description: 'Ränteintäkt', account: '8310', amount: [500, 5000] }
  ];

  const transaction = transactions[Math.floor(Math.random() * transactions.length)];
  const amount = Math.floor(Math.random() * (transaction.amount[1] - transaction.amount[0])) + transaction.amount[0];

  const rows = [];
  if (transaction.type === 'payment' || transaction.type === 'fee') {
    rows.push(
      {
        account_code: transaction.account,
        account_name: getAccountName(transaction.account),
        debit_amount: amount,
        credit_amount: 0,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: transaction.description,
        row_number: 1
      },
      {
        account_code: '1930',
        account_name: 'Handelsbanken Företagskonto',
        debit_amount: 0,
        credit_amount: amount,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: transaction.description,
        row_number: 2
      }
    );
  } else {
    rows.push(
      {
        account_code: '1930',
        account_name: 'Handelsbanken Företagskonto',
        debit_amount: amount,
        credit_amount: 0,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: transaction.description,
        row_number: 1
      },
      {
        account_code: transaction.account,
        account_name: getAccountName(transaction.account),
        debit_amount: 0,
        credit_amount: amount,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: transaction.description,
        row_number: 2
      }
    );
  }

  return {
    company_id: companyId,
    voucher_series: 'BK',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: transaction.description,
    total_amount: amount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate inventory adjustment
 */
function generateInventoryAdjustment(date: string, voucherNumber: number, companyId: string): any {
  const adjustments = [
    { account: '1410', description: 'Lagerinventering råmaterial' },
    { account: '1420', description: 'Värdering produkter i arbete' },
    { account: '1430', description: 'Färdiga varor till lager' }
  ];

  const adjustment = adjustments[Math.floor(Math.random() * adjustments.length)];
  const amount = Math.floor(Math.random() * 90000) + 10000;
  const isIncrease = Math.random() > 0.3;

  const rows = [
    {
      account_code: adjustment.account,
      account_name: getAccountName(adjustment.account),
      debit_amount: isIncrease ? amount : 0,
      credit_amount: isIncrease ? 0 : amount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: adjustment.description,
      row_number: 1
    },
    {
      account_code: '4010',
      account_name: 'Inköp av råmaterial och förnödenheter',
      debit_amount: isIncrease ? 0 : amount,
      credit_amount: isIncrease ? amount : 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: adjustment.description,
      row_number: 2
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'LG',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: adjustment.description,
    total_amount: amount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate operational expense
 */
function generateOperationalExpense(date: string, voucherNumber: number, companyId: string): any {
  const expenses = [
    { account: '6110', description: 'Kontorsmaterial', amount: [500, 5000] },
    { account: '6250', description: 'Transportkostnader', amount: [2000, 15000] },
    { account: '6540', description: 'IT-tjänster', amount: [1000, 10000] }
  ];

  const expense = expenses[Math.floor(Math.random() * expenses.length)];
  const baseAmount = Math.floor(Math.random() * (expense.amount[1] - expense.amount[0])) + expense.amount[0];
  const vatAmount = baseAmount * 0.25;
  const totalAmount = baseAmount + vatAmount;

  const rows = [
    {
      account_code: expense.account,
      account_name: getAccountName(expense.account),
      debit_amount: baseAmount,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: expense.description,
      row_number: 1
    },
    {
      account_code: '2450',
      account_name: 'Ingående moms',
      debit_amount: vatAmount,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Ingående moms 25%',
      row_number: 2
    },
    {
      account_code: '2010',
      account_name: 'Leverantörsskulder',
      debit_amount: 0,
      credit_amount: totalAmount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: expense.description,
      row_number: 3
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'DV',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: expense.description,
    total_amount: totalAmount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate payroll entry (monthly)
 */
function generatePayrollEntry(date: string, voucherNumber: number, companyId: string): any {
  const grossSalaries = 2800000;
  const socialContributions = grossSalaries * 0.31;
  const preliminaryTax = grossSalaries * 0.30;
  const netSalaries = grossSalaries - preliminaryTax;

  const rows = [
    {
      account_code: '5010',
      account_name: 'Löner till arbetare',
      debit_amount: grossSalaries * 0.7,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Löner produktionspersonal',
      row_number: 1
    },
    {
      account_code: '5020',
      account_name: 'Löner till tjänstemän',
      debit_amount: grossSalaries * 0.3,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Löner kontorspersonal',
      row_number: 2
    },
    {
      account_code: '5410',
      account_name: 'Sociala avgifter',
      debit_amount: socialContributions,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Arbetsgivaravgifter',
      row_number: 3
    },
    {
      account_code: '2710',
      account_name: 'Personalskatt',
      debit_amount: 0,
      credit_amount: preliminaryTax + socialContributions,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Skatter och avgifter',
      row_number: 4
    },
    {
      account_code: '1930',
      account_name: 'Handelsbanken Företagskonto',
      debit_amount: 0,
      credit_amount: netSalaries,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Nettolöner',
      row_number: 5
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'LÖ',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Löner ${new Date(date).toLocaleDateString('sv-SE', { month: 'long', year: 'numeric' })}`,
    total_amount: grossSalaries + socialContributions,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate tax entry (monthly)
 */
function generateTaxEntry(date: string, voucherNumber: number, companyId: string): any {
  const vatPayable = Math.floor(Math.random() * 400000) + 100000;

  const rows = [
    {
      account_code: '2440',
      account_name: 'Utgående moms',
      debit_amount: vatPayable,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Momsbetalning till Skatteverket',
      row_number: 1
    },
    {
      account_code: '1930',
      account_name: 'Handelsbanken Företagskonto',
      debit_amount: 0,
      credit_amount: vatPayable,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Momsbetalning',
      row_number: 2
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'SK',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: 'Momsbetalning',
    total_amount: vatPayable,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate FX adjustment
 */
function generateFXAdjustment(date: string, voucherNumber: number, companyId: string): any {
  const currencies = ['EUR', 'USD', 'NOK'];
  const currency = currencies[Math.floor(Math.random() * currencies.length)];
  const amount = Math.floor(Math.random() * 40000) + 5000;
  const isGain = Math.random() > 0.5;

  const rows = [
    {
      account_code: isGain ? '3740' : '7740',
      account_name: isGain ? 'Valutakursvinster' : 'Valutakursförluster',
      debit_amount: isGain ? 0 : amount,
      credit_amount: isGain ? amount : 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: `Valutakursjustering ${currency}`,
      row_number: 1
    },
    {
      account_code: currency === 'EUR' ? '1931' : currency === 'USD' ? '1932' : '1933',
      account_name: `Valutakonto ${currency}`,
      debit_amount: isGain ? amount : 0,
      credit_amount: isGain ? 0 : amount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: `Omvärdering ${currency}`,
      row_number: 2
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'VK',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Valutakursjustering ${currency}`,
    total_amount: amount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Get account name from code
 */
function getAccountName(accountCode: string): string {
  const account = chartOfAccounts.find(acc => acc.account_code === accountCode);
  return account?.account_name || 'Okänt konto';
}

/**
 * Check if date is last working day of month
 */
function isLastWorkingDay(date: Date): boolean {
  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  while (lastDay.getDay() === 0 || lastDay.getDay() === 6) {
    lastDay.setDate(lastDay.getDate() - 1);
  }
  return date.getDate() === lastDay.getDate();
}

/**
 * Generate historical exchange rates for the entire period (2023-2025)
 * Creates realistic fluctuating rates for each currency pair
 */
function generateHistoricalExchangeRates(): any[] {
  const rates: any[] = [];
  const startDate = new Date('2023-01-01');
  const endDate = new Date('2025-05-31');

  // Base rates with realistic fluctuation ranges
  const baseRates = {
    'EUR-SEK': { base: 11.5, variance: 0.8 },  // 10.7 - 12.3
    'USD-SEK': { base: 10.5, variance: 1.0 },  // 9.5 - 11.5
    'NOK-SEK': { base: 0.95, variance: 0.1 },  // 0.85 - 1.05
    'DKK-SEK': { base: 1.54, variance: 0.12 }, // 1.42 - 1.66
    'SEK-EUR': { base: 0.087, variance: 0.006 },
    'SEK-USD': { base: 0.095, variance: 0.009 },
    'SEK-NOK': { base: 1.05, variance: 0.11 },
    'SEK-DKK': { base: 0.65, variance: 0.05 }
  };

  const currentDate = new Date(startDate);
  let dayCounter = 0;

  while (currentDate <= endDate) {
    // Generate rates every 7 days (weekly updates)
    if (dayCounter % 7 === 0) {
      const dateStr = currentDate.toISOString().split('T')[0];

      Object.entries(baseRates).forEach(([pair, config]) => {
        const [base, target] = pair.split('-');

        // Create realistic fluctuation using sine wave + random noise
        const timeProgress = dayCounter / 365; // Years since start
        const seasonalVariation = Math.sin(timeProgress * 2 * Math.PI) * 0.3; // Seasonal cycle
        const randomNoise = (Math.random() - 0.5) * 0.4; // Random daily variation
        const trendFactor = Math.sin(timeProgress * 0.5) * 0.2; // Long-term trend

        const fluctuation = (seasonalVariation + randomNoise + trendFactor) * config.variance;
        const rate = config.base + fluctuation;

        rates.push({
          base_currency: base,
          target_currency: target,
          rate: Math.round(rate * 1000000) / 1000000, // 6 decimal precision
          rate_date: dateStr,
          source: 'historical_simulation'
        });
      });
    }

    currentDate.setDate(currentDate.getDate() + 1);
    dayCounter++;
  }

  console.log(`Generated ${rates.length} historical exchange rate entries`);
  return rates;
}

/**
 * MAIN FUNCTION: Insert comprehensive demo data directly into Supabase
 * Generates 2+ years of daily verifikationer (Jan 1, 2023 to May 31, 2025)
 * Target: 300-600 verifikationer per month = ~8,700-17,400 total entries
 */
export async function insertComprehensiveDemoData(): Promise<void> {
  console.log('🚀 Starting comprehensive demo data insertion...');
  console.log('📊 Target: 2+ years of data (Jan 1, 2023 to May 31, 2025)');
  console.log('📊 Expected: ~8,700-17,400 verifikationer total (300-600 per month)');

  try {
    // 1. Clean up existing data
    console.log('🧹 Cleaning up existing data...');
    await supabase.from('companies').delete().eq('organization_number', '556123-4567');

    // 2. Insert company
    console.log('🏢 Creating Nordström GreenTech AB...');
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .insert(nordstromProfile)
      .select()
      .single();

    if (companyError) {
      console.error('❌ Company creation failed:', companyError);
      throw new Error(`Company creation failed: ${companyError.message}`);
    }

    console.log('✅ Company created:', company.company_name, company.id);

    // 3. Generate historical exchange rates for the entire period
    console.log('💱 Generating historical exchange rates...');
    const historicalRates = generateHistoricalExchangeRates();
    const { error: ratesError } = await supabase
      .from('exchange_rates')
      .upsert(historicalRates, { onConflict: 'base_currency,target_currency,rate_date' });

    if (ratesError) {
      console.warn('⚠️ Exchange rates warning:', ratesError.message);
    } else {
      console.log('✅ Historical exchange rates inserted:', historicalRates.length, 'rates');
    }

    // 4. Insert chart of accounts
    console.log('📊 Inserting BAS 2025 chart of accounts...');
    const accountsWithCompanyId = chartOfAccounts.map(account => ({
      ...account,
      company_id: company.id,
      is_active: true
    }));

    const { error: accountsError } = await supabase
      .from('chart_of_accounts')
      .insert(accountsWithCompanyId);

    if (accountsError) {
      console.error('❌ Chart of accounts failed:', accountsError);
      throw new Error(`Chart of accounts failed: ${accountsError.message}`);
    }

    console.log('✅ Chart of accounts inserted:', accountsWithCompanyId.length, 'accounts');

    // 5. Generate and insert daily verifikationer for 2+ years
    console.log('📝 Generating daily verifikationer for 2+ years...');

    // Date range: January 1, 2023 to May 31, 2025
    const startDate = new Date('2023-01-01');
    const endDate = new Date('2025-05-31');

    let totalVerifikationer = 0;
    let totalErrors = 0;
    let monthlyCount = 0;
    let currentMonth = startDate.getMonth();
    let currentYear = startDate.getFullYear();
    const currentDate = new Date(startDate);

    console.log(`📅 Date range: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);
    console.log(`📅 Total period: ${Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30.44))} months`);

    while (currentDate <= endDate) {
      try {
        // Track monthly progress
        if (currentDate.getMonth() !== currentMonth || currentDate.getFullYear() !== currentYear) {
          console.log(`📊 Month completed: ${currentYear}-${String(currentMonth + 1).padStart(2, '0')} - ${monthlyCount} verifikationer`);
          monthlyCount = 0;
          currentMonth = currentDate.getMonth();
          currentYear = currentDate.getFullYear();
        }

        const dailyVerifikationer = generateDailyVerifikationer(currentDate, company.id);

        if (dailyVerifikationer.length > 0) {
          // Only log every 10th day to avoid console spam
          if (currentDate.getDate() % 10 === 0) {
            console.log(`📅 ${currentDate.toISOString().split('T')[0]}: ${dailyVerifikationer.length} verifikationer`);
          }

          for (const voucherData of dailyVerifikationer) {
            try {
              // Insert voucher
              const { data: voucher, error: voucherError } = await supabase
                .from('vouchers')
                .insert({
                  company_id: voucherData.company_id,
                  voucher_series: voucherData.voucher_series,
                  voucher_number: voucherData.voucher_number,
                  transaction_date: voucherData.transaction_date,
                  description: voucherData.description,
                  total_amount: voucherData.total_amount,
                  currency_code: voucherData.currency_code,
                  exchange_rate: voucherData.exchange_rate,
                  sync_status: voucherData.sync_status
                })
                .select()
                .single();

              if (voucherError) {
                // Only log errors occasionally to avoid spam
                if (totalErrors % 50 === 0) {
                  console.error(`❌ Voucher error #${totalErrors}:`, voucherError.message);
                }
                totalErrors++;
                continue;
              }

              // Insert voucher rows
              const rowsWithVoucherId = voucherData.rows.map(row => ({
                ...row,
                voucher_id: voucher.id
              }));

              const { error: rowsError } = await supabase
                .from('voucher_rows')
                .insert(rowsWithVoucherId);

              if (rowsError) {
                if (totalErrors % 50 === 0) {
                  console.error(`❌ Rows error #${totalErrors}:`, rowsError.message);
                }
                totalErrors++;
                continue;
              }

              totalVerifikationer++;
              monthlyCount++;

              // Progress update every 1000 verifikationer
              if (totalVerifikationer % 1000 === 0) {
                console.log(`🔄 Progress: ${totalVerifikationer} verifikationer inserted...`);
              }

            } catch (error) {
              totalErrors++;
              if (totalErrors % 100 === 0) {
                console.error(`❌ Processing error #${totalErrors}:`, error);
              }
            }
          }
        }
      } catch (error) {
        console.error(`❌ Error generating for ${currentDate.toISOString().split('T')[0]}:`, error);
        totalErrors++;
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Log final month
    console.log(`📊 Final month: ${currentYear}-${String(currentMonth + 1).padStart(2, '0')} - ${monthlyCount} verifikationer`);

    console.log('🎉 Comprehensive demo data insertion completed!');
    console.log(`📊 Results: ${totalVerifikationer} verifikationer created, ${totalErrors} errors`);
    console.log(`📊 Average: ${Math.round(totalVerifikationer / 29)} verifikationer per month`);
    console.log(`📊 Period: January 1, 2023 to May 31, 2025 (29 months)`);
    console.log('📍 Nordström GreenTech AB is now available in the dashboard');

    if (totalVerifikationer === 0) {
      throw new Error('No verifikationer were successfully created');
    }

  } catch (error) {
    console.error('💥 Comprehensive demo data insertion failed:', error);
    throw error;
  }
}

// Auto-run the insertion when this module is imported
console.log('🔄 Auto-running comprehensive demo data insertion...');
insertComprehensiveDemoData().catch(error => {
  console.error('💥 Auto-insertion failed:', error);
});
