#!/usr/bin/env tsx
/**
 * Standalone Database Seeding Script for Arcim B2B Fintech Platform
 *
 * This script pre-populates the Supabase database with comprehensive demo data
 * for Nordström GreenTech AB, including:
 * - Company setup (org number: 556123-4567)
 * - BAS 2025 compliant chart of accounts
 * - 2+ years of daily verifikationer (Jan 1, 2023 to May 31, 2025)
 * - 300-600 verifikationer entries per month
 * - Historical exchange rates for multi-currency operations
 *
 * Usage:
 *   npm run seed-database
 *   or
 *   npx tsx scripts/seed-database.ts
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Nordström GreenTech AB company profile
const nordstromProfile = {
  organization_number: '556123-4567',
  company_name: 'Nordström GreenTech AB',
  base_currency: 'SEK',
  annual_revenue: *********,
  employee_count: 50,
  sync_status: 'completed',
  last_sync_at: new Date().toISOString()
};

// BAS 2025 compliant chart of accounts (essential accounts)
const chartOfAccounts = [
  // Assets (1000-1999)
  { account_code: '1910', account_name: 'Kassa', account_type: 'asset' },
  { account_code: '1930', account_name: 'Handelsbanken Företagskonto', account_type: 'asset' },
  { account_code: '1931', account_name: 'SEB Valutakonto EUR', account_type: 'asset' },
  { account_code: '1932', account_name: 'SEB Valutakonto USD', account_type: 'asset' },
  { account_code: '1933', account_name: 'Nordea Valutakonto NOK', account_type: 'asset' },
  { account_code: '1934', account_name: 'Danske Bank Valutakonto DKK', account_type: 'asset' },
  { account_code: '1510', account_name: 'Kundfordringar', account_type: 'asset' },
  { account_code: '1513', account_name: 'Kundfordringar EUR', account_type: 'asset' },
  { account_code: '1514', account_name: 'Kundfordringar USD', account_type: 'asset' },
  { account_code: '1515', account_name: 'Kundfordringar NOK', account_type: 'asset' },
  { account_code: '1516', account_name: 'Kundfordringar DKK', account_type: 'asset' },
  { account_code: '1410', account_name: 'Råmaterial och förnödenheter', account_type: 'asset' },
  { account_code: '1420', account_name: 'Varor under tillverkning', account_type: 'asset' },
  { account_code: '1430', account_name: 'Färdiga varor', account_type: 'asset' },
  { account_code: '1220', account_name: 'Maskiner och andra tekniska anläggningar', account_type: 'asset' },
  { account_code: '1250', account_name: 'Inventarier, verktyg och installationer', account_type: 'asset' },

  // Liabilities (2000-2999)
  { account_code: '2010', account_name: 'Leverantörsskulder', account_type: 'liability' },
  { account_code: '2013', account_name: 'Leverantörsskulder EUR', account_type: 'liability' },
  { account_code: '2014', account_name: 'Leverantörsskulder USD', account_type: 'liability' },
  { account_code: '2015', account_name: 'Leverantörsskulder NOK', account_type: 'liability' },
  { account_code: '2440', account_name: 'Utgående moms', account_type: 'liability' },
  { account_code: '2640', account_name: 'Ingående moms', account_type: 'liability' },
  { account_code: '2710', account_name: 'Personalskatt', account_type: 'liability' },
  { account_code: '2730', account_name: 'Avräkning för sociala avgifter', account_type: 'liability' },

  // Equity (2000-2999)
  { account_code: '2081', account_name: 'Aktiekapital', account_type: 'equity' },
  { account_code: '2091', account_name: 'Balanserad vinst eller förlust', account_type: 'equity' },

  // Revenue (3000-3999)
  { account_code: '3010', account_name: 'Försäljning inom Sverige', account_type: 'revenue' },
  { account_code: '3011', account_name: 'Försäljning inom EU', account_type: 'revenue' },
  { account_code: '3012', account_name: 'Försäljning utanför EU', account_type: 'revenue' },
  { account_code: '3740', account_name: 'Valutakursvinster', account_type: 'revenue' },

  // Expenses (4000-8999)
  { account_code: '4010', account_name: 'Inköp av råmaterial och förnödenheter', account_type: 'expense' },
  { account_code: '4011', account_name: 'Inköp stål från Tyskland (EUR)', account_type: 'expense' },
  { account_code: '4012', account_name: 'Inköp kompositmaterial från Kina (USD)', account_type: 'expense' },
  { account_code: '5010', account_name: 'Löner till arbetare', account_type: 'expense' },
  { account_code: '5020', account_name: 'Löner till tjänstemän', account_type: 'expense' },
  { account_code: '5710', account_name: 'Sociala avgifter enligt lag', account_type: 'expense' },
  { account_code: '6110', account_name: 'Kontorsmaterial', account_type: 'expense' },
  { account_code: '6250', account_name: 'Datakommunikation', account_type: 'expense' },
  { account_code: '7740', account_name: 'Valutakursförluster', account_type: 'expense' },
  { account_code: '8310', account_name: 'Ränteintäkter', account_type: 'expense' },
  { account_code: '8410', account_name: 'Räntekostnader', account_type: 'expense' }
];

/**
 * Main seeding function
 */
async function seedDatabase(): Promise<void> {
  console.log('🚀 Starting database seeding for Arcim B2B Fintech Platform');
  console.log('📊 Target: 2+ years of comprehensive financial data');
  console.log('🏢 Company: Nordström GreenTech AB (556123-4567)');
  console.log('📅 Period: January 1, 2023 to May 31, 2025');
  console.log('📈 Expected: ~8,700-17,400 verifikationer total (300-600 per month)');
  console.log('');

  try {
    // Step 1: Clean up existing data
    await cleanupExistingData();

    // Step 2: Create company
    const company = await createCompany();

    // Step 3: Insert chart of accounts
    await insertChartOfAccounts(company.id);

    // Step 4: Generate historical exchange rates
    await insertHistoricalExchangeRates();

    // Step 5: Generate comprehensive verifikationer data
    await generateComprehensiveVerifikationer(company.id);

    console.log('');
    console.log('✅ Database seeding completed successfully!');
    console.log('🎯 The webapp will now load instantly with pre-populated data');
    console.log('📊 Navigate to /dashboard to view Treasury Management with live data');

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
}

/**
 * Clean up existing data
 */
async function cleanupExistingData(): Promise<void> {
  console.log('🧹 Cleaning up existing data...');

  try {
    // Delete existing Nordström GreenTech AB data
    await supabase.from('companies').delete().eq('organization_number', '556123-4567');
    console.log('✅ Existing data cleaned up');
  } catch (error) {
    console.warn('⚠️ Cleanup warning:', error);
  }
}

/**
 * Create company
 */
async function createCompany(): Promise<any> {
  console.log('🏢 Creating Nordström GreenTech AB...');

  const { data: company, error } = await supabase
    .from('companies')
    .insert(nordstromProfile)
    .select()
    .single();

  if (error) {
    throw new Error(`Company creation failed: ${error.message}`);
  }

  console.log('✅ Company created:', company.company_name, company.id);
  return company;
}

/**
 * Insert chart of accounts
 */
async function insertChartOfAccounts(companyId: string): Promise<void> {
  console.log('📊 Inserting BAS 2025 chart of accounts...');

  const accountsWithCompanyId = chartOfAccounts.map(account => ({
    ...account,
    company_id: companyId,
    is_active: true
  }));

  const { error } = await supabase
    .from('chart_of_accounts')
    .insert(accountsWithCompanyId);

  if (error) {
    throw new Error(`Chart of accounts failed: ${error.message}`);
  }

  console.log('✅ Chart of accounts inserted:', chartOfAccounts.length, 'accounts');
}

/**
 * Insert historical exchange rates
 */
async function insertHistoricalExchangeRates(): Promise<void> {
  console.log('💱 Generating historical exchange rates...');

  const startDate = new Date('2023-01-01');
  const endDate = new Date('2025-05-31');
  const exchangeRates: any[] = [];

  // Base rates with realistic fluctuation ranges
  const baseRates = {
    'EUR-SEK': { base: 11.5, variance: 0.8 },
    'USD-SEK': { base: 10.5, variance: 1.0 },
    'NOK-SEK': { base: 0.95, variance: 0.1 },
    'DKK-SEK': { base: 1.54, variance: 0.12 },
    'SEK-EUR': { base: 0.087, variance: 0.006 },
    'SEK-USD': { base: 0.095, variance: 0.009 },
    'SEK-NOK': { base: 1.05, variance: 0.11 },
    'SEK-DKK': { base: 0.65, variance: 0.05 }
  };

  const currentDate = new Date(startDate);
  let dayCounter = 0;

  while (currentDate <= endDate) {
    // Generate rates every 7 days (weekly updates)
    if (dayCounter % 7 === 0) {
      const dateStr = currentDate.toISOString().split('T')[0];

      Object.entries(baseRates).forEach(([pair, config]) => {
        const [base, target] = pair.split('-');
        const fluctuation = (Math.random() - 0.5) * 2 * config.variance;
        const rate = Math.max(0.01, config.base + fluctuation);

        exchangeRates.push({
          base_currency: base,
          target_currency: target,
          rate: parseFloat(rate.toFixed(4)),
          rate_date: dateStr,
          source: 'ECB'
        });
      });
    }

    currentDate.setDate(currentDate.getDate() + 1);
    dayCounter++;
  }

  const { error } = await supabase
    .from('exchange_rates')
    .upsert(exchangeRates, { onConflict: 'base_currency,target_currency,rate_date' });

  if (error) {
    console.warn('⚠️ Exchange rates warning:', error.message);
  } else {
    console.log('✅ Historical exchange rates inserted:', exchangeRates.length, 'rates');
  }
}

/**
 * Generate daily verifikationer for a specific date
 */
function generateDailyVerifikationer(date: Date, companyId: string, globalVoucherCounters: any): any[] {
  const verifikationer: any[] = [];
  const dateStr = date.toISOString().split('T')[0];
  const dayOfWeek = date.getDay();
  const dayOfMonth = date.getDate();

  // Skip weekends
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return verifikationer;
  }

  // Generate 15-30 verifikationer per working day
  const dailyCount = Math.floor(Math.random() * 16) + 15; // 15-30 entries

  for (let i = 0; i < dailyCount; i++) {
    const transactionType = Math.random();

    if (transactionType < 0.4) {
      // Customer invoices (40%)
      verifikationer.push(generateCustomerInvoice(dateStr, globalVoucherCounters.KF++, companyId));
    } else if (transactionType < 0.7) {
      // Supplier invoices (30%)
      verifikationer.push(generateSupplierInvoice(dateStr, globalVoucherCounters.LF++, companyId));
    } else {
      // Other transactions (30%)
      verifikationer.push(generateOtherTransaction(dateStr, globalVoucherCounters.ÖV++, companyId));
    }
  }

  // Monthly payroll (25th of month)
  if (dayOfMonth === 25) {
    verifikationer.push(generatePayrollEntry(dateStr, globalVoucherCounters.LÖ++, companyId));
  }

  // Monthly tax entries (last working day)
  if (isLastWorkingDay(date)) {
    verifikationer.push(generateTaxEntry(dateStr, globalVoucherCounters.SK++, companyId));
  }

  // FX adjustments (random, 1-2 per week)
  if (Math.random() < 0.3) {
    verifikationer.push(generateFXAdjustment(dateStr, globalVoucherCounters.VK++, companyId));
  }

  return verifikationer;
}

/**
 * Generate customer invoice
 */
function generateCustomerInvoice(date: string, voucherNumber: number, companyId: string): any {
  const customers = [
    { name: 'Norsk Vindkraft AS', currency: 'NOK', amount: [800000, 2500000] },
    { name: 'Vattenfall AB', currency: 'SEK', amount: [1200000, 3000000] },
    { name: 'Ørsted A/S', currency: 'DKK', amount: [600000, 1800000] },
    { name: 'American Wind Solutions Inc', currency: 'USD', amount: [150000, 400000] },
    { name: 'EDF Renewables', currency: 'EUR', amount: [100000, 300000] }
  ];

  const customer = customers[Math.floor(Math.random() * customers.length)];
  const baseAmount = Math.floor(Math.random() * (customer.amount[1] - customer.amount[0])) + customer.amount[0];
  const vatRate = customer.currency === 'SEK' ? 0.25 : 0;
  const vatAmount = baseAmount * vatRate;
  const totalAmount = baseAmount + vatAmount;

  const exchangeRate = customer.currency === 'SEK' ? 1.0 :
                      customer.currency === 'EUR' ? 11.5 :
                      customer.currency === 'USD' ? 10.5 :
                      customer.currency === 'NOK' ? 0.95 : 1.54;

  const accountCode = customer.currency === 'SEK' ? '3010' :
                     customer.currency === 'EUR' ? '3011' : '3012';

  const rows = [
    {
      account_code: customer.currency === 'SEK' ? '1510' :
                    customer.currency === 'EUR' ? '1513' :
                    customer.currency === 'USD' ? '1514' : '1515',
      account_name: `Kundfordringar ${customer.currency}`,
      debit_amount: totalAmount,
      credit_amount: 0,
      currency_code: customer.currency,
      exchange_rate: exchangeRate,
      description: customer.name,
      row_number: 1
    },
    {
      account_code: accountCode,
      account_name: getAccountName(accountCode),
      debit_amount: 0,
      credit_amount: baseAmount,
      currency_code: customer.currency,
      exchange_rate: exchangeRate,
      description: 'Försäljning turbinkomponenter',
      row_number: 2
    }
  ];

  if (vatAmount > 0) {
    rows.push({
      account_code: '2440',
      account_name: 'Utgående moms',
      debit_amount: 0,
      credit_amount: vatAmount,
      currency_code: customer.currency,
      exchange_rate: exchangeRate,
      description: 'Moms 25%',
      row_number: 3
    });
  }

  return {
    company_id: companyId,
    voucher_series: 'KF',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Kundfaktura ${customer.name}`,
    total_amount: totalAmount,
    currency_code: customer.currency,
    exchange_rate: exchangeRate,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate supplier invoice
 */
function generateSupplierInvoice(date: string, voucherNumber: number, companyId: string): any {
  const suppliers = [
    { name: 'ThyssenKrupp AG', currency: 'EUR', amount: [50000, 150000], account: '4011' },
    { name: 'Sinocomp Ltd', currency: 'USD', amount: [30000, 100000], account: '4012' },
    { name: 'Sandvik AB', currency: 'SEK', amount: [80000, 200000], account: '4010' },
    { name: 'SSAB', currency: 'SEK', amount: [100000, 250000], account: '4010' }
  ];

  const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];
  const baseAmount = Math.floor(Math.random() * (supplier.amount[1] - supplier.amount[0])) + supplier.amount[0];
  const vatRate = supplier.currency === 'SEK' ? 0.25 : 0;
  const vatAmount = baseAmount * vatRate;
  const totalAmount = baseAmount + vatAmount;

  const exchangeRate = supplier.currency === 'SEK' ? 1.0 :
                      supplier.currency === 'EUR' ? 11.5 : 10.5;

  const rows = [
    {
      account_code: supplier.account,
      account_name: getAccountName(supplier.account),
      debit_amount: baseAmount,
      credit_amount: 0,
      currency_code: supplier.currency,
      exchange_rate: exchangeRate,
      description: `Inköp från ${supplier.name}`,
      row_number: 1
    }
  ];

  if (vatAmount > 0) {
    rows.push({
      account_code: '2640',
      account_name: 'Ingående moms',
      debit_amount: vatAmount,
      credit_amount: 0,
      currency_code: supplier.currency,
      exchange_rate: exchangeRate,
      description: 'Moms 25%',
      row_number: 2
    });
  }

  rows.push({
    account_code: supplier.currency === 'SEK' ? '2010' :
                  supplier.currency === 'EUR' ? '2013' : '2014',
    account_name: `Leverantörsskulder ${supplier.currency}`,
    debit_amount: 0,
    credit_amount: totalAmount,
    currency_code: supplier.currency,
    exchange_rate: exchangeRate,
    description: supplier.name,
    row_number: rows.length + 1
  });

  return {
    company_id: companyId,
    voucher_series: 'LF',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Leverantörsfaktura ${supplier.name}`,
    total_amount: totalAmount,
    currency_code: supplier.currency,
    exchange_rate: exchangeRate,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate other transaction types
 */
function generateOtherTransaction(date: string, voucherNumber: number, companyId: string): any {
  const transactions = [
    { description: 'Kontorsmaterial', account: '6110', amount: [2000, 15000] },
    { description: 'Datakommunikation', account: '6250', amount: [5000, 25000] },
    { description: 'Ränteintäkter', account: '8310', amount: [1000, 8000] }
  ];

  const transaction = transactions[Math.floor(Math.random() * transactions.length)];
  const amount = Math.floor(Math.random() * (transaction.amount[1] - transaction.amount[0])) + transaction.amount[0];
  const isExpense = transaction.account.startsWith('6') || transaction.account.startsWith('7');

  const rows = [
    {
      account_code: transaction.account,
      account_name: getAccountName(transaction.account),
      debit_amount: isExpense ? amount : 0,
      credit_amount: isExpense ? 0 : amount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: transaction.description,
      row_number: 1
    },
    {
      account_code: '1930',
      account_name: 'Handelsbanken Företagskonto',
      debit_amount: isExpense ? 0 : amount,
      credit_amount: isExpense ? amount : 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: transaction.description,
      row_number: 2
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'ÖV',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: transaction.description,
    total_amount: amount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate payroll entry
 */
function generatePayrollEntry(date: string, voucherNumber: number, companyId: string): any {
  const grossSalaries = 2800000; // 2.8M SEK monthly
  const socialContributions = grossSalaries * 0.3142; // 31.42% employer contributions
  const preliminaryTax = grossSalaries * 0.30; // 30% preliminary tax
  const netSalaries = grossSalaries - preliminaryTax;

  const rows = [
    {
      account_code: '5010',
      account_name: 'Löner till arbetare',
      debit_amount: grossSalaries * 0.6,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Löner arbetare',
      row_number: 1
    },
    {
      account_code: '5020',
      account_name: 'Löner till tjänstemän',
      debit_amount: grossSalaries * 0.4,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Löner tjänstemän',
      row_number: 2
    },
    {
      account_code: '5710',
      account_name: 'Sociala avgifter enligt lag',
      debit_amount: socialContributions,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Arbetsgivaravgifter',
      row_number: 3
    },
    {
      account_code: '2710',
      account_name: 'Personalskatt',
      debit_amount: 0,
      credit_amount: preliminaryTax + socialContributions,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Skatter och avgifter',
      row_number: 4
    },
    {
      account_code: '1930',
      account_name: 'Handelsbanken Företagskonto',
      debit_amount: 0,
      credit_amount: netSalaries,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Nettolöner',
      row_number: 5
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'LÖ',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Löner ${new Date(date).toLocaleDateString('sv-SE', { month: 'long', year: 'numeric' })}`,
    total_amount: grossSalaries + socialContributions,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate tax entry
 */
function generateTaxEntry(date: string, voucherNumber: number, companyId: string): any {
  const amount = Math.floor(Math.random() * 500000) + 200000; // 200k-700k SEK

  const rows = [
    {
      account_code: '2710',
      account_name: 'Personalskatt',
      debit_amount: amount,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Skatteinbetalning',
      row_number: 1
    },
    {
      account_code: '1930',
      account_name: 'Handelsbanken Företagskonto',
      debit_amount: 0,
      credit_amount: amount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Skatteinbetalning',
      row_number: 2
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'SK',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: 'Skatteinbetalning',
    total_amount: amount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Generate FX adjustment
 */
function generateFXAdjustment(date: string, voucherNumber: number, companyId: string): any {
  const currencies = ['EUR', 'USD', 'NOK'];
  const currency = currencies[Math.floor(Math.random() * currencies.length)];
  const amount = Math.floor(Math.random() * 90000) + 10000;
  const isGain = Math.random() > 0.5;

  const rows = [
    {
      account_code: isGain ? '3740' : '7740',
      account_name: isGain ? 'Valutakursvinster' : 'Valutakursförluster',
      debit_amount: isGain ? 0 : amount,
      credit_amount: isGain ? amount : 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: `Valutakursjustering ${currency}`,
      row_number: 1
    },
    {
      account_code: currency === 'EUR' ? '1931' : currency === 'USD' ? '1932' : '1933',
      account_name: `Valutakonto ${currency}`,
      debit_amount: isGain ? amount : 0,
      credit_amount: isGain ? 0 : amount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: `Omvärdering ${currency}`,
      row_number: 2
    }
  ];

  return {
    company_id: companyId,
    voucher_series: 'VK',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Valutakursjustering ${currency}`,
    total_amount: amount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    sync_status: 'completed',
    rows
  };
}

/**
 * Get account name from code
 */
function getAccountName(accountCode: string): string {
  const account = chartOfAccounts.find(acc => acc.account_code === accountCode);
  return account?.account_name || 'Okänt konto';
}

/**
 * Check if date is last working day of month
 */
function isLastWorkingDay(date: Date): boolean {
  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  while (lastDay.getDay() === 0 || lastDay.getDay() === 6) {
    lastDay.setDate(lastDay.getDate() - 1);
  }
  return date.getDate() === lastDay.getDate();
}

/**
 * Generate comprehensive verifikationer data
 */
async function generateComprehensiveVerifikationer(companyId: string): Promise<void> {
  console.log('📈 Generating comprehensive verifikationer data...');
  console.log('⏳ This may take several minutes for 2+ years of data...');

  const startDate = new Date('2023-01-01');
  const endDate = new Date('2025-05-31');

  let totalVerifikationer = 0;
  let totalErrors = 0;

  // Global voucher counters for unique numbering
  const globalVoucherCounters = {
    KF: 1000,  // Customer invoices
    LF: 2000,  // Supplier invoices
    LÖ: 100,   // Payroll
    SK: 50,    // Tax entries
    VK: 200,   // FX adjustments
    ÖV: 500    // Other vouchers
  };

  const currentDate = new Date(startDate);

  while (currentDate <= endDate) {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    console.log(`📅 Processing ${currentDate.toLocaleDateString('sv-SE', { month: 'long', year: 'numeric' })}...`);

    let monthlyCount = 0;
    const monthStart = new Date(year, month, 1);
    const monthEnd = new Date(year, month + 1, 0);

    // Generate daily verifikationer for the month
    const dailyDate = new Date(monthStart);
    while (dailyDate <= monthEnd) {
      const dayOfWeek = dailyDate.getDay();

      // Skip weekends
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        const dailyVerifikationer = generateDailyVerifikationer(new Date(dailyDate), companyId, globalVoucherCounters);

        // Insert each verifikation
        for (const voucherData of dailyVerifikationer) {
          try {
            // Insert voucher
            const { data: voucher, error: voucherError } = await supabase
              .from('vouchers')
              .insert({
                company_id: voucherData.company_id,
                voucher_series: voucherData.voucher_series,
                voucher_number: voucherData.voucher_number,
                transaction_date: voucherData.transaction_date,
                description: voucherData.description,
                total_amount: voucherData.total_amount,
                currency_code: voucherData.currency_code,
                exchange_rate: voucherData.exchange_rate,
                sync_status: voucherData.sync_status
              })
              .select()
              .single();

            if (voucherError) {
              totalErrors++;
              continue;
            }

            // Insert voucher rows
            const rowsWithVoucherId = voucherData.rows.map(row => ({
              ...row,
              voucher_id: voucher.id
            }));

            const { error: rowsError } = await supabase
              .from('voucher_rows')
              .insert(rowsWithVoucherId);

            if (rowsError) {
              totalErrors++;
              continue;
            }

            totalVerifikationer++;
            monthlyCount++;

            // Progress update every 1000 verifikationer
            if (totalVerifikationer % 1000 === 0) {
              console.log(`🔄 Progress: ${totalVerifikationer} verifikationer inserted...`);
            }

          } catch (error) {
            totalErrors++;
          }
        }
      }

      dailyDate.setDate(dailyDate.getDate() + 1);
    }

    console.log(`✅ ${currentDate.toLocaleDateString('sv-SE', { month: 'long', year: 'numeric' })}: ${monthlyCount} verifikationer`);

    // Move to next month
    currentDate.setMonth(currentDate.getMonth() + 1);
    currentDate.setDate(1);
  }

  console.log('');
  console.log('📊 Verifikationer generation summary:');
  console.log(`✅ Total verifikationer inserted: ${totalVerifikationer}`);
  console.log(`⚠️ Total errors: ${totalErrors}`);
  console.log(`📈 Average per month: ${Math.round(totalVerifikationer / 29)} verifikationer`);
}

// Run the seeding script
if (require.main === module) {
  seedDatabase().catch(console.error);
}

export { seedDatabase };
