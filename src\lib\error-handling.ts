/**
 * Error Handling Utilities
 * Centralized error handling for the Swedish fintech platform
 */

import { ERROR_MESSAGES } from './constants';

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: unknown;
  timestamp: Date;
  context?: Record<string, unknown>;
}

export interface ValidationError extends AppError {
  field: string;
  value: unknown;
}

export interface NetworkError extends AppError {
  status?: number;
  url?: string;
}

// Error codes
export const ERROR_CODES = {
  // Network errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT: 'TIMEOUT',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  SERVER_ERROR: 'SERVER_ERROR',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_ORG_NUMBER: 'INVALID_ORG_NUMBER',
  INVALID_PERSONNUMMER: 'INVALID_PERSONNUMMER',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  
  // Business logic errors
  INSUFFICIENT_FUNDS: 'INSUFFICIENT_FUNDS',
  DUPLICATE_APPLICATION: 'DUPLICATE_APPLICATION',
  APPLICATION_EXPIRED: 'APPLICATION_EXPIRED',
  
  // BankID errors
  BANKID_FAILED: 'BANKID_FAILED',
  BANKID_CANCELLED: 'BANKID_CANCELLED',
  BANKID_TIMEOUT: 'BANKID_TIMEOUT',
  
  // Database errors
  DATABASE_ERROR: 'DATABASE_ERROR',
  CONSTRAINT_VIOLATION: 'CONSTRAINT_VIOLATION',
  
  // Unknown error
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

/**
 * Creates a standardized error object
 */
export function createError(
  code: string,
  message: string,
  details?: unknown,
  context?: Record<string, unknown>
): AppError {
  return {
    code,
    message,
    details,
    timestamp: new Date(),
    context,
  };
}

/**
 * Creates a validation error
 */
export function createValidationError(
  field: string,
  value: unknown,
  message: string,
  context?: Record<string, unknown>
): ValidationError {
  return {
    ...createError(ERROR_CODES.VALIDATION_ERROR, message, value, context),
    field,
    value,
  };
}

/**
 * Creates a network error from a fetch response
 */
export function createNetworkError(
  status: number,
  url: string,
  message?: string,
  context?: Record<string, unknown>
): NetworkError {
  const errorMessage = message || getErrorMessageForStatus(status);
  const code = getErrorCodeForStatus(status);
  
  return {
    ...createError(code, errorMessage, undefined, context),
    status,
    url,
  };
}

/**
 * Gets appropriate error message for HTTP status
 */
function getErrorMessageForStatus(status: number): string {
  switch (status) {
    case 400:
      return ERROR_MESSAGES.VALIDATION_ERROR;
    case 401:
      return ERROR_MESSAGES.UNAUTHORIZED;
    case 403:
      return ERROR_MESSAGES.FORBIDDEN;
    case 404:
      return ERROR_MESSAGES.NOT_FOUND;
    case 408:
      return ERROR_MESSAGES.TIMEOUT;
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_MESSAGES.SERVER_ERROR;
    default:
      return ERROR_MESSAGES.NETWORK_ERROR;
  }
}

/**
 * Gets appropriate error code for HTTP status
 */
function getErrorCodeForStatus(status: number): string {
  switch (status) {
    case 400:
      return ERROR_CODES.VALIDATION_ERROR;
    case 401:
      return ERROR_CODES.UNAUTHORIZED;
    case 403:
      return ERROR_CODES.FORBIDDEN;
    case 404:
      return ERROR_CODES.NOT_FOUND;
    case 408:
      return ERROR_CODES.TIMEOUT;
    case 500:
    case 502:
    case 503:
    case 504:
      return ERROR_CODES.SERVER_ERROR;
    default:
      return ERROR_CODES.NETWORK_ERROR;
  }
}

/**
 * Handles and logs errors consistently
 */
export function handleError(error: unknown, context?: Record<string, unknown>): AppError {
  // If it's already an AppError, just log and return
  if (isAppError(error)) {
    logError(error);
    return error;
  }
  
  // Handle fetch/network errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    const networkError = createError(
      ERROR_CODES.NETWORK_ERROR,
      ERROR_MESSAGES.NETWORK_ERROR,
      error,
      context
    );
    logError(networkError);
    return networkError;
  }
  
  // Handle generic errors
  if (error instanceof Error) {
    const appError = createError(
      ERROR_CODES.UNKNOWN_ERROR,
      error.message || ERROR_MESSAGES.SERVER_ERROR,
      error,
      context
    );
    logError(appError);
    return appError;
  }
  
  // Handle unknown error types
  const unknownError = createError(
    ERROR_CODES.UNKNOWN_ERROR,
    ERROR_MESSAGES.SERVER_ERROR,
    error,
    context
  );
  logError(unknownError);
  return unknownError;
}

/**
 * Type guard to check if an error is an AppError
 */
export function isAppError(error: unknown): error is AppError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'code' in error &&
    'message' in error &&
    'timestamp' in error
  );
}

/**
 * Type guard to check if an error is a ValidationError
 */
export function isValidationError(error: unknown): error is ValidationError {
  return isAppError(error) && 'field' in error && 'value' in error;
}

/**
 * Type guard to check if an error is a NetworkError
 */
export function isNetworkError(error: unknown): error is NetworkError {
  return isAppError(error) && 'status' in error && 'url' in error;
}

/**
 * Logs errors to console and potentially external services
 */
function logError(error: AppError): void {
  // Always log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Application Error:', {
      code: error.code,
      message: error.message,
      timestamp: error.timestamp,
      details: error.details,
      context: error.context,
    });
  }
  
  // In production, you might want to send to an error tracking service
  // Example: Sentry, LogRocket, etc.
  if (process.env.NODE_ENV === 'production') {
    // sendToErrorTrackingService(error);
  }
}

/**
 * Retry utility for operations that might fail
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: unknown;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxAttempts) {
        break;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw handleError(lastError, { maxAttempts, finalAttempt: true });
}

/**
 * Safe async operation wrapper
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<{ data?: T; error?: AppError }> {
  try {
    const data = await operation();
    return { data };
  } catch (error) {
    const appError = handleError(error);
    return { error: appError, data: fallback };
  }
}
