import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  CheckCircle,
  MapPin,
  Phone,
  Mail,
  ArrowRight
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Contact = () => {
  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="pt-36 pb-20 px-6 bg-gradient-to-b from-gray-50 to-white">
        <div className="container-refined mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div className="text-left space-y-6">
              <Badge variant="subtle" className="mb-2 text-xs px-3 py-1 font-medium">
                Get in Touch
              </Badge>
              <h1 className="text-heading-1 text-gray-900 leading-tight">
                Contact Arcim
              </h1>
              <p className="text-body-large text-gray-700 max-w-xl leading-relaxed">
                Our team is ready to discuss your financing needs and how our platform can help your business grow.
              </p>
            </div>
            <div className="relative hidden md:block">
              <div className="relative rounded-2xl overflow-hidden shadow-lg">
                <img
                  src="https://images.unsplash.com/photo-1573164574572-cb89e39749b4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80"
                  alt="Business meeting"
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-section px-6 bg-white">
        <div className="container-refined mx-auto">
          <div className="grid md:grid-cols-2 gap-16 items-start">
            <div className="text-gray-900">
              <h2 className="text-heading-2 text-gray-900 mb-6">
                Ready to Discuss Your Capital Requirements?
              </h2>
              <p className="text-body-large text-gray-700 mb-8">
                Our financing team specializes in creating customized solutions for Swedish businesses with €5M+ annual revenue.
              </p>
              <div className="space-y-6">
                <div className="flex items-start gap-3">
                  <div className="bg-primary-light p-2 rounded-full mt-0.5">
                    <CheckCircle className="h-4 w-4 text-primary" />
                  </div>
                  <span className="text-body text-gray-700">Tailored financing solutions for your specific needs</span>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-primary-light p-2 rounded-full mt-0.5">
                    <CheckCircle className="h-4 w-4 text-primary" />
                  </div>
                  <span className="text-body text-gray-700">Fast decision process with minimal paperwork</span>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-primary-light p-2 rounded-full mt-0.5">
                    <CheckCircle className="h-4 w-4 text-primary" />
                  </div>
                  <span className="text-body text-gray-700">Competitive rates based on your business performance</span>
                </div>
              </div>

              <div className="mt-12 space-y-6 border-t border-gray-100 pt-8">
                <h3 className="text-heading-4 text-gray-900 mb-4">Our Office</h3>
                <div className="flex items-start gap-3">
                  <div className="bg-primary-light p-2 rounded-full mt-0.5">
                    <MapPin className="h-4 w-4 text-primary" />
                  </div>
                  <span className="text-body text-gray-700">Stureplan 4C, 114 35 Stockholm, Sweden</span>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-primary-light p-2 rounded-full mt-0.5">
                    <Phone className="h-4 w-4 text-primary" />
                  </div>
                  <span className="text-body text-gray-700">+46 8 123 45 67</span>
                </div>
                <div className="flex items-start gap-3">
                  <div className="bg-primary-light p-2 rounded-full mt-0.5">
                    <Mail className="h-4 w-4 text-primary" />
                  </div>
                  <span className="text-body text-gray-700"><EMAIL></span>
                </div>
              </div>
            </div>

            <div>
              <Card>
                <CardContent className="p-8">
                  <h3 className="text-heading-3 text-gray-900 mb-8">Schedule a Financing Consultation</h3>
                  <form className="space-y-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700">First Name</label>
                        <Input placeholder="Your first name" />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700">Last Name</label>
                        <Input placeholder="Your last name" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Company Name</label>
                      <Input placeholder="Your company" />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Business Email</label>
                      <Input placeholder="<EMAIL>" type="email" />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Phone Number</label>
                      <Input placeholder="+46 XXX XXX XXX" />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Annual Revenue</label>
                      <select className="w-full h-12 rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-900 shadow-subtle">
                        <option value="">Select Annual Revenue</option>
                        <option value="1-5M">€1M - €5M</option>
                        <option value="5-10M">€5M - €10M</option>
                        <option value="10-50M">€10M - €50M</option>
                        <option value="50M+">€50M+</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-gray-700">Message</label>
                      <textarea
                        className="w-full min-h-[120px] rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-900 resize-none shadow-subtle"
                        placeholder="Tell us about your financing needs"
                      ></textarea>
                    </div>
                    <Button className="w-full h-12">
                      Schedule Consultation
                    </Button>
                    <p className="text-xs text-center text-gray-500 mt-2">
                      We typically respond within 24 hours
                    </p>
                  </form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-section-sm px-6 bg-gray-50">
        <div className="container-refined mx-auto">
          <div className="max-w-5xl mx-auto">
            <div className="bg-white rounded-lg shadow-subtle overflow-hidden">
              <div className="aspect-video w-full bg-gray-200 overflow-hidden">
                {/* This would be replaced with an actual map component */}
                <div className="w-full h-full flex items-center justify-center bg-gray-100">
                  <p className="text-gray-500 font-medium">Interactive Map Would Be Here</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Contact;
