import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Shield, FileText, AlertTriangle, CheckCircle, ExternalLink, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConsentItem {
  id: string;
  title: string;
  description: string;
  required: boolean;
  checked: boolean;
}

interface KYCResult {
  status: 'pending' | 'passed' | 'failed' | 'requires_review';
  checks: Array<{
    name: string;
    status: 'passed' | 'failed' | 'pending';
    description: string;
  }>;
}

interface ConsentComplianceProps {
  onConsentComplete: (consents: Record<string, boolean>, kycResult: KYCResult) => void;
  className?: string;
}

const ConsentCompliance: React.FC<ConsentComplianceProps> = ({
  onConsentComplete,
  className,
}) => {
  const [consents, setConsents] = useState<ConsentItem[]>([
    {
      id: 'terms_of_service',
      title: 'Terms of Service',
      description: 'I agree to the Arcim platform terms of service and user agreement.',
      required: true,
      checked: false,
    },
    {
      id: 'privacy_policy',
      title: 'Privacy Policy',
      description: 'I acknowledge that I have read and understood the privacy policy.',
      required: true,
      checked: false,
    },
    {
      id: 'gdpr_consent',
      title: 'GDPR Data Processing Consent',
      description: 'I consent to the processing of my personal and company data as described in our privacy policy, including data sharing with financial partners for credit assessment purposes.',
      required: true,
      checked: false,
    },
    {
      id: 'kyc_authorization',
      title: 'KYC/KYB Authorization',
      description: 'I authorize Arcim to perform Know Your Customer (KYC) and Know Your Business (KYB) checks, including background verification through Bolagsverket and AML screening.',
      required: true,
      checked: false,
    },
    {
      id: 'credit_check',
      title: 'Credit Assessment Authorization',
      description: 'I authorize credit checks and financial assessments for lending and financial services.',
      required: true,
      checked: false,
    },
    {
      id: 'marketing_consent',
      title: 'Marketing Communications',
      description: 'I consent to receiving marketing communications about relevant financial products and services.',
      required: false,
      checked: false,
    },
  ]);

  const [isRunningKYC, setIsRunningKYC] = useState(false);
  const [kycResult, setKycResult] = useState<KYCResult | null>(null);
  const [showKYCDetails, setShowKYCDetails] = useState(false);

  const handleConsentChange = (id: string, checked: boolean) => {
    setConsents(prev => prev.map(consent => 
      consent.id === id ? { ...consent, checked } : consent
    ));
  };

  const runKYCChecks = async () => {
    setIsRunningKYC(true);
    
    try {
      // Simulate KYC/KYB checks
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Mock KYC result
      const mockResult: KYCResult = {
        status: 'passed',
        checks: [
          {
            name: 'Company Registry Check',
            status: 'passed',
            description: 'Company verified in Bolagsverket registry',
          },
          {
            name: 'Board Member Verification',
            status: 'passed',
            description: 'All board members verified and cleared',
          },
          {
            name: 'AML Screening',
            status: 'passed',
            description: 'No matches found in sanctions or PEP lists',
          },
          {
            name: 'Financial Standing',
            status: 'passed',
            description: 'Company financial status verified',
          },
        ],
      };
      
      setKycResult(mockResult);
    } catch (error) {
      const errorResult: KYCResult = {
        status: 'failed',
        checks: [
          {
            name: 'System Error',
            status: 'failed',
            description: 'Unable to complete verification checks',
          },
        ],
      };
      setKycResult(errorResult);
    } finally {
      setIsRunningKYC(false);
    }
  };

  const handleComplete = () => {
    if (!kycResult) return;
    
    const consentRecord = consents.reduce((acc, consent) => {
      acc[consent.id] = consent.checked;
      return acc;
    }, {} as Record<string, boolean>);
    
    onConsentComplete(consentRecord, kycResult);
  };

  const allRequiredConsentsGiven = consents
    .filter(consent => consent.required)
    .every(consent => consent.checked);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-700 border-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Terms and Consent Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Terms & Consent</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {consents.map((consent) => (
            <div key={consent.id} className="flex items-start space-x-3 p-4 border rounded-lg">
              <Checkbox
                id={consent.id}
                checked={consent.checked}
                onCheckedChange={(checked) => handleConsentChange(consent.id, !!checked)}
                className="mt-1"
              />
              <div className="flex-1">
                <label htmlFor={consent.id} className="cursor-pointer">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{consent.title}</span>
                    {consent.required && (
                      <Badge variant="outline" className="text-xs">Required</Badge>
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{consent.description}</p>
                </label>
              </div>
              <Button variant="ghost" size="sm" className="text-blue-600">
                <ExternalLink className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* KYC/KYB Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>KYC/KYB Verification</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!kycResult && !isRunningKYC && (
            <div className="text-center space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <AlertTriangle className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <h4 className="font-semibold text-blue-900">Verification Required</h4>
                <p className="text-blue-800 text-sm mt-1">
                  We need to verify your company and perform compliance checks before proceeding.
                </p>
              </div>
              <Button
                onClick={runKYCChecks}
                disabled={!allRequiredConsentsGiven}
                className="w-full"
              >
                <Shield className="h-4 w-4 mr-2" />
                Start Verification Process
              </Button>
              {!allRequiredConsentsGiven && (
                <p className="text-sm text-gray-500">
                  Please accept all required consents to proceed with verification.
                </p>
              )}
            </div>
          )}

          {isRunningKYC && (
            <div className="text-center space-y-4 p-6">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
              <div>
                <h4 className="font-semibold text-gray-900">Running verification checks...</h4>
                <p className="text-sm text-gray-600 mt-1">
                  This may take a few moments while we verify your company information.
                </p>
              </div>
            </div>
          )}

          {kycResult && (
            <div className="space-y-4">
              <div className={cn("p-4 rounded-lg border", getStatusColor(kycResult.status))}>
                <div className="flex items-center space-x-2">
                  {kycResult.status === 'passed' ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <AlertTriangle className="h-5 w-5" />
                  )}
                  <span className="font-semibold">
                    Verification {kycResult.status === 'passed' ? 'Completed' : 'Failed'}
                  </span>
                </div>
              </div>

              <Button
                variant="outline"
                onClick={() => setShowKYCDetails(!showKYCDetails)}
                className="w-full"
              >
                {showKYCDetails ? 'Hide' : 'Show'} Verification Details
              </Button>

              {showKYCDetails && (
                <div className="space-y-2">
                  {kycResult.checks.map((check, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{check.name}</p>
                        <p className="text-sm text-gray-600">{check.description}</p>
                      </div>
                      <Badge className={getStatusColor(check.status)}>
                        {check.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}

              {kycResult.status === 'passed' && (
                <Button onClick={handleComplete} className="w-full bg-green-600 hover:bg-green-700">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Complete Compliance Check
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ConsentCompliance;
