import * as React from "react"

import { cn } from "@/lib/utils"

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex h-12 w-full rounded-md border border-gray-300 bg-white px-4 py-2 text-body text-gray-900 shadow-subtle transition-all duration-200",
          "placeholder:text-gray-500/60 placeholder:text-sm",
          "focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30",
          "hover:border-gray-400",
          "disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:border-gray-300",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
