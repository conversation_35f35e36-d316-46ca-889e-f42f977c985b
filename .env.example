# Fortnox API Configuration
VITE_FORTNOX_CLIENT_ID=your_fortnox_client_id_here
VITE_FORTNOX_CLIENT_SECRET=your_fortnox_client_secret_here
VITE_FORTNOX_REDIRECT_URI=http://localhost:5173/auth/fortnox/callback

# Supabase Configuration (already configured)
VITE_SUPABASE_URL=https://oalvyhgprwxydmeossvg.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9hbHZ5aGdwcnd4eWRtZW9zc3ZnIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5OTc5ODEsImV4cCI6MjA2MzU3Mzk4MX0.Ec3lMwbuo4i3Ncc-UpIheBUHm0ErObV13APXsT6aK30

# Exchange Rate API (optional - for live FX rates)
VITE_EXCHANGE_RATE_API_KEY=your_exchange_rate_api_key_here

# Application Configuration
VITE_APP_NAME=Arcim Fintech Platform
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=development
