import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Quote, ArrowUpRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';

const Testimonials = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative pt-32 pb-20 px-6 bg-gradient-to-br from-white via-blue-50/10 to-white overflow-hidden">
        <div className="container relative mx-auto">
          <div className="max-w-3xl mx-auto text-center">
            <Badge variant="outline" className="mb-4 bg-blue-50 text-blue-600 border-blue-200 text-sm px-4 py-1">Client Success Stories</Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Trusted by Sweden's Financial Decision-Makers
            </h1>
            <p className="text-xl text-gray-700 mb-8 max-w-2xl mx-auto">
              Hear from the financial leaders who have partnered with Arcim to fuel their growth.
            </p>
          </div>
        </div>
      </section>
      
      {/* Featured Testimonials */}
      <section className="py-24 px-6 bg-white">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex justify-between items-start mb-6">
                  <Quote className="text-blue-600" size={32} />
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg key={star} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <p className="text-gray-700 mb-6">
                  "Arcim's approach to growth financing aligned perfectly with our seasonal business cycle. Their understanding of our industry fundamentals resulted in terms that traditional lenders couldn't match."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-indigo-500 flex items-center justify-center text-white font-bold mr-4">SL</div>
                  <div>
                    <p className="font-semibold text-gray-900">Sofia Lindberg</p>
                    <p className="text-sm text-gray-600">CFO at Norrsken Manufacturing</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex justify-between items-start mb-6">
                  <Quote className="text-blue-600" size={32} />
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg key={star} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <p className="text-gray-700 mb-6">
                  "The combination of capital access and financial intelligence gave us both the resources and insights needed during our expansion phase. Their platform identified optimization opportunities that improved our cash conversion cycle by 15 days."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center text-white font-bold mr-4">AJ</div>
                  <div>
                    <p className="font-semibold text-gray-900">Anders Johansson</p>
                    <p className="text-sm text-gray-600">Finance Director at TechVision AB</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-gray-300 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-8">
                <div className="flex justify-between items-start mb-6">
                  <Quote className="text-blue-600" size={32} />
                  <div className="flex">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg key={star} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <p className="text-gray-700 mb-6">
                  "Working with Arcim meant dealing with finance professionals who understood our business model from day one. Their structuring expertise saved us significant dilution during our critical growth phase."
                </p>
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white font-bold mr-4">ME</div>
                  <div>
                    <p className="font-semibold text-gray-900">Maria Eriksson</p>
                    <p className="text-sm text-gray-600">CEO at Sustainable Solutions Group</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      
      {/* Additional Testimonials */}
      <section className="py-20 px-6 bg-gray-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              More Success Stories
            </h2>
            <p className="text-xl text-gray-700 max-w-2xl mx-auto">
              See how Arcim has helped businesses across various industries.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="border border-gray-300 shadow-md">
              <CardContent className="p-6">
                <Quote className="text-blue-600 mb-4" size={24} />
                <p className="text-gray-700 mb-4">
                  "Arcim transformed our treasury operations. The adaptive lending feature alone saved us 30% on financing costs this year."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mr-3 flex items-center justify-center text-white font-bold">SJ</div>
                  <div>
                    <div className="font-semibold text-gray-900">Sarah Johnson</div>
                    <div className="text-sm text-gray-600">CFO, TechScale AB</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-gray-300 shadow-md">
              <CardContent className="p-6">
                <Quote className="text-blue-600 mb-4" size={24} />
                <p className="text-gray-700 mb-4">
                  "The real-time integrations give us visibility we never had before. It's like having a financial control tower for our entire business."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mr-3 flex items-center justify-center text-white font-bold">LA</div>
                  <div>
                    <div className="font-semibold text-gray-900">Lars Andersson</div>
                    <div className="text-sm text-gray-600">Treasurer, Nordic Ventures</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-gray-300 shadow-md">
              <CardContent className="p-6">
                <Quote className="text-blue-600 mb-4" size={24} />
                <p className="text-gray-700 mb-4">
                  "Finally, a platform that understands midmarket needs. The FX hedging tools are incredibly sophisticated yet easy to use."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-3 flex items-center justify-center text-white font-bold">EK</div>
                  <div>
                    <div className="font-semibold text-gray-900">Emma Kristensen</div>
                    <div className="text-sm text-gray-600">Finance Director, GrowthCorp</div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="border border-gray-300 shadow-md">
              <CardContent className="p-6">
                <Quote className="text-blue-600 mb-4" size={24} />
                <p className="text-gray-700 mb-4">
                  "The analytics platform has become an essential part of our financial planning. We've been able to identify trends that would have otherwise gone unnoticed."
                </p>
                <div className="flex items-center">
                  <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full mr-3 flex items-center justify-center text-white font-bold">JB</div>
                  <div>
                    <div className="font-semibold text-gray-900">Johan Bergström</div>
                    <div className="text-sm text-gray-600">CFO, Innovate Solutions</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="py-16 px-6 bg-blue-600">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-white mb-6">
              Join our growing list of satisfied clients
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Experience the Arcim difference for your business.
            </p>
            <a href="/contact">
              <Button size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
                Contact Us Today
              </Button>
            </a>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default Testimonials;
