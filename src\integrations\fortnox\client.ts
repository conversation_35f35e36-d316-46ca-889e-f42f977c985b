
/**
 * Fortnox API Client
 * Handles OAuth 2.0 authentication and API requests to Fortnox
 */

export interface FortnoxConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  baseUrl: string;
}

export interface FortnoxTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

export interface FortnoxVoucher {
  VoucherSeries: string;
  VoucherNumber: number;
  TransactionDate: string;
  Description: string;
  VoucherRows: FortnoxVoucherRow[];
  '@url'?: string;
}

export interface FortnoxVoucherRow {
  Account: string;
  AccountName?: string;
  Debit: number;
  Credit: number;
  Description?: string;
  CostCenter?: string;
  Project?: string;
}

export interface FortnoxAccount {
  Number: string;
  Description: string;
  Type: string;
  Active: boolean;
}

export interface FortnoxApiResponse<T> {
  MetaInformation?: {
    '@TotalResources': number;
    '@TotalPages': number;
    '@CurrentPage': number;
  };
  [key: string]: T[] | any;
}

export class FortnoxApiClient {
  private config: FortnoxConfig;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiresAt: Date | null = null;

  constructor(config: FortnoxConfig) {
    this.config = config;
  }

  /**
   * Generate OAuth 2.0 authorization URL with comprehensive scopes
   */
  getAuthorizationUrl(state?: string): string {
    const scopes = [
      'salary',
      'bookkeeping',
      'archive',
      'connectfile',
      'article',
      'companyinformation',
      'settings',
      'invoice',
      'costcenter',
      'currency',
      'customer',
      'inbox',
      'payment',
      'noxfinansinvoice',
      'offer',
      'order',
      'price',
      'print',
      'project',
      'profile',
      'supplierinvoice',
      'supplier',
      'timereporting'
    ].join(' ');

    const params = new URLSearchParams({
      client_id: this.config.clientId,
      redirect_uri: this.config.redirectUri,
      response_type: 'code',
      scope: scopes,
      ...(state && { state })
    });

    return `${this.config.baseUrl}/oauth-v1/auth?${params.toString()}`;
  }

  /**
   * Exchange authorization code for access tokens
   */
  async exchangeCodeForTokens(code: string): Promise<FortnoxTokens> {
    const response = await fetch(`${this.config.baseUrl}/oauth-v1/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${btoa(`${this.config.clientId}:${this.config.clientSecret}`)}`
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: this.config.redirectUri
      })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Token exchange failed: ${error}`);
    }

    const tokens: FortnoxTokens = await response.json();
    this.setTokens(tokens);
    return tokens;
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(): Promise<FortnoxTokens> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await fetch(`${this.config.baseUrl}/oauth-v1/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${btoa(`${this.config.clientId}:${this.config.clientSecret}`)}`
      },
      body: new URLSearchParams({
        grant_type: 'refresh_token',
        refresh_token: this.refreshToken
      })
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Token refresh failed: ${error}`);
    }

    const tokens: FortnoxTokens = await response.json();
    this.setTokens(tokens);
    return tokens;
  }

  /**
   * Set tokens and expiration time
   */
  setTokens(tokens: FortnoxTokens): void {
    this.accessToken = tokens.access_token;
    this.refreshToken = tokens.refresh_token;
    this.tokenExpiresAt = new Date(Date.now() + tokens.expires_in * 1000);
  }

  /**
   * Check if access token is valid and refresh if needed
   */
  private async ensureValidToken(): Promise<void> {
    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    // Check if token expires within 5 minutes
    if (this.tokenExpiresAt && this.tokenExpiresAt.getTime() - Date.now() < 5 * 60 * 1000) {
      await this.refreshAccessToken();
    }
  }

  /**
   * Make authenticated API request
   */
  private async apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    await this.ensureValidToken();

    const response = await fetch(`${this.config.baseUrl}/3${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      }
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`API request failed: ${response.status} ${error}`);
    }

    return response.json();
  }

  /**
   * Get vouchers with pagination
   */
  async getVouchers(params: {
    page?: number;
    limit?: number;
    fromDate?: string;
    toDate?: string;
    voucherSeries?: string;
  } = {}): Promise<FortnoxApiResponse<FortnoxVoucher>> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.set('page', params.page.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.fromDate) searchParams.set('fromdate', params.fromDate);
    if (params.toDate) searchParams.set('todate', params.toDate);
    if (params.voucherSeries) searchParams.set('voucherseries', params.voucherSeries);

    const endpoint = `/vouchers?${searchParams.toString()}`;
    return this.apiRequest<FortnoxApiResponse<FortnoxVoucher>>(endpoint);
  }

  /**
   * Get specific voucher by series and number
   */
  async getVoucher(voucherSeries: string, voucherNumber: number): Promise<{ Voucher: FortnoxVoucher }> {
    const endpoint = `/vouchers/${voucherSeries}/${voucherNumber}`;
    return this.apiRequest<{ Voucher: FortnoxVoucher }>(endpoint);
  }

  /**
   * Get chart of accounts
   */
  async getAccounts(): Promise<FortnoxApiResponse<FortnoxAccount>> {
    return this.apiRequest<FortnoxApiResponse<FortnoxAccount>>('/accounts');
  }

  /**
   * Get company information
   */
  async getCompanyInformation(): Promise<any> {
    return this.apiRequest('/companyinformation');
  }
}

// Default configuration for Fortnox API
export const fortnoxConfig: FortnoxConfig = {
  clientId: '1Y9FA35cHyB3',
  clientSecret: '0wSksFb65q',
  redirectUri: 'https://arcim-fintech-nexus.lovableproject.com/auth/fortnox/callback',
  baseUrl: 'https://api.fortnox.se'
};

// Export singleton instance
export const fortnoxClient = new FortnoxApiClient(fortnoxConfig);
