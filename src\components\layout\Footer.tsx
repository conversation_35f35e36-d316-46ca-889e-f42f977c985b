
import React from 'react';
import { Linkedin, Twitter, Facebook, Instagram } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="container-clean py-16">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-12 mb-12">
          <div className="md:col-span-4">
            <div className="flex items-center mb-6">
              <div className="text-heading-lg font-semibold text-gray-900">Arcim</div>
            </div>
            <p className="text-body-md text-gray-600 max-w-md mb-6">
              Arcim AB is a regulated payment institution with Sweden's financial supervisory authority (Finansinspektionen), identification number 57683.
            </p>
            <p className="text-body-sm text-gray-500">
              Org number: 559223-3208
            </p>
          </div>

          <div className="md:col-span-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div>
                <h3 className="text-label-lg text-gray-900 font-semibold mb-4">Company</h3>
                <ul className="space-y-3">
                  <li>
                    <a href="/about" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      About us
                    </a>
                  </li>
                  <li>
                    <a href="/financing" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Benefits
                    </a>
                  </li>
                  <li>
                    <a href="/testimonials" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Comparison
                    </a>
                  </li>
                  <li>
                    <a href="/careers" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Careers
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-label-lg text-gray-900 font-semibold mb-4">Product</h3>
                <ul className="space-y-3">
                  <li>
                    <a href="/platform" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      How it works
                    </a>
                  </li>
                  <li>
                    <a href="/pricing" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Pricing
                    </a>
                  </li>
                  <li>
                    <a href="/faq" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      FAQ
                    </a>
                  </li>
                  <li>
                    <a href="/security" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Security
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-label-lg text-gray-900 font-semibold mb-4">Resources</h3>
                <ul className="space-y-3">
                  <li>
                    <a href="/blog" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Blog
                    </a>
                  </li>
                  <li>
                    <a href="/guides" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Guides
                    </a>
                  </li>
                  <li>
                    <a href="/case-studies" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Case studies
                    </a>
                  </li>
                  <li>
                    <a href="/api" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      API docs
                    </a>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-label-lg text-gray-900 font-semibold mb-4">Legal</h3>
                <ul className="space-y-3">
                  <li>
                    <a href="/privacy" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Privacy policy
                    </a>
                  </li>
                  <li>
                    <a href="/terms" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Terms of service
                    </a>
                  </li>
                  <li>
                    <a href="/cookies" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      Cookie policy
                    </a>
                  </li>
                  <li>
                    <a href="/gdpr" className="text-body-md text-gray-600 hover:text-gray-900 transition-colors">
                      GDPR
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-200 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-body-sm text-gray-500 mb-4 md:mb-0">
            © 2024 Arcim AB. All rights reserved.
          </div>
          <div className="flex items-center space-x-6">
            <a href="https://linkedin.com" className="text-gray-400 hover:text-gray-900 transition-colors" aria-label="LinkedIn">
              <Linkedin className="h-5 w-5" />
            </a>
            <a href="https://twitter.com" className="text-gray-400 hover:text-gray-900 transition-colors" aria-label="Twitter">
              <Twitter className="h-5 w-5" />
            </a>
            <a href="https://instagram.com" className="text-gray-400 hover:text-gray-900 transition-colors" aria-label="Instagram">
              <Instagram className="h-5 w-5" />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default Footer;
