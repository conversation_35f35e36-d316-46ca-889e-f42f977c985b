import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Building2, MapPin, Calendar, Users, Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { CompanyType } from '@/lib/validation';

interface CompanyData {
  organizationNumber: string;
  companyName: string;
  companyType: CompanyType;
  registeredAddress: {
    street: string;
    postalCode: string;
    city: string;
  };
  registrationDate: string;
  status: 'active' | 'inactive' | 'dissolved';
  boardMembers: Array<{
    name: string;
    role: string;
    appointmentDate: string;
  }>;
  shareCapital?: number;
  industry?: string;
  employees?: number;
}

interface CompanyDataFetcherProps {
  organizationNumber: string;
  onDataFetched: (data: CompanyData) => void;
  className?: string;
}

const CompanyDataFetcher: React.FC<CompanyDataFetcherProps> = ({
  organizationNumber,
  onDataFetched,
  className,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [companyData, setCompanyData] = useState<CompanyData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConfirmed, setIsConfirmed] = useState(false);

  // Auto-fetch when organization number is provided
  useEffect(() => {
    if (organizationNumber && organizationNumber.length >= 10) {
      fetchCompanyData();
    }
  }, [organizationNumber]);

  const fetchCompanyData = async () => {
    if (!organizationNumber) return;

    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call to Bolagsverket
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Mock company data - in real implementation, call Bolagsverket API
      const mockData: CompanyData = {
        organizationNumber,
        companyName: 'Nordström GreenTech AB',
        companyType: CompanyType.AKTIEBOLAG,
        registeredAddress: {
          street: 'Storgatan 15',
          postalCode: '111 51',
          city: 'Stockholm',
        },
        registrationDate: '2018-03-15',
        status: 'active',
        boardMembers: [
          {
            name: 'Erik Nordström',
            role: 'Verkställande direktör',
            appointmentDate: '2018-03-15',
          },
          {
            name: 'Anna Lindberg',
            role: 'Styrelseordförande',
            appointmentDate: '2018-03-15',
          },
          {
            name: 'Magnus Eriksson',
            role: 'Styrelseledamot',
            appointmentDate: '2020-06-10',
          },
        ],
        shareCapital: 500000,
        industry: 'Wind Turbine Components Manufacturing',
        employees: 50,
      };

      setCompanyData(mockData);
    } catch (error) {
      setError('Failed to fetch company data from Bolagsverket. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirm = () => {
    if (companyData) {
      setIsConfirmed(true);
      onDataFetched(companyData);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'dissolved':
        return 'bg-red-100 text-red-700 border-red-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center p-8 border border-gray-200 rounded-lg", className)}>
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
          <div>
            <p className="font-medium text-gray-900">Fetching company data...</p>
            <p className="text-sm text-gray-500">Connecting to Bolagsverket registry</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("p-4 border border-red-200 rounded-lg bg-red-50", className)}>
        <div className="flex items-center space-x-3">
          <AlertCircle className="h-5 w-5 text-red-600" />
          <div className="flex-1">
            <p className="text-red-800 font-medium">Error fetching company data</p>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchCompanyData}
            className="text-red-600 border-red-300 hover:bg-red-100"
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!companyData) {
    return null;
  }

  if (isConfirmed) {
    return (
      <div className={cn("p-4 border border-green-200 rounded-lg bg-green-50", className)}>
        <div className="flex items-center space-x-3">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <div>
            <p className="text-green-800 font-medium">Company data confirmed</p>
            <p className="text-green-600 text-sm">{companyData.companyName}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Building2 className="h-5 w-5" />
          <span>Company Information from Bolagsverket</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">Basic Information</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-gray-500">Company Name:</span>
                <p className="font-medium">{companyData.companyName}</p>
              </div>
              <div>
                <span className="text-gray-500">Organization Number:</span>
                <p className="font-medium">{companyData.organizationNumber}</p>
              </div>
              <div>
                <span className="text-gray-500">Company Type:</span>
                <Badge variant="outline" className="ml-2">
                  {companyData.companyType}
                </Badge>
              </div>
              <div>
                <span className="text-gray-500">Status:</span>
                <Badge className={cn("ml-2", getStatusColor(companyData.status))}>
                  {companyData.status.charAt(0).toUpperCase() + companyData.status.slice(1)}
                </Badge>
              </div>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-gray-900 mb-2">Registration Details</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-start space-x-2">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                <div>
                  <p className="font-medium">{companyData.registeredAddress.street}</p>
                  <p className="text-gray-600">
                    {companyData.registeredAddress.postalCode} {companyData.registeredAddress.city}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <span className="text-gray-500">Registered:</span>
                  <span className="ml-1 font-medium">{companyData.registrationDate}</span>
                </div>
              </div>
              {companyData.employees && (
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-400" />
                  <div>
                    <span className="text-gray-500">Employees:</span>
                    <span className="ml-1 font-medium">{companyData.employees}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-semibold text-gray-900 mb-3">Board Members</h4>
          <div className="space-y-2">
            {companyData.boardMembers.map((member, index) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-gray-900">{member.name}</p>
                  <p className="text-sm text-gray-600">{member.role}</p>
                </div>
                <p className="text-xs text-gray-500">Since {member.appointmentDate}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="flex items-center justify-between pt-4 border-t">
          <p className="text-sm text-gray-600">
            Please confirm that this information is correct for your company.
          </p>
          <Button onClick={handleConfirm} className="bg-green-600 hover:bg-green-700">
            <CheckCircle className="h-4 w-4 mr-2" />
            Confirm Company Data
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CompanyDataFetcher;
