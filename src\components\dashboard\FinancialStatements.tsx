/**
 * BAS 2025 Compliant Financial Statements Dashboard
 * Displays balance sheet and income statement with Swedish accounting standards
 */

import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  TrendingUp, 
  Calculator, 
  RefreshCw,
  Download,
  Calendar,
  BarChart3
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  generateBalanceSheet, 
  generateIncomeStatement, 
  calculateWorkingCapitalMetrics,
  BalanceSheetData,
  IncomeStatementData,
  WorkingCapitalMetrics
} from '@/integrations/analytics/financial-statements';
import { supabase } from '@/integrations/supabase/client';

const FinancialStatements: React.FC = () => {
  const [balanceSheet, setBalanceSheet] = useState<BalanceSheetData | null>(null);
  const [incomeStatement, setIncomeStatement] = useState<IncomeStatementData | null>(null);
  const [workingCapital, setWorkingCapital] = useState<WorkingCapitalMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [companyId, setCompanyId] = useState<string>('');

  useEffect(() => {
    loadFinancialStatements();
  }, [selectedPeriod]);

  const loadFinancialStatements = async () => {
    try {
      setLoading(true);

      // Get demo company
      const { data: companies } = await supabase
        .from('companies')
        .select('*')
        .eq('company_name', 'Nordström GreenTech AB')
        .limit(1);

      if (!companies || companies.length === 0) return;

      const company = companies[0];
      setCompanyId(company.id);

      // Calculate date ranges
      const today = new Date();
      const asOfDate = today.toISOString().split('T')[0];
      
      let fromDate: string;
      switch (selectedPeriod) {
        case 'current-month':
          fromDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
          break;
        case 'current-quarter':
          const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1);
          fromDate = quarterStart.toISOString().split('T')[0];
          break;
        case 'current-year':
          fromDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
          break;
        default:
          fromDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
      }

      // Load financial statements
      const [balanceSheetData, incomeStatementData, workingCapitalData] = await Promise.all([
        generateBalanceSheet(company.id, asOfDate),
        generateIncomeStatement(company.id, fromDate, asOfDate),
        calculateWorkingCapitalMetrics(company.id, asOfDate)
      ]);

      setBalanceSheet(balanceSheetData);
      setIncomeStatement(incomeStatementData);
      setWorkingCapital(workingCapitalData);

    } catch (error) {
      console.error('Error loading financial statements:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'currency',
      currency: 'SEK',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat('sv-SE', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(value / 100);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900">Financial Statements</h2>
          <p className="text-gray-500">BAS 2025 compliant financial reporting</p>
        </div>
        <div className="flex items-center space-x-4">
          <select 
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="current-month">Current Month</option>
            <option value="current-quarter">Current Quarter</option>
            <option value="current-year">Current Year</option>
          </select>
          <Button variant="outline" size="sm" onClick={loadFinancialStatements}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {workingCapital && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Working Capital</CardTitle>
              <Calculator className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(workingCapital.working_capital)}</div>
              <p className="text-xs text-muted-foreground">
                Current assets - Current liabilities
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Ratio</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workingCapital.current_ratio.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground">
                Liquidity indicator
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">DSO</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round(workingCapital.days_sales_outstanding)} days</div>
              <p className="text-xs text-muted-foreground">
                Days Sales Outstanding
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">DPO</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Math.round(workingCapital.days_payable_outstanding)} days</div>
              <p className="text-xs text-muted-foreground">
                Days Payable Outstanding
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Financial Statements Tabs */}
      <Tabs defaultValue="balance-sheet" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="balance-sheet">Balance Sheet</TabsTrigger>
          <TabsTrigger value="income-statement">Income Statement</TabsTrigger>
        </TabsList>

        <TabsContent value="balance-sheet" className="space-y-6">
          {balanceSheet && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Assets */}
              <Card>
                <CardHeader>
                  <CardTitle>Assets (Tillgångar)</CardTitle>
                  <CardDescription>BAS 2025 compliant asset classification</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Current Assets</h4>
                      <div className="space-y-2">
                        {balanceSheet.assets.current_assets.map((item) => (
                          <div key={item.account_code} className="flex justify-between text-sm">
                            <span>{item.account_code} - {item.account_name}</span>
                            <span className="font-medium">{formatCurrency(item.balance)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Fixed Assets</h4>
                      <div className="space-y-2">
                        {balanceSheet.assets.fixed_assets.map((item) => (
                          <div key={item.account_code} className="flex justify-between text-sm">
                            <span>{item.account_code} - {item.account_name}</span>
                            <span className="font-medium">{formatCurrency(item.balance)}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="border-t pt-2">
                      <div className="flex justify-between font-bold">
                        <span>Total Assets</span>
                        <span>{formatCurrency(balanceSheet.assets.total_assets)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Liabilities & Equity */}
              <Card>
                <CardHeader>
                  <CardTitle>Liabilities & Equity (Skulder & Eget kapital)</CardTitle>
                  <CardDescription>BAS 2025 compliant liability and equity classification</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Current Liabilities</h4>
                      <div className="space-y-2">
                        {balanceSheet.liabilities.current_liabilities.map((item) => (
                          <div key={item.account_code} className="flex justify-between text-sm">
                            <span>{item.account_code} - {item.account_name}</span>
                            <span className="font-medium">{formatCurrency(item.balance)}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Equity</h4>
                      <div className="space-y-2">
                        {balanceSheet.equity.equity_items.map((item) => (
                          <div key={item.account_code} className="flex justify-between text-sm">
                            <span>{item.account_code} - {item.account_name}</span>
                            <span className="font-medium">{formatCurrency(item.balance)}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="border-t pt-2">
                      <div className="flex justify-between font-bold">
                        <span>Total Liabilities & Equity</span>
                        <span>{formatCurrency(balanceSheet.total_liabilities_and_equity)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="income-statement" className="space-y-6">
          {incomeStatement && (
            <Card>
              <CardHeader>
                <CardTitle>Income Statement (Resultaträkning)</CardTitle>
                <CardDescription>BAS 2025 compliant income statement for {selectedPeriod}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Revenue */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Revenue (Intäkter)</h4>
                    <div className="space-y-2">
                      {incomeStatement.revenue.operating_revenue.map((item) => (
                        <div key={item.account_code} className="flex justify-between text-sm">
                          <span>{item.account_code} - {item.account_name}</span>
                          <span className="font-medium">{formatCurrency(item.amount)}</span>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-between font-medium border-t pt-2 mt-2">
                      <span>Total Revenue</span>
                      <span>{formatCurrency(incomeStatement.revenue.total_revenue)}</span>
                    </div>
                  </div>

                  {/* Cost of Goods Sold */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Cost of Goods Sold</h4>
                    <div className="space-y-2">
                      {incomeStatement.expenses.cost_of_goods_sold.map((item) => (
                        <div key={item.account_code} className="flex justify-between text-sm">
                          <span>{item.account_code} - {item.account_name}</span>
                          <span className="font-medium text-red-600">-{formatCurrency(item.amount)}</span>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-between font-medium border-t pt-2 mt-2">
                      <span>Gross Profit</span>
                      <span className="text-green-600">{formatCurrency(incomeStatement.gross_profit)}</span>
                    </div>
                  </div>

                  {/* Operating Expenses */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Operating Expenses</h4>
                    <div className="space-y-2">
                      {[...incomeStatement.expenses.personnel_costs, ...incomeStatement.expenses.other_operating_expenses].map((item) => (
                        <div key={item.account_code} className="flex justify-between text-sm">
                          <span>{item.account_code} - {item.account_name}</span>
                          <span className="font-medium text-red-600">-{formatCurrency(item.amount)}</span>
                        </div>
                      ))}
                    </div>
                    <div className="flex justify-between font-medium border-t pt-2 mt-2">
                      <span>Operating Profit</span>
                      <span className="text-green-600">{formatCurrency(incomeStatement.operating_profit)}</span>
                    </div>
                  </div>

                  {/* Financial Items */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Financial Items</h4>
                    <div className="space-y-2">
                      {incomeStatement.expenses.financial_costs.map((item) => (
                        <div key={item.account_code} className="flex justify-between text-sm">
                          <span>{item.account_code} - {item.account_name}</span>
                          <span className={`font-medium ${item.amount > 0 ? 'text-red-600' : 'text-green-600'}`}>
                            {item.amount > 0 ? '-' : ''}{formatCurrency(Math.abs(item.amount))}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Net Profit */}
                  <div className="border-t-2 pt-4">
                    <div className="flex justify-between text-lg font-bold">
                      <span>Net Profit (Årets resultat)</span>
                      <span className={incomeStatement.net_profit >= 0 ? 'text-green-600' : 'text-red-600'}>
                        {formatCurrency(incomeStatement.net_profit)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancialStatements;
