import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/20 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-white hover:bg-primary/90 shadow-subtle hover:shadow-card",
        secondary: "bg-primary-light text-primary hover:bg-primary-light/80 shadow-subtle hover:shadow-card",
        outline: "border border-gray-300 bg-white hover:bg-gray-50 text-gray-700 shadow-subtle hover:shadow-card",
        ghost: "bg-transparent hover:bg-gray-50 text-gray-700 shadow-none hover:shadow-none",
        link: "bg-transparent text-primary hover:text-primary/80 p-0 h-auto shadow-none hover:shadow-none",
        accent: "bg-accent text-white hover:bg-accent/90 shadow-subtle hover:shadow-card",
        destructive: "bg-error text-white hover:bg-error/90 shadow-subtle hover:shadow-card",
      },
      size: {
        default: "h-10 py-2 px-4 text-sm",
        xs: "h-8 px-2.5 rounded-md text-xs",
        sm: "h-9 px-3 rounded-md text-xs",
        md: "h-10 px-4 rounded-md text-sm",
        lg: "h-12 px-6 rounded-md text-sm",
        xl: "h-14 px-8 rounded-md text-base",
        icon: "h-10 w-10 p-0",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
