# Arcim Design System

## Overview
This design system is inspired by treyd.io's clean, minimal aesthetic and is specifically designed for B2B fintech applications targeting Swedish businesses. The system emphasizes clarity, professionalism, and accessibility.

## Design Principles

### 1. Minimal & Clean
- Use plenty of white space
- Avoid unnecessary visual elements
- Focus on content hierarchy
- Keep interfaces uncluttered

### 2. Professional & Trustworthy
- Use conservative color palette
- Maintain consistent spacing
- Ensure high contrast for readability
- Professional typography choices

### 3. Accessible & Inclusive
- WCAG 2.1 AA compliance
- Clear focus states
- Sufficient color contrast
- Keyboard navigation support

## Color Palette

### Primary Colors
- **Black**: `#000000` - Primary actions, headings
- **White**: `#ffffff` - Backgrounds, light text

### Gray Scale
- **Gray 50**: `#f9fafb` - Light backgrounds
- **Gray 100**: `#f3f4f6` - Subtle backgrounds
- **Gray 200**: `#e5e7eb` - Borders, dividers
- **Gray 300**: `#d1d5db` - Input borders
- **Gray 400**: `#9ca3af` - Placeholder text
- **Gray 500**: `#6b7280` - Secondary text
- **Gray 600**: `#4b5563` - Body text
- **Gray 700**: `#374151` - Emphasized text
- **Gray 800**: `#1f2937` - Strong emphasis
- **Gray 900**: `#111827` - Headings, primary text

### Status Colors
- **Success**: `#10b981` - Success states
- **Warning**: `#f59e0b` - Warning states
- **Error**: `#ef4444` - Error states

## Typography

### Font Family
- **Primary**: Inter (Google Fonts)
- **Fallback**: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif

### Type Scale

#### Display Sizes (Hero sections)
- **Display XL**: 72px / 1.1 line height / 600 weight
- **Display LG**: 60px / 1.1 line height / 600 weight
- **Display MD**: 48px / 1.2 line height / 600 weight
- **Display SM**: 36px / 1.3 line height / 600 weight

#### Headings
- **Heading XL**: 32px / 1.3 line height / 600 weight
- **Heading LG**: 24px / 1.4 line height / 600 weight
- **Heading MD**: 20px / 1.4 line height / 600 weight
- **Heading SM**: 18px / 1.5 line height / 600 weight

#### Body Text
- **Body XL**: 20px / 1.6 line height / 400 weight
- **Body LG**: 18px / 1.6 line height / 400 weight
- **Body MD**: 16px / 1.6 line height / 400 weight
- **Body SM**: 14px / 1.5 line height / 400 weight
- **Body XS**: 12px / 1.5 line height / 400 weight

#### Labels
- **Label LG**: 14px / 1.25 line height / 500 weight
- **Label MD**: 12px / 1.25 line height / 500 weight
- **Label SM**: 11px / 1.25 line height / 500 weight

## Spacing System

### Base Unit: 4px

#### Spacing Scale
- **0.5**: 2px
- **1**: 4px
- **1.5**: 6px
- **2**: 8px
- **2.5**: 10px
- **3**: 12px
- **4**: 16px
- **5**: 20px
- **6**: 24px
- **8**: 32px
- **10**: 40px
- **12**: 48px
- **16**: 64px
- **20**: 80px
- **24**: 96px

#### Section Spacing
- **Section**: 80px (5rem)
- **Section SM**: 48px (3rem)
- **Section XS**: 32px (2rem)

## Components

### Buttons

#### Primary Button
```css
.btn-primary {
  background: #111827;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 200ms;
}
```

#### Secondary Button
```css
.btn-secondary {
  background: white;
  color: #111827;
  border: 1px solid #d1d5db;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 200ms;
}
```

### Cards
```css
.card-clean {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 200ms;
}
```

### Forms
```css
.form-input {
  border: 1px solid #d1d5db;
  border-radius: 6px;
  padding: 8px 12px;
  font-size: 16px;
  transition: border-color 200ms;
}

.form-input:focus {
  border-color: #111827;
  outline: none;
  box-shadow: 0 0 0 1px #111827;
}
```

## Layout Guidelines

### Container Widths
- **Max Width**: 1280px (7xl)
- **Padding**: 24px on mobile, 32px on desktop

### Grid System
- Use CSS Grid or Flexbox
- 12-column grid for complex layouts
- Consistent gutters: 24px

### Breakpoints
- **SM**: 640px
- **MD**: 768px
- **LG**: 1024px
- **XL**: 1280px
- **2XL**: 1400px

## Shadows

### Elevation System
- **Button**: `0 1px 2px rgba(0, 0, 0, 0.05)`
- **Card**: `0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06)`
- **Card Hover**: `0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06)`

## Border Radius
- **SM**: 2px
- **Default**: 6px
- **MD**: 8px
- **LG**: 12px
- **XL**: 16px

## Usage Guidelines

### Do's
- Use consistent spacing from the scale
- Maintain proper contrast ratios
- Use semantic HTML elements
- Follow the typography hierarchy
- Keep interfaces clean and minimal

### Don'ts
- Don't use arbitrary spacing values
- Don't mix different design patterns
- Don't use too many colors
- Don't ignore accessibility guidelines
- Don't overcomplicate interfaces

## Implementation

### Tailwind Classes
All design tokens are available as Tailwind CSS classes:
- Colors: `text-gray-900`, `bg-white`
- Typography: `text-display-lg`, `text-body-md`
- Spacing: `p-6`, `m-4`, `space-y-8`
- Shadows: `shadow-card`, `shadow-button`

### CSS Custom Properties
Core values are also available as CSS custom properties in the `:root` selector for maximum flexibility.
