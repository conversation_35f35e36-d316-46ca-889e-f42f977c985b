import React from 'react';
import { LucideIcon } from 'lucide-react';

interface ChartContainerProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  children: React.ReactNode;
  className?: string;
  headerActions?: React.ReactNode;
  height?: string;
}

const ChartContainer: React.FC<ChartContainerProps> = ({
  title,
  description,
  icon: Icon,
  children,
  className = '',
  headerActions,
  height = 'h-80'
}) => {
  return (
    <div className={`card-clean ${className}`}>
      {/* Chart Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {Icon && (
              <div className="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center">
                <Icon className="h-4 w-4 text-gray-600" />
              </div>
            )}
            <div>
              <h4 className="text-heading-md">{title}</h4>
              {description && (
                <p className="text-body-sm text-gray-600 mt-1">{description}</p>
              )}
            </div>
          </div>
          {headerActions && (
            <div className="flex items-center space-x-2">
              {headerActions}
            </div>
          )}
        </div>
      </div>

      {/* Chart Content */}
      <div className={`p-6 ${height}`}>
        {children}
      </div>
    </div>
  );
};

export default ChartContainer;
