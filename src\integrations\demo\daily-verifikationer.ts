/**
 * Daily Verifikationer Generator for Nordström GreenTech AB
 * Generates realistic daily accounting entries following BAS 2025 standards
 * 300-600 entries per month = 15-30 entries per working day
 */

import { supabase } from '@/integrations/supabase/client';
import { bas2025Accounts } from './bas-2025-accounts';

export interface DailyVerifikation {
  voucher_series: string;
  voucher_number: number;
  transaction_date: string;
  description: string;
  total_amount: number;
  currency_code: string;
  exchange_rate: number;
  rows: VerifikationRow[];
  category: 'supplier' | 'customer' | 'payroll' | 'bank' | 'inventory' | 'tax' | 'fx' | 'operational';
}

export interface VerifikationRow {
  account_code: string;
  account_name: string;
  debit_amount: number;
  credit_amount: number;
  currency_code: string;
  exchange_rate: number;
  description: string;
  cost_center?: string;
  project_code?: string;
}

// Exchange rates for realistic FX calculations
const EXCHANGE_RATES = {
  'SEK': 1.0,
  'EUR': 11.5,
  'USD': 10.5,
  'NOK': 0.95,
  'DKK': 1.54
};

// Voucher series mapping
const VOUCHER_SERIES = {
  supplier: 'LF',      // Leverantörsfaktura
  customer: 'KF',      // Kundfaktura
  payroll: 'LÖ',       // Löner
  bank: 'BK',          // Bank
  inventory: 'LG',     // Lager
  tax: 'SK',           // Skatt
  fx: 'VK',            // Valutakurs
  operational: 'DV'    // Diverse
};

/**
 * Generate daily verifikationer for a specific date
 */
export function generateDailyVerifikationer(date: Date, companyId: string): DailyVerifikation[] {
  const verifikationer: DailyVerifikation[] = [];
  const dateStr = date.toISOString().split('T')[0];
  const dayOfWeek = date.getDay();
  const dayOfMonth = date.getDate();

  // Skip weekends for most transactions
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return verifikationer;
  }

  let voucherCounter = {
    LF: Math.floor(Math.random() * 100) + 1000,
    KF: Math.floor(Math.random() * 100) + 2000,
    LÖ: Math.floor(Math.random() * 10) + 3000,
    BK: Math.floor(Math.random() * 50) + 4000,
    LG: Math.floor(Math.random() * 20) + 5000,
    SK: Math.floor(Math.random() * 10) + 6000,
    VK: Math.floor(Math.random() * 5) + 7000,
    DV: Math.floor(Math.random() * 30) + 8000
  };

  // 1. SUPPLIER INVOICES (2-5 per day)
  const supplierCount = Math.floor(Math.random() * 4) + 2;
  for (let i = 0; i < supplierCount; i++) {
    verifikationer.push(generateSupplierInvoice(dateStr, voucherCounter.LF++));
  }

  // 2. CUSTOMER INVOICES (1-3 per day)
  const customerCount = Math.floor(Math.random() * 3) + 1;
  for (let i = 0; i < customerCount; i++) {
    verifikationer.push(generateCustomerInvoice(dateStr, voucherCounter.KF++));
  }

  // 3. BANK TRANSACTIONS (3-8 per day)
  const bankCount = Math.floor(Math.random() * 6) + 3;
  for (let i = 0; i < bankCount; i++) {
    verifikationer.push(generateBankTransaction(dateStr, voucherCounter.BK++));
  }

  // 4. INVENTORY ADJUSTMENTS (1-3 per day)
  const inventoryCount = Math.floor(Math.random() * 3) + 1;
  for (let i = 0; i < inventoryCount; i++) {
    verifikationer.push(generateInventoryAdjustment(dateStr, voucherCounter.LG++));
  }

  // 5. OPERATIONAL EXPENSES (2-5 per day)
  const operationalCount = Math.floor(Math.random() * 4) + 2;
  for (let i = 0; i < operationalCount; i++) {
    verifikationer.push(generateOperationalExpense(dateStr, voucherCounter.DV++));
  }

  // 6. PAYROLL (monthly on 25th)
  if (dayOfMonth === 25) {
    verifikationer.push(generatePayrollEntry(dateStr, voucherCounter.LÖ++));
  }

  // 7. TAX ENTRIES (monthly on last working day)
  if (isLastWorkingDay(date)) {
    verifikationer.push(generateTaxEntry(dateStr, voucherCounter.SK++));
  }

  // 8. FX ADJUSTMENTS (random, 1-2 per week)
  if (Math.random() < 0.3) {
    verifikationer.push(generateFXAdjustment(dateStr, voucherCounter.VK++));
  }

  return verifikationer;
}

/**
 * Generate supplier invoice verifikation
 */
function generateSupplierInvoice(date: string, voucherNumber: number): DailyVerifikation {
  const suppliers = [
    { name: 'ThyssenKrupp Steel Europe AG', currency: 'EUR', account: '4011', amount: [50000, 200000] },
    { name: 'Sinocomp Materials Ltd', currency: 'USD', account: '4012', amount: [30000, 150000] },
    { name: 'Sandvik AB', currency: 'SEK', account: '4010', amount: [25000, 100000] },
    { name: 'Scania Logistics AB', currency: 'SEK', account: '6250', amount: [5000, 25000] },
    { name: 'ABB Motors and Drives', currency: 'SEK', account: '4010', amount: [40000, 180000] }
  ];

  const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];
  const baseAmount = Math.floor(Math.random() * (supplier.amount[1] - supplier.amount[0])) + supplier.amount[0];
  const vatRate = supplier.currency === 'SEK' ? 0.25 : 0; // 25% VAT for Swedish suppliers
  const vatAmount = baseAmount * vatRate;
  const totalAmount = baseAmount + vatAmount;

  const rows: VerifikationRow[] = [
    {
      account_code: supplier.account,
      account_name: getAccountName(supplier.account),
      debit_amount: baseAmount,
      credit_amount: 0,
      currency_code: supplier.currency,
      exchange_rate: EXCHANGE_RATES[supplier.currency as keyof typeof EXCHANGE_RATES],
      description: `Inköp från ${supplier.name}`
    }
  ];

  if (vatAmount > 0) {
    rows.push({
      account_code: '2450',
      account_name: 'Ingående moms',
      debit_amount: vatAmount,
      credit_amount: 0,
      currency_code: supplier.currency,
      exchange_rate: EXCHANGE_RATES[supplier.currency as keyof typeof EXCHANGE_RATES],
      description: 'Ingående moms 25%'
    });
  }

  rows.push({
    account_code: supplier.currency === 'SEK' ? '2010' :
                  supplier.currency === 'EUR' ? '2013' : '2014',
    account_name: `Leverantörsskulder ${supplier.currency}`,
    debit_amount: 0,
    credit_amount: totalAmount,
    currency_code: supplier.currency,
    exchange_rate: EXCHANGE_RATES[supplier.currency as keyof typeof EXCHANGE_RATES],
    description: supplier.name
  });

  return {
    voucher_series: 'LF',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Leverantörsfaktura - ${supplier.name}`,
    total_amount: totalAmount,
    currency_code: supplier.currency,
    exchange_rate: EXCHANGE_RATES[supplier.currency as keyof typeof EXCHANGE_RATES],
    rows,
    category: 'supplier'
  };
}

/**
 * Generate customer invoice verifikation
 */
function generateCustomerInvoice(date: string, voucherNumber: number): DailyVerifikation {
  const customers = [
    { name: 'Norsk Vindkraft AS', currency: 'NOK', amount: [500000, 2000000] },
    { name: 'Ørsted Wind Power A/S', currency: 'DKK', amount: [400000, 1800000] },
    { name: 'Vattenfall Wind Power GmbH', currency: 'EUR', amount: [600000, 2500000] },
    { name: 'American Wind Solutions Inc', currency: 'USD', amount: [800000, 3000000] },
    { name: 'Svensk Vindenergi AB', currency: 'SEK', amount: [300000, 1500000] }
  ];

  const customer = customers[Math.floor(Math.random() * customers.length)];
  const baseAmount = Math.floor(Math.random() * (customer.amount[1] - customer.amount[0])) + customer.amount[0];
  const vatRate = customer.currency === 'SEK' ? 0.25 : 0; // 25% VAT for Swedish customers
  const vatAmount = baseAmount * vatRate;
  const totalAmount = baseAmount + vatAmount;

  const accountCode = customer.currency === 'SEK' ? '3010' :
                     ['NOK', 'DKK'].includes(customer.currency) ? '3011' : '3012';

  const rows: VerifikationRow[] = [
    {
      account_code: customer.currency === 'SEK' ? '1510' :
                    customer.currency === 'EUR' ? '1513' :
                    customer.currency === 'USD' ? '1514' : '1515',
      account_name: `Kundfordringar ${customer.currency}`,
      debit_amount: totalAmount,
      credit_amount: 0,
      currency_code: customer.currency,
      exchange_rate: EXCHANGE_RATES[customer.currency as keyof typeof EXCHANGE_RATES],
      description: customer.name
    },
    {
      account_code: accountCode,
      account_name: getAccountName(accountCode),
      debit_amount: 0,
      credit_amount: baseAmount,
      currency_code: customer.currency,
      exchange_rate: EXCHANGE_RATES[customer.currency as keyof typeof EXCHANGE_RATES],
      description: 'Försäljning turbinkomponenter'
    }
  ];

  if (vatAmount > 0) {
    rows.push({
      account_code: '2440',
      account_name: 'Utgående moms',
      debit_amount: 0,
      credit_amount: vatAmount,
      currency_code: customer.currency,
      exchange_rate: EXCHANGE_RATES[customer.currency as keyof typeof EXCHANGE_RATES],
      description: 'Utgående moms 25%'
    });
  }

  return {
    voucher_series: 'KF',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Kundfaktura - ${customer.name}`,
    total_amount: totalAmount,
    currency_code: customer.currency,
    exchange_rate: EXCHANGE_RATES[customer.currency as keyof typeof EXCHANGE_RATES],
    rows,
    category: 'customer'
  };
}

/**
 * Generate bank transaction verifikation
 */
function generateBankTransaction(date: string, voucherNumber: number): DailyVerifikation {
  const transactions = [
    { type: 'payment', description: 'Betalning leverantörsfaktura', account: '2010', amount: [10000, 100000] },
    { type: 'receipt', description: 'Kundbetalning', account: '1510', amount: [50000, 500000] },
    { type: 'fee', description: 'Bankavgift', account: '6540', amount: [100, 1000] },
    { type: 'interest', description: 'Ränteintäkt', account: '8310', amount: [500, 5000] },
    { type: 'fx_payment', description: 'Valutabetalning', account: '2013', amount: [20000, 200000] }
  ];

  const transaction = transactions[Math.floor(Math.random() * transactions.length)];
  const amount = Math.floor(Math.random() * (transaction.amount[1] - transaction.amount[0])) + transaction.amount[0];
  const currency = transaction.type === 'fx_payment' ? 'EUR' : 'SEK';

  const rows: VerifikationRow[] = [];

  if (transaction.type === 'payment' || transaction.type === 'fee') {
    rows.push(
      {
        account_code: transaction.account,
        account_name: getAccountName(transaction.account),
        debit_amount: amount,
        credit_amount: 0,
        currency_code: currency,
        exchange_rate: EXCHANGE_RATES[currency as keyof typeof EXCHANGE_RATES],
        description: transaction.description
      },
      {
        account_code: currency === 'SEK' ? '1930' : '1931',
        account_name: currency === 'SEK' ? 'Handelsbanken Företagskonto' : 'SEB Valutakonto EUR',
        debit_amount: 0,
        credit_amount: amount,
        currency_code: currency,
        exchange_rate: EXCHANGE_RATES[currency as keyof typeof EXCHANGE_RATES],
        description: transaction.description
      }
    );
  } else {
    rows.push(
      {
        account_code: currency === 'SEK' ? '1930' : '1931',
        account_name: currency === 'SEK' ? 'Handelsbanken Företagskonto' : 'SEB Valutakonto EUR',
        debit_amount: amount,
        credit_amount: 0,
        currency_code: currency,
        exchange_rate: EXCHANGE_RATES[currency as keyof typeof EXCHANGE_RATES],
        description: transaction.description
      },
      {
        account_code: transaction.account,
        account_name: getAccountName(transaction.account),
        debit_amount: 0,
        credit_amount: amount,
        currency_code: currency,
        exchange_rate: EXCHANGE_RATES[currency as keyof typeof EXCHANGE_RATES],
        description: transaction.description
      }
    );
  }

  return {
    voucher_series: 'BK',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: transaction.description,
    total_amount: amount,
    currency_code: currency,
    exchange_rate: EXCHANGE_RATES[currency as keyof typeof EXCHANGE_RATES],
    rows,
    category: 'bank'
  };
}

/**
 * Generate inventory adjustment verifikation
 */
function generateInventoryAdjustment(date: string, voucherNumber: number): DailyVerifikation {
  const adjustments = [
    { type: 'raw_material', account: '1410', description: 'Lagerinventering råmaterial' },
    { type: 'wip', account: '1420', description: 'Värdering produkter i arbete' },
    { type: 'finished_goods', account: '1430', description: 'Färdiga varor till lager' }
  ];

  const adjustment = adjustments[Math.floor(Math.random() * adjustments.length)];
  const amount = Math.floor(Math.random() * 90000) + 10000; // 10k-100k SEK
  const isIncrease = Math.random() > 0.3; // 70% increases, 30% decreases

  const rows: VerifikationRow[] = [
    {
      account_code: adjustment.account,
      account_name: getAccountName(adjustment.account),
      debit_amount: isIncrease ? amount : 0,
      credit_amount: isIncrease ? 0 : amount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: adjustment.description
    },
    {
      account_code: '4010', // Cost of goods sold
      account_name: 'Inköp av råmaterial och förnödenheter',
      debit_amount: isIncrease ? 0 : amount,
      credit_amount: isIncrease ? amount : 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: adjustment.description
    }
  ];

  return {
    voucher_series: 'LG',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: adjustment.description,
    total_amount: amount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    rows,
    category: 'inventory'
  };
}

/**
 * Generate operational expense verifikation
 */
function generateOperationalExpense(date: string, voucherNumber: number): DailyVerifikation {
  const expenses = [
    { account: '6110', description: 'Kontorsmaterial', amount: [500, 5000] },
    { account: '6250', description: 'Transportkostnader', amount: [2000, 15000] },
    { account: '6540', description: 'IT-tjänster', amount: [1000, 10000] }
  ];

  const expense = expenses[Math.floor(Math.random() * expenses.length)];
  const baseAmount = Math.floor(Math.random() * (expense.amount[1] - expense.amount[0])) + expense.amount[0];
  const vatAmount = baseAmount * 0.25; // 25% VAT
  const totalAmount = baseAmount + vatAmount;

  const rows: VerifikationRow[] = [
    {
      account_code: expense.account,
      account_name: getAccountName(expense.account),
      debit_amount: baseAmount,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: expense.description
    },
    {
      account_code: '2450',
      account_name: 'Ingående moms',
      debit_amount: vatAmount,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Ingående moms 25%'
    },
    {
      account_code: '2010',
      account_name: 'Leverantörsskulder',
      debit_amount: 0,
      credit_amount: totalAmount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: expense.description
    }
  ];

  return {
    voucher_series: 'DV',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: expense.description,
    total_amount: totalAmount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    rows,
    category: 'operational'
  };
}

/**
 * Generate payroll entry (monthly)
 */
function generatePayrollEntry(date: string, voucherNumber: number): DailyVerifikation {
  const grossSalaries = 2800000; // 2.8M SEK for 50 employees
  const socialContributions = grossSalaries * 0.31; // 31% employer contributions
  const preliminaryTax = grossSalaries * 0.30; // 30% preliminary tax
  const netSalaries = grossSalaries - preliminaryTax;

  const rows: VerifikationRow[] = [
    {
      account_code: '5010',
      account_name: 'Löner till arbetare',
      debit_amount: grossSalaries * 0.7, // 70% workers
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Löner produktionspersonal'
    },
    {
      account_code: '5020',
      account_name: 'Löner till tjänstemän',
      debit_amount: grossSalaries * 0.3, // 30% office staff
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Löner kontorspersonal'
    },
    {
      account_code: '5410',
      account_name: 'Sociala avgifter',
      debit_amount: socialContributions,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Arbetsgivaravgifter'
    },
    {
      account_code: '2710',
      account_name: 'Personalskatt',
      debit_amount: 0,
      credit_amount: preliminaryTax + socialContributions,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Skatter och avgifter'
    },
    {
      account_code: '1930',
      account_name: 'Handelsbanken Företagskonto',
      debit_amount: 0,
      credit_amount: netSalaries,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Nettolöner'
    }
  ];

  return {
    voucher_series: 'LÖ',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Löner ${new Date(date).toLocaleDateString('sv-SE', { month: 'long', year: 'numeric' })}`,
    total_amount: grossSalaries + socialContributions,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    rows,
    category: 'payroll'
  };
}

/**
 * Generate tax entry (monthly)
 */
function generateTaxEntry(date: string, voucherNumber: number): DailyVerifikation {
  const vatPayable = Math.floor(Math.random() * 400000) + 100000; // 100k-500k SEK

  const rows: VerifikationRow[] = [
    {
      account_code: '2440',
      account_name: 'Utgående moms',
      debit_amount: vatPayable,
      credit_amount: 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Momsbetalning till Skatteverket'
    },
    {
      account_code: '1930',
      account_name: 'Handelsbanken Företagskonto',
      debit_amount: 0,
      credit_amount: vatPayable,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: 'Momsbetalning'
    }
  ];

  return {
    voucher_series: 'SK',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: 'Momsbetalning',
    total_amount: vatPayable,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    rows,
    category: 'tax'
  };
}

/**
 * Generate FX adjustment verifikation
 */
function generateFXAdjustment(date: string, voucherNumber: number): DailyVerifikation {
  const currencies = ['EUR', 'USD', 'NOK'];
  const currency = currencies[Math.floor(Math.random() * currencies.length)];
  const amount = Math.floor(Math.random() * 40000) + 5000; // 5k-45k SEK
  const isGain = Math.random() > 0.5;

  const rows: VerifikationRow[] = [
    {
      account_code: isGain ? '3740' : '7740',
      account_name: isGain ? 'Valutakursvinster' : 'Valutakursförluster',
      debit_amount: isGain ? 0 : amount,
      credit_amount: isGain ? amount : 0,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: `Valutakursjustering ${currency}`
    },
    {
      account_code: currency === 'EUR' ? '1931' : currency === 'USD' ? '1932' : '1933',
      account_name: `Valutakonto ${currency}`,
      debit_amount: isGain ? amount : 0,
      credit_amount: isGain ? 0 : amount,
      currency_code: 'SEK',
      exchange_rate: 1.0,
      description: `Omvärdering ${currency}`
    }
  ];

  return {
    voucher_series: 'VK',
    voucher_number: voucherNumber,
    transaction_date: date,
    description: `Valutakursjustering ${currency}`,
    total_amount: amount,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    rows,
    category: 'fx'
  };
}

/**
 * Helper function to get account name
 */
function getAccountName(accountCode: string): string {
  const account = bas2025Accounts.find(acc => acc.account_code === accountCode);
  return account?.account_name || 'Okänt konto';
}

/**
 * Check if date is last working day of month
 */
function isLastWorkingDay(date: Date): boolean {
  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  while (lastDay.getDay() === 0 || lastDay.getDay() === 6) {
    lastDay.setDate(lastDay.getDate() - 1);
  }
  return date.getDate() === lastDay.getDate();
}
