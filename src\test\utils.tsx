/**
 * Test Utilities
 * Helper functions and components for testing
 */

import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { TooltipProvider } from '@/components/ui/tooltip';

// Create a test query client
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      gcTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
});

// Test wrapper component
interface TestWrapperProps {
  children: React.ReactNode;
  queryClient?: QueryClient;
}

const TestWrapper: React.FC<TestWrapperProps> = ({ 
  children, 
  queryClient = createTestQueryClient() 
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <TooltipProvider>
          {children}
        </TooltipProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
}

export const renderWithProviders = (
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { queryClient, ...renderOptions } = options;

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper queryClient={queryClient}>
      {children}
    </TestWrapper>
  );

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock data generators
export const mockCompany = (overrides = {}) => ({
  id: 'test-company-id',
  name: 'Test Company AB',
  organization_number: '556123-4567',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockVoucher = (overrides = {}) => ({
  id: 'test-voucher-id',
  company_id: 'test-company-id',
  voucher_number: 'V001',
  voucher_date: '2024-01-01',
  description: 'Test voucher',
  total_amount: 1000,
  created_at: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockFinanceApplication = (overrides = {}) => ({
  id: 'test-application-id',
  company_name: 'Test Company AB',
  organization_number: '556123-4567',
  loan_amount: 1000000,
  loan_purpose: 'Working capital',
  status: 'draft',
  created_at: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const mockMetricData = (overrides = {}) => ({
  title: 'Test Metric',
  value: 1000,
  format: 'currency' as const,
  change: {
    value: 5.2,
    period: 'vs last month',
    type: 'increase' as const,
  },
  ...overrides,
});

export const mockChartData = (length = 5) => 
  Array.from({ length }, (_, i) => ({
    date: `2024-01-${String(i + 1).padStart(2, '0')}`,
    value: Math.floor(Math.random() * 1000) + 500,
    category: `Category ${i + 1}`,
  }));

// Test helpers
export const waitForLoadingToFinish = () => 
  new Promise(resolve => setTimeout(resolve, 0));

export const mockApiResponse = <T>(data: T, error?: any) => ({
  data: error ? undefined : data,
  error: error || null,
  isLoading: false,
  isError: !!error,
  refetch: vi.fn(),
});

export const mockMutationResponse = <T>(data?: T, error?: any) => ({
  mutate: vi.fn(),
  mutateAsync: vi.fn(() => error ? Promise.reject(error) : Promise.resolve(data)),
  isLoading: false,
  isError: !!error,
  error: error || null,
  data: error ? undefined : data,
});

// Custom matchers
export const expectElementToBeVisible = (element: HTMLElement) => {
  expect(element).toBeInTheDocument();
  expect(element).toBeVisible();
};

export const expectElementToHaveText = (element: HTMLElement, text: string) => {
  expect(element).toBeInTheDocument();
  expect(element).toHaveTextContent(text);
};

// Form testing helpers
export const fillFormField = async (
  getByLabelText: (text: string) => HTMLElement,
  userEvent: any,
  label: string,
  value: string
) => {
  const field = getByLabelText(label);
  await userEvent.clear(field);
  await userEvent.type(field, value);
};

export const submitForm = async (
  getByRole: (role: string, options?: any) => HTMLElement,
  userEvent: any
) => {
  const submitButton = getByRole('button', { name: /submit|save|send/i });
  await userEvent.click(submitButton);
};

// Error boundary for testing
export class TestErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Test Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div data-testid="error-boundary">Something went wrong.</div>;
    }

    return this.props.children;
  }
}

// Re-export everything from testing library
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
