/**
 * API Hooks
 * Standardized data fetching hooks using React Query
 */

import React from 'react';
import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { handleError, withRetry, safeAsync, AppError } from '@/lib/error-handling';
import { CACHE_KEYS, API } from '@/lib/constants';

// Generic API response type
export interface ApiResponse<T> {
  data?: T;
  error?: AppError;
  loading: boolean;
}

// Query options with defaults
export interface ApiQueryOptions<T> extends Omit<UseQueryOptions<T, AppError>, 'queryKey' | 'queryFn'> {
  retryAttempts?: number;
  retryDelay?: number;
}

// Mutation options with defaults
export interface ApiMutationOptions<TData, TVariables> extends Omit<UseMutationOptions<TData, AppError, TVariables>, 'mutationFn'> {
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * Generic hook for fetching data with standardized error handling
 */
export function useApiQuery<T>(
  queryKey: string[],
  queryFn: () => Promise<T>,
  options: ApiQueryOptions<T> = {}
) {
  const {
    retryAttempts = API.RETRY_ATTEMPTS,
    retryDelay = API.RETRY_DELAY,
    ...queryOptions
  } = options;

  return useQuery<T, AppError>({
    queryKey,
    queryFn: () => withRetry(queryFn, retryAttempts, retryDelay),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on certain error types
      if (error.code === 'UNAUTHORIZED' || error.code === 'FORBIDDEN') {
        return false;
      }
      return failureCount < 3;
    },
    ...queryOptions,
  });
}

/**
 * Generic hook for mutations with standardized error handling
 */
export function useApiMutation<TData, TVariables>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options: ApiMutationOptions<TData, TVariables> = {}
) {
  const {
    retryAttempts = 1, // Usually don't retry mutations
    retryDelay = API.RETRY_DELAY,
    ...mutationOptions
  } = options;

  return useMutation<TData, AppError, TVariables>({
    mutationFn: (variables) => withRetry(() => mutationFn(variables), retryAttempts, retryDelay),
    ...mutationOptions,
  });
}

/**
 * Hook for fetching companies data
 */
export function useCompanies(options: ApiQueryOptions<any[]> = {}) {
  return useApiQuery(
    [CACHE_KEYS.COMPANIES],
    async () => {
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw handleError(error);
      }

      return data || [];
    },
    options
  );
}

/**
 * Hook for fetching vouchers data
 */
export function useVouchers(companyId?: string, options: ApiQueryOptions<any[]> = {}) {
  return useApiQuery(
    [CACHE_KEYS.VOUCHERS, companyId],
    async () => {
      let query = supabase
        .from('vouchers')
        .select('*')
        .order('voucher_date', { ascending: false });

      if (companyId) {
        query = query.eq('company_id', companyId);
      }

      const { data, error } = await query;

      if (error) {
        throw handleError(error);
      }

      return data || [];
    },
    {
      enabled: !!companyId,
      ...options,
    }
  );
}

/**
 * Hook for fetching chart of accounts
 */
export function useChartOfAccounts(companyId?: string, options: ApiQueryOptions<any[]> = {}) {
  return useApiQuery(
    [CACHE_KEYS.CHART_OF_ACCOUNTS, companyId],
    async () => {
      let query = supabase
        .from('chart_of_accounts')
        .select('*')
        .order('account_code', { ascending: true });

      if (companyId) {
        query = query.eq('company_id', companyId);
      }

      const { data, error } = await query;

      if (error) {
        throw handleError(error);
      }

      return data || [];
    },
    {
      enabled: !!companyId,
      ...options,
    }
  );
}

/**
 * Hook for fetching exchange rates
 */
export function useExchangeRates(options: ApiQueryOptions<any[]> = {}) {
  return useApiQuery(
    [CACHE_KEYS.EXCHANGE_RATES],
    async () => {
      const { data, error } = await supabase
        .from('exchange_rates')
        .select('*')
        .order('date', { ascending: false })
        .limit(100);

      if (error) {
        throw handleError(error);
      }

      return data || [];
    },
    {
      staleTime: 15 * 60 * 1000, // 15 minutes for exchange rates
      ...options,
    }
  );
}

/**
 * Hook for fetching finance applications
 */
export function useFinanceApplications(options: ApiQueryOptions<any[]> = {}) {
  return useApiQuery(
    [CACHE_KEYS.FINANCE_APPLICATIONS],
    async () => {
      const { data, error } = await supabase
        .from('finance_applications')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        throw handleError(error);
      }

      return data || [];
    },
    options
  );
}

/**
 * Hook for creating a finance application
 */
export function useCreateFinanceApplication() {
  const queryClient = useQueryClient();

  return useApiMutation(
    async (applicationData: any) => {
      const { data, error } = await supabase
        .from('finance_applications')
        .insert([applicationData])
        .select()
        .single();

      if (error) {
        throw handleError(error);
      }

      return data;
    },
    {
      onSuccess: () => {
        // Invalidate and refetch finance applications
        queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.FINANCE_APPLICATIONS] });
      },
    }
  );
}

/**
 * Hook for updating a finance application
 */
export function useUpdateFinanceApplication() {
  const queryClient = useQueryClient();

  return useApiMutation(
    async ({ id, updates }: { id: string; updates: any }) => {
      const { data, error } = await supabase
        .from('finance_applications')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw handleError(error);
      }

      return data;
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [CACHE_KEYS.FINANCE_APPLICATIONS] });
      },
    }
  );
}

/**
 * Hook for safe async operations with loading states
 */
export function useSafeAsync<T>(
  operation: () => Promise<T>,
  dependencies: any[] = []
): ApiResponse<T> {
  const [state, setState] = React.useState<{
    data?: T;
    error?: AppError;
    loading: boolean;
  }>({
    loading: false,
  });

  React.useEffect(() => {
    let cancelled = false;

    const runOperation = async () => {
      setState({ loading: true });

      const result = await safeAsync(operation);

      if (!cancelled) {
        setState({
          data: result.data,
          error: result.error,
          loading: false,
        });
      }
    };

    runOperation();

    return () => {
      cancelled = true;
    };
  }, dependencies);

  return state;
}

/**
 * Hook for invalidating specific cache keys
 */
export function useInvalidateCache() {
  const queryClient = useQueryClient();

  return React.useCallback((cacheKeys: string[]) => {
    cacheKeys.forEach(key => {
      queryClient.invalidateQueries({ queryKey: [key] });
    });
  }, [queryClient]);
}
