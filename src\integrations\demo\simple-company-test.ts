/**
 * Simple Company Creation Test
 * Test basic company creation to identify schema issues
 */

import { supabase } from '@/integrations/supabase/client';

/**
 * Test basic company creation with minimal data
 */
export async function testBasicCompanyCreation(): Promise<{
  success: boolean;
  error?: string;
  companyId?: string;
}> {
  try {
    console.log('Testing basic company creation...');

    // First, let's try to see what columns exist in the companies table
    const { data: existingCompanies, error: selectError } = await supabase
      .from('companies')
      .select('*')
      .limit(1);

    if (selectError) {
      console.error('Error selecting from companies table:', selectError);
      return {
        success: false,
        error: `Cannot access companies table: ${selectError.message}`
      };
    }

    console.log('Companies table is accessible. Sample data:', existingCompanies);

    // Try creating a company with only the most basic required fields
    const basicCompanyData = {
      organization_number: '556123-4567',
      company_name: 'Nordström GreenTech AB'
    };

    console.log('Attempting to create company with basic data:', basicCompanyData);

    const { data: company, error: companyError } = await supabase
      .from('companies')
      .insert(basicCompanyData)
      .select()
      .single();

    if (companyError) {
      console.error('Basic company creation failed:', companyError);
      return {
        success: false,
        error: companyError.message
      };
    }

    console.log('Basic company created successfully:', company);

    return {
      success: true,
      companyId: company.id
    };

  } catch (error) {
    console.error('Unexpected error in testBasicCompanyCreation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test company creation with all fields
 */
export async function testFullCompanyCreation(): Promise<{
  success: boolean;
  error?: string;
  companyId?: string;
}> {
  try {
    console.log('Testing full company creation...');

    const fullCompanyData = {
      organization_number: '556123-4568', // Different number to avoid conflicts
      company_name: 'Nordström GreenTech AB Test',
      industry: 'Wind Turbine Components Manufacturing',
      base_currency: 'SEK',
      annual_revenue: *********,
      employee_count: 50,
      sync_status: 'completed'
    };

    console.log('Attempting to create company with full data:', fullCompanyData);

    const { data: company, error: companyError } = await supabase
      .from('companies')
      .insert(fullCompanyData)
      .select()
      .single();

    if (companyError) {
      console.error('Full company creation failed:', companyError);
      return {
        success: false,
        error: companyError.message
      };
    }

    console.log('Full company created successfully:', company);

    return {
      success: true,
      companyId: company.id
    };

  } catch (error) {
    console.error('Unexpected error in testFullCompanyCreation:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check what columns exist in the companies table
 */
export async function checkCompaniesTableSchema(): Promise<{
  success: boolean;
  columns?: string[];
  error?: string;
}> {
  try {
    console.log('Checking companies table schema...');

    // Try to get the table structure by selecting with a limit of 0
    const { data, error } = await supabase
      .from('companies')
      .select('*')
      .limit(0);

    if (error) {
      console.error('Error checking table schema:', error);
      return {
        success: false,
        error: error.message
      };
    }

    // Get column names from the response metadata
    // This is a bit tricky with Supabase, so let's try a different approach
    
    // Try to insert a test record and see what fields are accepted
    const testData = {
      organization_number: 'TEST-SCHEMA',
      company_name: 'Schema Test Company',
      industry: 'Test',
      base_currency: 'SEK',
      annual_revenue: 1000000,
      employee_count: 10,
      sync_status: 'test'
    };

    const { error: insertError } = await supabase
      .from('companies')
      .insert(testData)
      .select();

    if (insertError) {
      console.log('Insert test error (this helps us understand the schema):', insertError);
      
      // Clean up any partial insert
      await supabase
        .from('companies')
        .delete()
        .eq('organization_number', 'TEST-SCHEMA');

      return {
        success: false,
        error: `Schema test failed: ${insertError.message}`
      };
    }

    // Clean up the test record
    await supabase
      .from('companies')
      .delete()
      .eq('organization_number', 'TEST-SCHEMA');

    console.log('Schema test passed - all fields are supported');

    return {
      success: true,
      columns: Object.keys(testData)
    };

  } catch (error) {
    console.error('Unexpected error in checkCompaniesTableSchema:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Clean up test companies
 */
export async function cleanupTestCompanies(): Promise<void> {
  try {
    console.log('Cleaning up test companies...');

    await supabase
      .from('companies')
      .delete()
      .in('organization_number', ['556123-4567', '556123-4568', 'TEST-SCHEMA']);

    console.log('Test companies cleaned up');
  } catch (error) {
    console.error('Error cleaning up test companies:', error);
  }
}
