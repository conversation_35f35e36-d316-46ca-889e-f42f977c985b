import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calculator, 
  TrendingUp, 
  TrendingDown, 
  DollarSign,
  RefreshCw,
  Target,
  Activity,
  Shield
} from 'lucide-react';
import { Slider } from '@/components/ui/slider';
import { RateFactors, calculateRateFactors, generateRateBreakdown } from '@/utils/rateCalculations';

interface RateSimulatorProps {
  currentFactors: RateFactors;
  onFactorsChange: (newFactors: RateFactors) => void;
}

const RateSimulator: React.FC<RateSimulatorProps> = ({ currentFactors, onFactorsChange }) => {
  const [simulatedFactors, setSimulatedFactors] = useState({
    liquidityScore: currentFactors.liquidityScore,
    workingCapitalScore: currentFactors.workingCapitalScore,
    profitabilityScore: currentFactors.profitabilityScore,
    riskScore: currentFactors.riskScore,
    complianceScore: currentFactors.complianceScore
  });

  const calculateSimulatedRate = () => {
    // Calculate weighted overall score
    const overallScore = (
      simulatedFactors.liquidityScore * 0.25 +
      simulatedFactors.workingCapitalScore * 0.20 +
      simulatedFactors.profitabilityScore * 0.25 +
      simulatedFactors.riskScore * 0.15 +
      simulatedFactors.complianceScore * 0.15
    );

    // Calculate rate impact based on score
    let rateImpact = 0;
    if (overallScore >= 90) rateImpact = -1.5;
    else if (overallScore >= 75) rateImpact = -0.75;
    else if (overallScore >= 60) rateImpact = 0;
    else if (overallScore >= 40) rateImpact = 1.0;
    else rateImpact = 2.5;

    const baseRate = 2.5;
    return baseRate + rateImpact;
  };

  const simulatedRate = calculateSimulatedRate();
  const rateDifference = simulatedRate - currentFactors.currentRate;
  const annualSavings = rateDifference * -5000000; // Assuming 5M loan

  const resetToOriginal = () => {
    setSimulatedFactors({
      liquidityScore: currentFactors.liquidityScore,
      workingCapitalScore: currentFactors.workingCapitalScore,
      profitabilityScore: currentFactors.profitabilityScore,
      riskScore: currentFactors.riskScore,
      complianceScore: currentFactors.complianceScore
    });
  };

  const applyChanges = () => {
    const newFactors = {
      ...currentFactors,
      ...simulatedFactors,
      overallScore: (
        simulatedFactors.liquidityScore * 0.25 +
        simulatedFactors.workingCapitalScore * 0.20 +
        simulatedFactors.profitabilityScore * 0.25 +
        simulatedFactors.riskScore * 0.15 +
        simulatedFactors.complianceScore * 0.15
      ),
      currentRate: simulatedRate,
      riskPremium: simulatedRate - 2.5
    };
    onFactorsChange(newFactors);
  };

  return (
    <div className="space-y-6">
      {/* Rate Impact Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calculator className="h-5 w-5 mr-2" />
            Interactive Rate Simulator
          </CardTitle>
          <p className="text-sm text-gray-600">
            Adjust your KPI scores to see real-time impact on your interest rate
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Current Rate</div>
              <div className="text-2xl font-bold text-blue-600">
                {currentFactors.currentRate.toFixed(2)}%
              </div>
            </div>
            
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Simulated Rate</div>
              <div className="text-2xl font-bold text-gray-900">
                {simulatedRate.toFixed(2)}%
              </div>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-1">Rate Difference</div>
              <div className={`text-2xl font-bold flex items-center justify-center ${rateDifference < 0 ? 'text-green-600' : 'text-red-600'}`}>
                {rateDifference < 0 ? <TrendingDown className="h-5 w-5 mr-1" /> : <TrendingUp className="h-5 w-5 mr-1" />}
                {rateDifference >= 0 ? '+' : ''}{rateDifference.toFixed(2)}%
              </div>
            </div>
          </div>

          {rateDifference !== 0 && (
            <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-yellow-800">
                    Annual Impact on 5M SEK Loan
                  </div>
                  <div className="text-sm text-yellow-600">
                    {rateDifference < 0 ? 'Potential savings' : 'Additional cost'}
                  </div>
                </div>
                <div className={`text-xl font-bold ${rateDifference < 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {annualSavings >= 0 ? '+' : ''}{(annualSavings / 1000).toFixed(0)}k SEK
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* KPI Sliders */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Liquidity Score */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-sm">
              <DollarSign className="h-4 w-4 mr-2" />
              Liquidity & Cash Flow Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Score</span>
                <Badge variant={simulatedFactors.liquidityScore >= 80 ? 'default' : simulatedFactors.liquidityScore >= 60 ? 'secondary' : 'destructive'}>
                  {simulatedFactors.liquidityScore.toFixed(0)}/100
                </Badge>
              </div>
              <Slider
                value={[simulatedFactors.liquidityScore]}
                onValueChange={(value) => setSimulatedFactors(prev => ({ ...prev, liquidityScore: value[0] }))}
                max={100}
                min={0}
                step={1}
                className="w-full"
              />
              <p className="text-xs text-gray-500">
                Improve by optimizing cash position and liquidity coverage
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Working Capital Score */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-sm">
              <Activity className="h-4 w-4 mr-2" />
              Working Capital Efficiency Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Score</span>
                <Badge variant={simulatedFactors.workingCapitalScore >= 80 ? 'default' : simulatedFactors.workingCapitalScore >= 60 ? 'secondary' : 'destructive'}>
                  {simulatedFactors.workingCapitalScore.toFixed(0)}/100
                </Badge>
              </div>
              <Slider
                value={[simulatedFactors.workingCapitalScore]}
                onValueChange={(value) => setSimulatedFactors(prev => ({ ...prev, workingCapitalScore: value[0] }))}
                max={100}
                min={0}
                step={1}
                className="w-full"
              />
              <p className="text-xs text-gray-500">
                Improve by reducing DSO and optimizing payment terms
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Profitability Score */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              Profitability & Growth Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Score</span>
                <Badge variant={simulatedFactors.profitabilityScore >= 80 ? 'default' : simulatedFactors.profitabilityScore >= 60 ? 'secondary' : 'destructive'}>
                  {simulatedFactors.profitabilityScore.toFixed(0)}/100
                </Badge>
              </div>
              <Slider
                value={[simulatedFactors.profitabilityScore]}
                onValueChange={(value) => setSimulatedFactors(prev => ({ ...prev, profitabilityScore: value[0] }))}
                max={100}
                min={0}
                step={1}
                className="w-full"
              />
              <p className="text-xs text-gray-500">
                Improve by increasing EBITDA margin and revenue growth
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Risk Score */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-sm">
              <Shield className="h-4 w-4 mr-2" />
              Risk Management Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Score</span>
                <Badge variant={simulatedFactors.riskScore >= 80 ? 'default' : simulatedFactors.riskScore >= 60 ? 'secondary' : 'destructive'}>
                  {simulatedFactors.riskScore.toFixed(0)}/100
                </Badge>
              </div>
              <Slider
                value={[simulatedFactors.riskScore]}
                onValueChange={(value) => setSimulatedFactors(prev => ({ ...prev, riskScore: value[0] }))}
                max={100}
                min={0}
                step={1}
                className="w-full"
              />
              <p className="text-xs text-gray-500">
                Improve by hedging FX exposure and reducing operational risks
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <Button variant="outline" onClick={resetToOriginal}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Reset to Current
        </Button>
        <Button onClick={applyChanges} disabled={rateDifference === 0}>
          <Target className="h-4 w-4 mr-2" />
          Apply Changes
        </Button>
      </div>
    </div>
  );
};

export default RateSimulator;
