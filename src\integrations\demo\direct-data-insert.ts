/**
 * Direct Data Insert for Nordström GreenTech AB
 * Bypasses the UI and directly inserts data into Supabase
 */

import { supabase } from '@/integrations/supabase/client';

// Simple company data
const nordstromCompany = {
  organization_number: '556123-4567',
  company_name: 'Nordström GreenTech AB',
  industry: 'Wind Turbine Components Manufacturing'
};

// Basic exchange rates
const basicExchangeRates = [
  { base_currency: 'SEK', target_currency: 'EUR', rate: 0.087, rate_date: '2024-01-15' },
  { base_currency: 'SEK', target_currency: 'USD', rate: 0.095, rate_date: '2024-01-15' },
  { base_currency: 'SEK', target_currency: 'NOK', rate: 1.05, rate_date: '2024-01-15' },
  { base_currency: 'EUR', target_currency: 'SEK', rate: 11.5, rate_date: '2024-01-15' },
  { base_currency: 'USD', target_currency: 'SEK', rate: 10.5, rate_date: '2024-01-15' },
  { base_currency: 'NOK', target_currency: 'SEK', rate: 0.95, rate_date: '2024-01-15' }
];

// Essential chart of accounts
const essentialAccounts = [
  { account_code: '1930', account_name: 'Handelsbanken Företagskonto', account_type: 'asset' },
  { account_code: '1931', account_name: 'SEB Valutakonto EUR', account_type: 'asset' },
  { account_code: '1510', account_name: 'Kundfordringar', account_type: 'asset' },
  { account_code: '1410', account_name: 'Råmaterial och förnödenheter', account_type: 'asset' },
  { account_code: '2010', account_name: 'Leverantörsskulder', account_type: 'liability' },
  { account_code: '2440', account_name: 'Utgående moms', account_type: 'liability' },
  { account_code: '2710', account_name: 'Personalskatt', account_type: 'liability' },
  { account_code: '3010', account_name: 'Försäljning inom Sverige', account_type: 'revenue' },
  { account_code: '3011', account_name: 'Försäljning inom EU', account_type: 'revenue' },
  { account_code: '3740', account_name: 'Valutakursvinster', account_type: 'revenue' },
  { account_code: '4010', account_name: 'Inköp av råmaterial', account_type: 'expense' },
  { account_code: '5010', account_name: 'Löner till arbetare', account_type: 'expense' },
  { account_code: '5410', account_name: 'Sociala avgifter', account_type: 'expense' },
  { account_code: '7740', account_name: 'Valutakursförluster', account_type: 'expense' }
];

// Sample vouchers
const sampleVouchers = [
  {
    voucher_series: 'KF',
    voucher_number: 1001,
    transaction_date: '2024-01-15',
    description: 'Kundfaktura - Norsk Vindkraft AS',
    total_amount: 2000000,
    currency_code: 'NOK',
    exchange_rate: 0.95,
    rows: [
      {
        account_code: '1510',
        account_name: 'Kundfordringar',
        debit_amount: 2000000,
        credit_amount: 0,
        currency_code: 'NOK',
        exchange_rate: 0.95,
        description: 'Norsk Vindkraft AS',
        row_number: 1
      },
      {
        account_code: '3011',
        account_name: 'Försäljning inom EU',
        debit_amount: 0,
        credit_amount: 2000000,
        currency_code: 'NOK',
        exchange_rate: 0.95,
        description: 'Turbinkomponenter',
        row_number: 2
      }
    ]
  },
  {
    voucher_series: 'LF',
    voucher_number: 2001,
    transaction_date: '2024-01-18',
    description: 'Leverantörsfaktura - ThyssenKrupp AG',
    total_amount: 87000,
    currency_code: 'EUR',
    exchange_rate: 11.5,
    rows: [
      {
        account_code: '4010',
        account_name: 'Inköp av råmaterial',
        debit_amount: 87000,
        credit_amount: 0,
        currency_code: 'EUR',
        exchange_rate: 11.5,
        description: 'Stål från Tyskland',
        row_number: 1
      },
      {
        account_code: '2010',
        account_name: 'Leverantörsskulder',
        debit_amount: 0,
        credit_amount: 87000,
        currency_code: 'EUR',
        exchange_rate: 11.5,
        description: 'ThyssenKrupp AG',
        row_number: 2
      }
    ]
  },
  {
    voucher_series: 'LÖ',
    voucher_number: 3001,
    transaction_date: '2024-01-25',
    description: 'Löner januari 2024',
    total_amount: 2800000,
    currency_code: 'SEK',
    exchange_rate: 1.0,
    rows: [
      {
        account_code: '5010',
        account_name: 'Löner till arbetare',
        debit_amount: 2800000,
        credit_amount: 0,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Bruttolöner januari',
        row_number: 1
      },
      {
        account_code: '5410',
        account_name: 'Sociala avgifter',
        debit_amount: 868000,
        credit_amount: 0,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Arbetsgivaravgifter 31%',
        row_number: 2
      },
      {
        account_code: '2710',
        account_name: 'Personalskatt',
        debit_amount: 0,
        credit_amount: 840000,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Preliminärskatt',
        row_number: 3
      },
      {
        account_code: '1930',
        account_name: 'Handelsbanken Företagskonto',
        debit_amount: 0,
        credit_amount: 2828000,
        currency_code: 'SEK',
        exchange_rate: 1.0,
        description: 'Nettolöner',
        row_number: 4
      }
    ]
  }
];

/**
 * Insert data directly into Supabase
 */
export async function insertDataDirectly(): Promise<void> {
  console.log('🚀 Starting direct data insertion...');

  try {
    // 1. Clean up any existing data first
    console.log('🧹 Cleaning up existing data...');
    await supabase.from('companies').delete().eq('organization_number', '556123-4567');

    // 2. Insert company
    console.log('🏢 Creating company...');
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .insert(nordstromCompany)
      .select()
      .single();

    if (companyError) {
      console.error('❌ Company creation failed:', companyError);
      throw new Error(`Company creation failed: ${companyError.message}`);
    }

    console.log('✅ Company created:', company.company_name, company.id);

    // 3. Insert exchange rates
    console.log('💱 Inserting exchange rates...');
    const { error: ratesError } = await supabase
      .from('exchange_rates')
      .upsert(basicExchangeRates, { onConflict: 'base_currency,target_currency,rate_date' });

    if (ratesError) {
      console.error('❌ Exchange rates failed:', ratesError);
    } else {
      console.log('✅ Exchange rates inserted');
    }

    // 4. Insert chart of accounts
    console.log('📊 Inserting chart of accounts...');
    const accountsWithCompanyId = essentialAccounts.map(account => ({
      ...account,
      company_id: company.id,
      is_active: true
    }));

    const { error: accountsError } = await supabase
      .from('chart_of_accounts')
      .insert(accountsWithCompanyId);

    if (accountsError) {
      console.error('❌ Chart of accounts failed:', accountsError);
    } else {
      console.log('✅ Chart of accounts inserted:', accountsWithCompanyId.length, 'accounts');
    }

    // 5. Insert sample vouchers
    console.log('📝 Inserting sample vouchers...');
    for (const voucherData of sampleVouchers) {
      try {
        // Insert voucher
        const { data: voucher, error: voucherError } = await supabase
          .from('vouchers')
          .insert({
            company_id: company.id,
            voucher_series: voucherData.voucher_series,
            voucher_number: voucherData.voucher_number,
            transaction_date: voucherData.transaction_date,
            description: voucherData.description,
            total_amount: voucherData.total_amount,
            currency_code: voucherData.currency_code,
            exchange_rate: voucherData.exchange_rate,
            sync_status: 'completed'
          })
          .select()
          .single();

        if (voucherError) {
          console.error(`❌ Voucher ${voucherData.voucher_series}-${voucherData.voucher_number} failed:`, voucherError);
          continue;
        }

        // Insert voucher rows
        const rowsWithVoucherId = voucherData.rows.map(row => ({
          ...row,
          voucher_id: voucher.id
        }));

        const { error: rowsError } = await supabase
          .from('voucher_rows')
          .insert(rowsWithVoucherId);

        if (rowsError) {
          console.error(`❌ Voucher rows for ${voucherData.voucher_series}-${voucherData.voucher_number} failed:`, rowsError);
        } else {
          console.log(`✅ Voucher ${voucherData.voucher_series}-${voucherData.voucher_number} inserted with ${voucherData.rows.length} rows`);
        }

      } catch (error) {
        console.error(`❌ Error processing voucher ${voucherData.voucher_series}-${voucherData.voucher_number}:`, error);
      }
    }

    console.log('🎉 Direct data insertion completed successfully!');
    console.log('📍 You should now see Nordström GreenTech AB in the dashboard');

  } catch (error) {
    console.error('💥 Direct data insertion failed:', error);
    throw error;
  }
}

// Auto-run the insertion when this module is imported
console.log('🔄 Auto-running direct data insertion...');
insertDataDirectly().catch(console.error);
